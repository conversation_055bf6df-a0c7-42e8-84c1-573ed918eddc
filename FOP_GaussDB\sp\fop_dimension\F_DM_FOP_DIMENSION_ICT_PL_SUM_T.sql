CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ICT_PL_SUM_T(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING, OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲ICT损益汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
		  参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ICT_PL_SUM_T();
*/
 
 DECLARE
	V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ICT_PL_SUM_T('''||P_VERSION_CODE||')';
	V_TBL_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ICT_PL_SUM_T';
	V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月_001...00N
	V_VERSION_MONTH VARCHAR(50);  -- 目标表的当前版本年月，格式：年月
	V_STEP_NUM   NUMERIC; --步骤号
	V_DML_ROW_COUNT  NUMBER DEFAULT 0 ;


BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';                                 --1表示成功
	

  
     -- 如果是传 VERSION_CODE 调函数取 传入的 P_VERSION_CODE ，如果是自动调度的 则从版本表取最新版本号
	 IF P_VERSION_CODE IS NOT NULL 
	 THEN
	 SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
	 ELSE 
	   -- 从版本表取最新版本号	    
	 SELECT VERSION_CODE INTO V_VERSION_CODE
	 FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
	 WHERE STEP = 1
	 ORDER BY LAST_UPDATE_DATE DESC
	 LIMIT 1
	 ;
	 END IF 
	 ;
		
			-- 当前版本所在的年月
		SELECT  SUBSTR(V_VERSION_CODE,1,6)  INTO V_VERSION_MONTH ;
		
		 --1.开始日志
  V_STEP_NUM := 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '盈利量纲ICT损益汇总表'||V_TBL_NAME||'，版本编码:'||V_VERSION_CODE||'，版本年月:'||V_VERSION_MONTH||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
  
 
	  
	   DROP TABLE IF EXISTS ICT_PL_TMP;
	   CREATE TEMPORARY TABLE  ICT_PL_TMP
		          AS
	   SELECT  T1.PERIOD_ID 										    -- 会计期
		      ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					     END) AS EQUIP_REV_RMB_AMT    -- 设备收入人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000' 
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_REV_USD_AMT     -- 设备收入美元金额     
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_RMB_AMT    -- 设备成本人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_USD_AMT    -- 设备成本美元金额     
		      ,T3.LV0_PROD_LIST_CODE    AS BG_CODE        			    -- BG编码
		      ,T3.LV0_PROD_LIST_CN_NAME AS BG_NAME     			        -- BG中文描述
		      ,T3.LV0_PROD_LIST_EN_NAME AS BG_EN_NAME                   -- BG英文描述
		      ,T3.LV0_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV0编码
		      ,T3.LV0_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV0中文描述
		      ,T3.LV0_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV0英文描述
		      ,T3.LV1_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV1_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV1中文描述
		      ,T3.LV1_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV1英文描述
		      ,T3.LV2_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV2_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV2中文描述
		      ,T3.LV2_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV2中文描述
		      ,T4.DOMESTIC_OR_OVERSEA_CODE                              --ICT国内或海外编码     
			  ,T4.DOMESTIC_OR_OVERSEA_CNAME   	                        -- ICT国内或海外中文名称 
 	    FROM FIN_DM_OPT_FOP.FOP_MR_DM_PS_FOR_OPT_PL_DTL_V T1		 	-- ICT损益表 
 	    LEFT JOIN DMDIM.DM_DIM_PRODUCT_D T3               			    ---产品维表
 	      ON T1.MAJOR_PROD_KEY = T3.PROD_KEY
       AND T3.DEL_FLAG = 'N'
 	    LEFT JOIN DMDIM.DM_DIM_REGION_RC_D T4             			    ---区域维表
 	      ON T1.GEO_PC_KEY = T4.GEO_PC_KEY
 	     AND T4.DEL_FLAG = 'N'
 	    JOIN DMDIM.DM_DIM_DATA_REP_CATEGORY_B T5    			        ---财经报告口径与数据口径关系维
        ON T1.DATA_CATEGORY_ID = T5.DATA_CATEGORY_ID
	     AND T5.REPORT_CATEGORY_ID = '11204'                            ---11204: 经营双算; 11202: 经营报告
 	    JOIN DMDIM.DM_DIM_REPORT_ITEM_LEVEL_D T8     			        ---报表项层级标准维
 	      ON T1.REPORT_ITEM_ID = T8.REPORT_ITEM_L5_ID
 	     AND T8.REPORT_ITEM_L1_CODE IN ('PS_PL_91000','PS_PL_91200')    -- '净销售收入','销售成本'
 	     AND T8.REPORT_ITEM_L2_CODE IN ('PS_PL_91050','PS_PL_91250')    -- '设备成本','设备收入'
 	     AND T8.USER_GROUP_ID = 14										---USER_GROUP_CN_NAME='产品与解决方案'
    WHERE T1.PERIOD_ID > 202312
        AND CAST(T1.PERIOD_ID AS INT) < CAST(V_VERSION_MONTH AS INT)   -- 取集成表PERIOD_ID<系统当前年月的数据
	    AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(TO_DATE(V_VERSION_MONTH,'YYYYMM'),-61),'YYYYMM') AS INT)   -- 取近5年数据
	   GROUP BY T1.PERIOD_ID 										    -- 会计期
			  ,T3.LV0_PROD_LIST_CODE                         		    -- BG编码
		      ,T3.LV0_PROD_LIST_CN_NAME                      	        -- BG中文描述
		      ,T3.LV0_PROD_LIST_EN_NAME                                 -- BG英文描述
		      ,T3.LV0_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV0编码
		      ,T3.LV0_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV0中文描述
		      ,T3.LV0_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV0英文描述
		      ,T3.LV1_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV1_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV1中文描述
		      ,T3.LV1_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV1英文描述
		      ,T3.LV2_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV2_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV2中文描述
		      ,T3.LV2_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV2中文描述
			  ,T4.DOMESTIC_OR_OVERSEA_CODE 
              ,T4.DOMESTIC_OR_OVERSEA_CNAME
			     UNION ALL
			   SELECT T1.PERIOD_ID										       -- 会计期
		       ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					     END) AS EQUIP_REV_RMB_AMT                             -- 设备收入人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000' 
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_REV_USD_AMT                              -- 设备收入美元金额     
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_RMB_AMT                             -- 设备成本人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_USD_AMT              -- 设备成本美元金额  
		       ,T3.LV0_PROD_LIST_CODE AS BG_CODE       			-- BG编码
		       ,T3.LV0_PROD_LIST_CN_NAME AS BG_NAME    			-- BG中文描述
		       ,T3.LV0_PROD_LIST_EN_NAME AS BG_EN_NAME          -- BG英文描述
		       ,T3.LV0_PROD_RND_TEAM_CODE	            		-- 重量级团队LV0编码
		       ,T3.LV0_PROD_RD_TEAM_CN_NAME	        		    -- 重量级团队LV0中文描述
		       ,T3.LV0_PROD_RD_TEAM_EN_NAME        			    -- 重量级团队LV0英文描述
		       ,T3.LV1_PROD_RND_TEAM_CODE	            		-- 重量级团队LV1编码
		       ,T3.LV1_PROD_RD_TEAM_CN_NAME	        			-- 重量级团队LV1中文描述
		       ,T3.LV1_PROD_RD_TEAM_EN_NAME	        			-- 重量级团队LV1英文描述
		       ,T3.LV2_PROD_RND_TEAM_CODE	            		-- 重量级团队LV1编码
		       ,T3.LV2_PROD_RD_TEAM_CN_NAME        			    -- 重量级团队LV2中文描述
		       ,T3.LV2_PROD_RD_TEAM_EN_NAME	        			-- 重量级团队LV2中文描述
		       ,T4.DOMESTIC_OR_OVERSEA_CODE                     --ICT国内或海外编码     
			   ,T4.DOMESTIC_OR_OVERSEA_CNAME   	                -- ICT国内或海外中文名称 		     
 	    FROM FIN_DM_OPT_FOP.FOP_MR_DM_PS_FOR_OPT_PL_DTL_V T1		 	    -- ICT损益（202305月版切的新表）
 	    LEFT JOIN DMDIM.DM_DIM_PRODUCT_D T3               			    ---产品维表
 	      ON T1.MAJOR_PROD_KEY = T3.PROD_KEY
       AND T3.DEL_FLAG = 'N'
 	    LEFT JOIN DMDIM.DM_DIM_REGION_RC_D T4             			    ---区域维表
 	      ON T1.GEO_PC_KEY = T4.GEO_PC_KEY
 	     AND T4.DEL_FLAG = 'N'
 	    JOIN DMDIM.DM_DIM_DATA_REP_CATEGORY_B T5    			    ---财经报告口径与数据口径关系维
        ON T1.DATA_CATEGORY_ID = T5.DATA_CATEGORY_ID
	     AND T5.REPORT_CATEGORY_ID = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    JOIN DMDIM.DM_DIM_REPORT_ITEM_LEVEL_D T8     			    ---报表项层级标准维
 	      ON T1.REPORT_ITEM_ID = T8.REPORT_ITEM_L5_ID
 	     AND T8.REPORT_ITEM_L1_CODE IN ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     AND T8.REPORT_ITEM_L2_CODE IN ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     AND T8.USER_GROUP_ID = 14														---USER_GROUP_CN_NAME='产品与解决方案'
     WHERE T1.PERIOD_ID BETWEEN 202301 AND 202312
	 AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(TO_DATE(V_VERSION_MONTH,'YYYYMM'),-61),'YYYYMM') AS INT)   -- 取近5年数据
       AND T3.LV0_PROD_LIST_CODE NOT IN ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
	    GROUP BY T1.PERIOD_ID 										    -- 会计期
			  ,T3.LV0_PROD_LIST_CODE                         		    -- BG编码
		      ,T3.LV0_PROD_LIST_CN_NAME                      	        -- BG中文描述
		      ,T3.LV0_PROD_LIST_EN_NAME                                 -- BG英文描述
		      ,T3.LV0_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV0编码
		      ,T3.LV0_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV0中文描述
		      ,T3.LV0_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV0英文描述
		      ,T3.LV1_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV1_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV1中文描述
		      ,T3.LV1_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV1英文描述
		      ,T3.LV2_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV2_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV2中文描述
		      ,T3.LV2_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV2中文描述
			  ,T4.DOMESTIC_OR_OVERSEA_CODE 
              ,T4.DOMESTIC_OR_OVERSEA_CNAME			  
	    UNION ALL
	    SELECT T1.PERIOD_ID										            -- 会计期
		      ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					     END) AS EQUIP_REV_RMB_AMT                             -- 设备收入人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91000' 
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91050'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_REV_USD_AMT                              -- 设备收入美元金额     
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.RMB_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_RMB_AMT                             -- 设备成本人民币金额   
              ,SUM(CASE WHEN T8.REPORT_ITEM_L1_CODE ='PS_PL_91200'
			             AND T8.REPORT_ITEM_L2_CODE ='PS_PL_91250'
					    THEN T1.USD_FACT_EX_RATE_PTD_AMT 
					    END) AS EQUIP_COST_USD_AMT              -- 设备成本美元金额  
		       ,T3.LV0_PROD_LIST_CODE AS BG_CODE       			-- BG编码
		       ,T3.LV0_PROD_LIST_CN_NAME AS BG_NAME   			-- BG中文描述
		       ,T3.LV0_PROD_LIST_EN_NAME AS BG_EN_NAME     -- BG英文描述 
		       ,T3.LV0_PROD_RND_TEAM_CODE	            			-- 重量级团队LV0编码
		       ,T3.LV0_PROD_RD_TEAM_CN_NAME	        			  -- 重量级团队LV0中文描述
		       ,T3.LV0_PROD_RD_TEAM_EN_NAME	        			  -- 重量级团队LV0英文描述
		       ,T3.LV1_PROD_RND_TEAM_CODE	            			-- 重量级团队LV1编码
		       ,T3.LV1_PROD_RD_TEAM_CN_NAME	        			  -- 重量级团队LV1中文描述
		       ,T3.LV1_PROD_RD_TEAM_EN_NAME	        			  -- 重量级团队LV1英文描述
		       ,T3.LV2_PROD_RND_TEAM_CODE	            			-- 重量级团队LV1编码
		       ,T3.LV2_PROD_RD_TEAM_CN_NAME	        			  -- 重量级团队LV2中文描述
		       ,T3.LV2_PROD_RD_TEAM_EN_NAME	        			  -- 重量级团队LV2中文描述
		       ,T4.DOMESTIC_OR_OVERSEA_CODE                     --ICT国内或海外编码     
			   ,T4.DOMESTIC_OR_OVERSEA_CNAME   	                -- ICT国内或海外中文名称
 	    FROM FIN_DM_OPT_FOP.FOP_DWR_FIN_RPT_ITEM_PS_FV T1		 	    -- ICT损益
 	    LEFT JOIN DMDIM.DM_DIM_PRODUCT_D T3               			    ---产品维表
 	      ON T1.MAJOR_PROD_KEY = T3.PROD_KEY
       AND T3.DEL_FLAG = 'N'
 	    LEFT JOIN DMDIM.DM_DIM_REGION_RC_D T4             			    ---区域维表
 	      ON T1.GEO_PC_KEY = T4.GEO_PC_KEY
 	     AND T4.DEL_FLAG = 'N'
 	    JOIN DMDIM.DM_DIM_DATA_REP_CATEGORY_B T5    			    ---财经报告口径与数据口径关系维
        ON T1.DATA_CATEGORY_ID = T5.DATA_CATEGORY_ID
	     AND T5.REPORT_CATEGORY_ID = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    JOIN DMDIM.DM_DIM_REPORT_ITEM_LEVEL_D T8     			    ---报表项层级标准维
 	      ON T1.REPORT_ITEM_ID = T8.REPORT_ITEM_L5_ID
 	     AND T8.REPORT_ITEM_L1_CODE IN ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     AND T8.REPORT_ITEM_L2_CODE IN ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     AND T8.USER_GROUP_ID = 14														---USER_GROUP_CN_NAME='产品与解决方案'
     WHERE T1.PERIOD_ID <= 202212    -- 20230426 UPDATE BY QWX1110218 2023年5月版切换新表，新表从202301开始取数 
	  AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(TO_DATE(V_VERSION_MONTH,'YYYYMM'),-61),'YYYYMM') AS INT)   -- 取近5年数据		
       AND T3.LV0_PROD_LIST_CODE NOT IN ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
	    GROUP BY T1.PERIOD_ID 										    -- 会计期
			  ,T3.LV0_PROD_LIST_CODE                         		    -- BG编码
		      ,T3.LV0_PROD_LIST_CN_NAME                      	        -- BG中文描述
		      ,T3.LV0_PROD_LIST_EN_NAME                                 -- BG英文描述
		      ,T3.LV0_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV0编码
		      ,T3.LV0_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV0中文描述
		      ,T3.LV0_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV0英文描述
		      ,T3.LV1_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV1_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV1中文描述
		      ,T3.LV1_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV1英文描述
		      ,T3.LV2_PROD_RND_TEAM_CODE 	            			    -- 重量级团队LV1编码
		      ,T3.LV2_PROD_RD_TEAM_CN_NAME 	        			        -- 重量级团队LV2中文描述
		      ,T3.LV2_PROD_RD_TEAM_EN_NAME 	        			        -- 重量级团队LV2中文描述
			  ,T4.DOMESTIC_OR_OVERSEA_CODE 
              ,T4.DOMESTIC_OR_OVERSEA_CNAME
			 ; 
			  
			   V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => 'ICT损益来源表逻辑处理：ICT_PL_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

          -- 币种列转行
			DROP TABLE IF EXISTS CURRENCY_TMP;
	        CREATE TEMPORARY TABLE  CURRENCY_TMP
		          AS
			 SELECT    PERIOD_ID 										     -- 会计期
		      , 'CNY' AS CURRENCY_CODE                               -- 币种
		      , EQUIP_REV_RMB_AMT   AS EQUIP_REV_CONS_AFTER_AMT      -- 设备收入(对价后)    
              , EQUIP_COST_RMB_AMT  AS EQUIP_COST_CONS_AFTER_AMT     -- 设备成本(对价后)     
		      , BG_CODE        	                                     -- BG编码
		      , BG_NAME     			                             -- BG中文描述
		      , BG_EN_NAME                                           -- BG英文描述
		      , LV0_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV0编码
		      , LV0_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV0中文描述
		      , LV0_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV0英文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE                              --ICT国内或海外编码     
			  , DOMESTIC_OR_OVERSEA_CNAME   	                        -- ICT国内或海外中文名称 
 	    FROM ICT_PL_TMP	 
        WHERE DOMESTIC_OR_OVERSEA_CODE IN ('GH0002','GH0003')	-- 只取 中国区 和 海外的数据，剔除其他的数据	
		  AND BG_CODE IN ('PDCG901159','PDCG901160')
		  AND LV1_PROD_RND_TEAM_CODE IN ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网
          UNION ALL		  
	  SELECT   PERIOD_ID 										     -- 会计期
		      , 'USD' AS CURRENCY_CODE                               -- 币种 
              , EQUIP_REV_USD_AMT   AS EQUIP_REV_CONS_AFTER_AMT      -- 设备收入(对价后)       
              , EQUIP_COST_USD_AMT  AS EQUIP_COST_CONS_AFTER_AMT     -- 设备成本(对价后)    
		      , BG_CODE        	                                     -- BG编码
		      , BG_NAME     			                             -- BG中文描述
		      , BG_EN_NAME                                           -- BG英文描述
		      , LV0_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV0编码
		      , LV0_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV0中文描述
		      , LV0_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV0英文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE                              --ICT国内或海外编码     
			  , DOMESTIC_OR_OVERSEA_CNAME   	                      -- ICT国内或海外中文名称 
 	    FROM ICT_PL_TMP	 
		WHERE DOMESTIC_OR_OVERSEA_CODE IN ('GH0002','GH0003')	-- 只取 中国区 和 海外的数据，剔除其他的数据
          AND BG_CODE IN ('PDCG901159','PDCG901160')	
          AND LV1_PROD_RND_TEAM_CODE IN ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网  
              ;	
         
		 
		  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '币种列转行：CURRENCY_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
               
			   DROP TABLE IF EXISTS OVERSEA_TMP;
	           CREATE TEMPORARY TABLE  OVERSEA_TMP
		          AS
				  -- 汇总到全球
			    SELECT    PERIOD_ID 								       -- 会计期
		      , CURRENCY_CODE                                              -- 币种
		      , SUM(EQUIP_REV_CONS_AFTER_AMT)  AS EQUIP_REV_CONS_AFTER_AMT  -- 设备收入(对价后)     
              , SUM(EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_CONS_AFTER_AMT -- 设备成本(对价后)      
		      , BG_CODE        	                                     -- BG编码
		      , BG_NAME     			                             -- BG中文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , 'GH0001' AS DOMESTIC_OR_OVERSEA_CODE                 --ICT国内或海外编码     
			  , '全球' AS DOMESTIC_OR_OVERSEA_CNAME   	             -- ICT国内或海外中文名称 
 	          FROM CURRENCY_TMP	             
			  GROUP BY PERIOD_ID 								
		      , CURRENCY_CODE                                   
		      , BG_CODE        	                                
		      , BG_NAME     			                                                                   			
		      , LV1_PROD_RND_TEAM_CODE 	            			
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			
		      , LV2_PROD_RND_TEAM_CODE 	            			
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			
		      , LV2_PROD_RD_TEAM_EN_NAME 	
                UNION ALL	
				-- 插入国内海外的数据
              SELECT     PERIOD_ID 										     -- 会计期
		      , CURRENCY_CODE                                        -- 币种 
              , EQUIP_REV_CONS_AFTER_AMT                             -- 设备收入(对价后)       
              , EQUIP_COST_CONS_AFTER_AMT                            -- 设备成本(对价后)    
		      , BG_CODE        	                                     -- BG编码
		      , BG_NAME     			                             -- BG中文描述                                         -- BG英文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE                              --ICT国内或海外编码     
			  , DOMESTIC_OR_OVERSEA_CNAME   	                      -- ICT国内或海外中文名称 
 	          FROM CURRENCY_TMP	             			  	  
              ;
			  
			  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '国内海外汇总到全球：OVERSEA_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
			  
			  -- 汇总到ICT
               DROP TABLE IF EXISTS BG_TMP;
	           CREATE TEMPORARY TABLE  BG_TMP
		          AS
			    SELECT    PERIOD_ID 								        -- 会计期
		      , CURRENCY_CODE                                               -- 币种
		      , SUM(EQUIP_REV_CONS_AFTER_AMT)  AS EQUIP_REV_CONS_AFTER_AMT  -- 设备收入(对价后)     
              , SUM(EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_CONS_AFTER_AMT -- 设备成本(对价后)      
		      , 'PROD0002' AS BG_CODE        	                                    -- BG编码
		      , 'ICT' AS BG_NAME     			                                -- BG中文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			            -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			            -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			            -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			            -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			            -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			            -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE                                    -- ICT国内或海外编码     
			  , DOMESTIC_OR_OVERSEA_CNAME   	                            -- ICT国内或海外中文名称 
 	          FROM OVERSEA_TMP	             
			  GROUP BY PERIOD_ID 								
		      , CURRENCY_CODE                                     			                                                                   			
		      , LV1_PROD_RND_TEAM_CODE 	            			
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			
		      , LV2_PROD_RND_TEAM_CODE 	            			
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			
		      , LV2_PROD_RD_TEAM_EN_NAME 	
              , DOMESTIC_OR_OVERSEA_CODE                                 
			  , DOMESTIC_OR_OVERSEA_CNAME  
                UNION ALL
               SELECT    PERIOD_ID 								        -- 会计期
		      , CURRENCY_CODE                                               -- 币种
		      , EQUIP_REV_CONS_AFTER_AMT  -- 设备收入(对价后)     
              , EQUIP_COST_CONS_AFTER_AMT -- 设备成本(对价后)      
		      , BG_CODE        	                                    -- BG编码
		      , BG_NAME     			                                -- BG中文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			            -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			            -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			            -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			            -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			            -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			            -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE                                    -- ICT国内或海外编码     
			  , DOMESTIC_OR_OVERSEA_CNAME   	                            -- ICT国内或海外中文名称 
 	          FROM OVERSEA_TMP	             			
              ;   

       V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '运营商政企汇总到ICT：BG_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;			  
			
         -- 所有时间维度
            DROP TABLE IF EXISTS ALL_TIME_TMP;
	        CREATE TEMPORARY TABLE  ALL_TIME_TMP
		          AS           
              SELECT TO_CHAR(GENERATE_SERIES,'YYYYMM')  AS PERIOD_ID
			        ,TO_CHAR(GENERATE_SERIES,'YYYY')    AS YEAR
              FROM GENERATE_SERIES('2020-01-01':: TIMESTAMP ,CURRENT_DATE::TIMESTAMP, '1 MONTH')
			  WHERE TO_CHAR(GENERATE_SERIES,'YYYYMM')<= (SELECT MAX(PERIOD_ID)  FROM ICT_PL_TMP  )
             ;		  
			 
			  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '所有时间：ALL_TIME_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;			
			 
			 
			 
			 -- 所有时间关联每年的所有维度
			 DROP TABLE IF EXISTS ALL_DIM_TMP;
	         CREATE TEMPORARY TABLE  ALL_DIM_TMP
		          AS
			  SELECT DISTINCT T1.PERIOD_ID                -- 会计期
			  , T1.YEAR                          -- 年份
		      , T2.CURRENCY_CODE                 -- 币种   
		      , T2.BG_CODE        	             -- BG编码
		      , T2.BG_NAME     			         -- BG中文描述
		      , T2.LV1_PROD_RND_TEAM_CODE 	     -- 重量级团队LV1编码
		      , T2.LV1_PROD_RD_TEAM_CN_NAME      -- 重量级团队LV1中文描述
		      , T2.LV1_PROD_RD_TEAM_EN_NAME      -- 重量级团队LV1英文描述
		      , T2.LV2_PROD_RND_TEAM_CODE 	     -- 重量级团队LV1编码
		      , T2.LV2_PROD_RD_TEAM_CN_NAME      -- 重量级团队LV2中文描述
		      , T2.LV2_PROD_RD_TEAM_EN_NAME      -- 重量级团队LV2中文描述
		      , T2.DOMESTIC_OR_OVERSEA_CODE      --ICT国内或海外编码     
			  , T2.DOMESTIC_OR_OVERSEA_CNAME     -- ICT国内或海外中文名称 
 	          FROM ALL_TIME_TMP T1
			  LEFT JOIN BG_TMP T2
 			  ON T1.YEAR= SUBSTR(T2.PERIOD_ID,1,4)
			  ;
			  
			  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '所有维度：ALL_DIM_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;			
			 
			  
			  -- 计算LV2层级的YTD
			  DROP TABLE IF EXISTS LV2_YTD_TMP;
	         CREATE TEMPORARY TABLE  LV2_YTD_TMP
		          AS
			  SELECT T1.PERIOD_ID 									     -- 会计期
			  , T1.YEAR                                                  -- 年份
		      , T1.CURRENCY_CODE                                         -- 币种    
		      , T1.BG_CODE        	                                     -- BG编码
		      , T1.BG_NAME     			                                 -- BG中文描述
		      , T1.LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , T1.LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , T1.LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , T1.LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , T1.LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , T1.LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , T1.DOMESTIC_OR_OVERSEA_CODE                              --ICT国内或海外编码     
			  , T1.DOMESTIC_OR_OVERSEA_CNAME   	                         -- ICT国内或海外中文名称 
			  ,SUM(COALESCE(T2.EQUIP_REV_CONS_AFTER_AMT,0)) OVER(PARTITION BY T1.YEAR                                                  
		                                                        , T1.CURRENCY_CODE                                        
		                                                        , T1.BG_CODE        	                                    
		                                                        , T1.BG_NAME     			                                                                         
		                                                        , T1.LV1_PROD_RND_TEAM_CODE 	            			    
		                                                        , T1.LV1_PROD_RD_TEAM_CN_NAME 	        			    
		                                                        , T1.LV1_PROD_RD_TEAM_EN_NAME 	        			    
		                                                        , T1.LV2_PROD_RND_TEAM_CODE 	            			    
		                                                        , T1.LV2_PROD_RD_TEAM_CN_NAME 	        			    
		                                                        , T1.LV2_PROD_RD_TEAM_EN_NAME 	        			    
		                                                        , T1.DOMESTIC_OR_OVERSEA_CODE                             
			                                                    , T1.DOMESTIC_OR_OVERSEA_CNAME   	                        
                                                           ORDER BY T1.PERIOD_ID
														   ) AS EQUIP_REV_CONS_AFTER_AMT
			  ,SUM(COALESCE(T2.EQUIP_COST_CONS_AFTER_AMT,0)) OVER(PARTITION BY T1.YEAR                                                  
		                                                        , T1.CURRENCY_CODE                                        
		                                                        , T1.BG_CODE        	                                    
		                                                        , T1.BG_NAME     			                                                                        
		                                                        , T1.LV1_PROD_RND_TEAM_CODE 	            			    
		                                                        , T1.LV1_PROD_RD_TEAM_CN_NAME 	        			    
		                                                        , T1.LV1_PROD_RD_TEAM_EN_NAME 	        			    
		                                                        , T1.LV2_PROD_RND_TEAM_CODE 	            			    
		                                                        , T1.LV2_PROD_RD_TEAM_CN_NAME 	        			    
		                                                        , T1.LV2_PROD_RD_TEAM_EN_NAME 	        			    
		                                                        , T1.DOMESTIC_OR_OVERSEA_CODE                             
			                                                    , T1.DOMESTIC_OR_OVERSEA_CNAME   	                        
                                                           ORDER BY T1.PERIOD_ID
														   ) AS  EQUIP_COST_CONS_AFTER_AMT                             
			  FROM ALL_DIM_TMP T1
			  LEFT JOIN BG_TMP T2
			  ON  T1.PERIOD_ID =T2.PERIOD_ID 				 					      
		      AND COALESCE(T1.CURRENCY_CODE            ,'SNULL')  = COALESCE(T2.CURRENCY_CODE             ,'SNULL')                            
		      AND COALESCE(T1.BG_CODE        	       ,'SNULL')  = COALESCE(T2.BG_CODE        	          ,'SNULL')                    
		      AND COALESCE(T1.BG_NAME     			   ,'SNULL')  = COALESCE(T2.BG_NAME     			  ,'SNULL')                                      			      
		      AND COALESCE(T1.LV1_PROD_RND_TEAM_CODE   ,'SNULL')  = COALESCE(T2.LV1_PROD_RND_TEAM_CODE 	  ,'SNULL')  			      
		      AND COALESCE(T1.LV1_PROD_RD_TEAM_CN_NAME ,'SNULL')  = COALESCE(T2.LV1_PROD_RD_TEAM_CN_NAME  ,'SNULL')  			      
		      AND COALESCE(T1.LV1_PROD_RD_TEAM_EN_NAME ,'SNULL')  = COALESCE(T2.LV1_PROD_RD_TEAM_EN_NAME  ,'SNULL')  			      
		      AND COALESCE(T1.LV2_PROD_RND_TEAM_CODE   ,'SNULL')  = COALESCE(T2.LV2_PROD_RND_TEAM_CODE 	  ,'SNULL')  			      
		      AND COALESCE(T1.LV2_PROD_RD_TEAM_CN_NAME ,'SNULL')  = COALESCE(T2.LV2_PROD_RD_TEAM_CN_NAME  ,'SNULL')  			      
		      AND COALESCE(T1.LV2_PROD_RD_TEAM_EN_NAME ,'SNULL')  = COALESCE(T2.LV2_PROD_RD_TEAM_EN_NAME  ,'SNULL')  			      
		      AND COALESCE(T1.DOMESTIC_OR_OVERSEA_CODE ,'SNULL')  = COALESCE(T2.DOMESTIC_OR_OVERSEA_CODE  ,'SNULL')                    
			  AND COALESCE(T1.DOMESTIC_OR_OVERSEA_CNAME,'SNULL')  = COALESCE(T2.DOMESTIC_OR_OVERSEA_CNAME ,'SNULL')
                    ;		

     V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '汇总LV2层级的YTD：LV2_YTD_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;								

             
	      -- 删除最大版本数据
      DELETE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ICT_PL_SUM_T WHERE VERSION_CODE = V_VERSION_CODE;
	  
	  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ICT_PL_SUM_T(
	   VERSION_CODE
     , PERIOD_ID
	 , TIME_WINDOW_CODE
     , CURRENCY_CODE                  
     , EQUIP_REV_CONS_AFTER_AMT
     , EQUIP_COST_CONS_AFTER_AMT
     , BG_CODE
     , BG_NAME
     , LV1_PROD_RND_TEAM_CODE
     , LV1_PROD_RD_TEAM_CN_NAME
     , LV1_PROD_RD_TEAM_EN_NAME
     , LV2_PROD_RND_TEAM_CODE
     , LV2_PROD_RD_TEAM_CN_NAME
     , LV2_PROD_RD_TEAM_EN_NAME
     , OVERSEA_CODE
     , OVERSEA_DESC
     , REMARK
     , CREATED_BY
     , CREATION_DATE
     , LAST_UPDATED_BY
     , LAST_UPDATE_DATE
     , DEL_FLAG
	  )
	  SELECT    V_VERSION_CODE AS VERSION_CODE                       -- 版本编码
	          , PERIOD_ID 										     -- 会计期
			  ,'YTD' AS TIME_WINDOW_CODE
		      , CURRENCY_CODE                               -- 币种
		      , EQUIP_REV_CONS_AFTER_AMT  -- 设备收入人民币金额     
              , EQUIP_COST_CONS_AFTER_AMT -- 设备成本人民币金额      
		      , BG_CODE        	                                     -- BG编码
		      , BG_NAME     			                             -- BG中文描述
		      , LV1_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV1_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV1中文描述
		      , LV1_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV1英文描述
		      , LV2_PROD_RND_TEAM_CODE 	            			     -- 重量级团队LV1编码
		      , LV2_PROD_RD_TEAM_CN_NAME 	        			     -- 重量级团队LV2中文描述
		      , LV2_PROD_RD_TEAM_EN_NAME 	        			     -- 重量级团队LV2中文描述
		      , DOMESTIC_OR_OVERSEA_CODE  AS OVERSEA_CODE            -- ICT国内或海外编码     
              ,CASE WHEN DOMESTIC_OR_OVERSEA_CODE	= 'GH0002'  
		       THEN '国内' 
			   ELSE DOMESTIC_OR_OVERSEA_CNAME
			   END AS OVERSEA_DESC                                     -- ICT国内或海外中文名称
			  ,'' AS REMARK
 	          , -1 AS CREATED_BY
 	          , CURRENT_TIMESTAMP AS CREATION_DATE
 	          , -1 AS LAST_UPDATED_BY
 	          , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	          , 'N' AS DEL_FLAG
 	    FROM LV2_YTD_TMP	
              ;			  
			  
			  
		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      		
			-- 写结束日志
	     V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '生成的版本编码:'||V_VERSION_CODE||'结果表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	--收集统计信息
	ANALYSE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ICT_PL_SUM_T;

--处理异常信息
	EXCEPTION
		WHEN OTHERS THEN
		
		 PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	X_SUCCESS_FLAG := 'FAIL';		        --2001表示失败

 
 
 
  END;
 $BODY$
 LANGUAGE PLPGSQL VOLATILE
  COST 100
