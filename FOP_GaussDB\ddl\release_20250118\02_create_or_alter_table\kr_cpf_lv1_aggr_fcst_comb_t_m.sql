-- ----------------------------
-- Table structure for kr_cpf_lv1_aggr_fcst_comb_t_m
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m";
CREATE TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m" (
  "period_id" varchar(50) COLLATE "pg_catalog"."default",
  "target_period" varchar(100) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "fcst_type" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_after_fcst" numeric(38,10),
  "mgp_rate_after_fcst" numeric(38,10),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "phase_date" varchar(60) COLLATE "pg_catalog"."default",
  "aggregate_flag" varchar(50) COLLATE "pg_catalog"."default",
  "combined_expert" varchar(500) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."target_period" IS '目标时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."lv1_code" IS 'LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."lv1_name" IS 'LV1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."lv2_code" IS 'LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."lv2_name" IS 'LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."fcst_type" IS '预测类型';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."equip_rev_after_fcst" IS '对价后设备收入预测_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."mgp_rate_after_fcst" IS '制毛率预测（对价后)_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."del_flag" IS '是否删除标识';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."phase_date" IS '期次';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."aggregate_flag" IS '汇聚标志（无效，全都是全球的）';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."combined_expert" IS '融合分析师';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m"."last_update_date" IS '修改时间';
COMMENT ON TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t_m" IS 'AI融合预测结果表（当前年月版本）';

