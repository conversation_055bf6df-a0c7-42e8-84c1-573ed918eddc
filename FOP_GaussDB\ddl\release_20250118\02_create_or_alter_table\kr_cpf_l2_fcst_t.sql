-- ----------------------------
-- Table structure for kr_cpf_l2_fcst_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."kr_cpf_l2_fcst_t";
CREATE TABLE "fin_dm_opt_fop"."kr_cpf_l2_fcst_t" (
  "period_id" numeric,
  "target_period" varchar(100) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_coefficient" numeric(38,10),
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_name" varchar(100) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "fcst_type" varchar(100) COLLATE "pg_catalog"."default",
  "rev_percent_fcst" numeric(38,10),
  "carryover_amount_fcst" numeric(38,10),
  "plan_qty" numeric(38,10),
  "equip_rev_before_fcst" numeric(38,10),
  "unit_price_fcst_conf" numeric(38,10),
  "unit_price_fcst" numeric(38,10),
  "unit_price_fcst_upper" numeric(38,10),
  "unit_price_fcst_lower" numeric(38,10),
  "unit_cost_fcst_conf" numeric(38,10),
  "unit_cost_fcst" numeric(38,10),
  "unit_cost_fcst_upper" numeric(38,10),
  "unit_cost_fcst_lower" numeric(38,10),
  "mgp_rate_before_fcst_conf" numeric(38,10),
  "mgp_rate_before_fcst" numeric(38,10),
  "mgp_rate_before_fcst_upper" numeric(38,10),
  "mgp_rate_before_fcst_lower" numeric(38,10),
  "unit_price_fcst_acc" numeric(38,10),
  "unit_cost_fcst_acc" numeric(38,10),
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8 DEFAULT (-1),
  "creation_date" timestamp(6) DEFAULT pg_systimestamp(),
  "last_updated_by" int8 DEFAULT (-1),
  "last_update_date" timestamp(6) DEFAULT pg_systimestamp(),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'N'::character varying,
  "phase_date" varchar(60) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."target_period" IS '预测时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."lv2_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."lv2_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."l1_coefficient" IS 'L1系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."fcst_type" IS '预测方法（分月法、年度法、年度平均法、时序法）';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."rev_percent_fcst" IS '收入占比预测';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."carryover_amount_fcst" IS '结转量预测';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."plan_qty" IS '发货量预测（sop）';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."equip_rev_before_fcst" IS '对价前设备收入额';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_price_fcst_conf" IS '单位价格预测_置信度';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_price_fcst" IS '单位价格预测_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_price_fcst_upper" IS '单位价格预测_上标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_price_fcst_lower" IS '单位价格预测_下标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_cost_fcst_conf" IS '单位成本预测_置信度';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_cost_fcst" IS '单位成本预测_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_cost_fcst_upper" IS '单位成本预测_上标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_cost_fcst_lower" IS '单位成本预测_下标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."mgp_rate_before_fcst_conf" IS '制毛率预测（对价前)_置信度';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."mgp_rate_before_fcst" IS '制毛率预测（对价前)_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."mgp_rate_before_fcst_upper" IS '制毛率预测（对价前)_上标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."mgp_rate_before_fcst_lower" IS '制毛率预测（对价前)_下标';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_price_fcst_acc" IS '单价预测准确率';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."unit_cost_fcst_acc" IS '单本预测准确率';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."articulation_flag" IS '勾稽方法标签（SCENO1、场景一 SCENO2、场景二 SCENO3、场景三）';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_l2_fcst_t"."phase_date" IS '计划期次';
COMMENT ON TABLE "fin_dm_opt_fop"."kr_cpf_l2_fcst_t" IS 'L2预测历史数';

