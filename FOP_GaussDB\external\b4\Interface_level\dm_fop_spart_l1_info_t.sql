-- Step 2：数据提取

标签名称：spart_detail_info_tmp   数据源：fin_dm_opt_fop_uat
select version_code
     , period_id
     , bg_name
     , bg_code
     , oversea_flag
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv1_prod_rnd_team_code as lv1_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , lv2_prod_rnd_team_code as lv2_code
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , ship_qty
     , spart_qty
     , equip_rev_rmb_amt
     , equip_cost_rmb_amt
     , rmb_revenue
     , rmb_cost
     , equip_rev_usd_amt
     , equip_cost_usd_amt
     , usd_revenue
     , usd_cost
     , articulation_flag
     , source_table
  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
 where upper(industry_type) = 'TGT'
   and version_code = '${V_MAX_VERSION_CODE}'
;

标签名称：l1_name_temp   数据源：fin_dm_opt_fop_uat
select distinct l1_name
  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
 where version_code = '${V_MAX_VERSION_CODE}'
   and l1_coefficient > 0
;

-- Step 3：SQL-Script

cache lazy table currency_union_temp
as
select version_code
     , cast(period_id as int) as period_id
     , bg_name
     , bg_code
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_name
     , lv1_code
     , lv2_name
     , lv2_code
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , 'CNY' as currency
     , sum(nvl(ship_qty,0)) as ship_qty                      /* 发货量（历史）      */
     , sum(nvl(spart_qty,0)) as spart_qty                    /* 收入量（历史）      */
     , sum(nvl(equip_rev_rmb_amt,0)) as equip_rev_amt        /* 设备收入金额        */
     , sum(nvl(equip_cost_rmb_amt,0)) as equip_cost_amt      /* 设备成本金额        */
     , sum(nvl(rmb_revenue,0)) as equip_rev_cons_before_amt  /* 设备收入额（对价前）*/
     , sum(nvl(rmb_cost,0)) as equip_cost_cons_before_amt    /* 设备成本额（对价前）*/
     , sum(nvl(equip_rev_rmb_amt,0)) as equip_rev_cons_after_amt   /* 设备收入额（对价后） */
     , sum(nvl(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt /* 设备成本 金额 对价后 */
     , articulation_flag
     , source_table
   from spart_detail_info_tmp
 group by version_code
     , period_id
     , bg_name
     , bg_code
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_name
     , lv1_code
     , lv2_name
     , lv2_code
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , articulation_flag
     , source_table
 union all
select version_code
     , cast(period_id as int) as period_id
     , bg_name
     , bg_code
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_name
     , lv1_code
     , lv2_name
     , lv2_code
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , 'USD' as currency
     , sum(nvl(ship_qty,0)) as ship_qty                      /* 发货量（历史）      */
     , sum(nvl(spart_qty,0)) as spart_qty                    /* 收入量（历史）      */
     , sum(nvl(equip_rev_usd_amt,0)) as equip_rev_amt        /* 设备收入金额        */
     , sum(nvl(equip_cost_usd_amt,0)) as equip_cost_amt      /* 设备成本金额        */
     , sum(nvl(usd_revenue,0)) as equip_rev_cons_before_amt  /* 设备收入额（对价前）*/
     , sum(nvl(usd_cost,0)) as equip_cost_cons_before_amt    /* 设备成本额（对价前）*/
     , sum(nvl(equip_rev_usd_amt,0)) as equip_rev_cons_after_amt   /* 设备收入额（对价后） */
     , sum(nvl(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt /* 设备成本 金额 对价后 */
     , articulation_flag
     , source_table
   from spart_detail_info_tmp
 group by version_code
     , period_id
     , bg_name
     , bg_code
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_name
     , lv1_code
     , lv2_name
     , lv2_code
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , articulation_flag
     , source_table
;

/*区域描述收敛为全球*/
cache lazy table oversea_desc_temp
as
select t1.version_code
     , t1.period_id
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , sum(t1.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(t1.equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(t1.equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(t1.equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.ship_qty
                else nvl(t1.l2_coefficient,0)*t1.ship_qty
           end) as ship_qty
     , sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.spart_qty
                else nvl(t1.l2_coefficient,0)*t1.spart_qty
           end) as spart_qty
     , t1.currency
     , t1.articulation_flag
  from currency_union_temp t1
  left join l1_name_temp t2
		on t1.l1_name = t2.l1_name
 where t1.oversea_desc in('国内','海外','其他','全球')    /*国内、海外、S&OP预测的全球*/
 group by t1.version_code
     , t1.period_id
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , t1.currency
     , t1.articulation_flag
 union all
select t1.version_code
     , t1.period_id
     , t1.bg_code
     , t1.bg_name
     , '全球' as oversea_desc  /*全球=中国区+海外+其他*/
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , sum(t1.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(t1.equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(t1.equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(t1.equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.ship_qty
                else nvl(t1.l2_coefficient,0)*t1.ship_qty
           end) as ship_qty
     , sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.spart_qty
                else nvl(t1.l2_coefficient,0)*t1.spart_qty
           end) as spart_qty
     , t1.currency
     , t1.articulation_flag
  from currency_union_temp t1
  left join l1_name_temp t2
	  on t1.l1_name = t2.l1_name
 where lower(t1.source_table) not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t')  /*排除S&OP表的国内、海外、全球数据*/
 group by t1.version_code
     , t1.period_id
     , t1.bg_code
     , t1.bg_name
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , t1.currency
     , t1.articulation_flag
;

cache lazy table  bg_name_temp
as
select version_code
     , period_id
     , 'PROD0002' as bg_code
     , 'ICT' as bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(ship_qty)  as ship_qty
     , sum(spart_qty) as spart_qty
     , currency
     , articulation_flag
  from oversea_desc_temp
 group by version_code
     , period_id
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , currency
     , articulation_flag
 union all
select version_code
     , period_id
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , ship_qty
     , spart_qty
     , currency
     , articulation_flag
  from oversea_desc_temp
;

/*计算场景一的 mca调整率、制毛调整率（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）*/
cache lazy table adjust_rate_sceno_tmp
as
select version_code
     , period_id
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , currency
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
  from bg_name_temp
 where articulation_flag = 'SCENO1'
 group by version_code
     , period_id
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , currency
;

/*计算制毛率、mca调整率、结转率、单位成本、单位价格*/
cache lazy table mgp_adjust_rate_t
as
select t1.version_code
     , t1.period_id
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , t1.currency
     , t1.ship_qty
     , t1.spart_qty
     , t1.equip_rev_cons_before_amt
     , t1.equip_cost_cons_before_amt
     , t1.equip_rev_cons_after_amt
     , t1.equip_cost_cons_after_amt
     , (case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 0
				     when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				     when nvl(t1.equip_rev_cons_after_amt,0) <> 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0
					   then 1 - t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
				     else null
			  end) as mgp_ratio  /*制毛率 = 1 - l1对价后成本金额/l1对价后收入金额*/
     , (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_rev_cons_after_amt,0) = 0 then 0
				                when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
				                else 1 - t2.equip_rev_cons_after_amt / t2.equip_rev_cons_before_amt
				           end)
			       when t1.articulation_flag in('SCENO2')
			       then (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_after_amt,0) = 0 then 0
				                when nvl(t1.equip_rev_cons_before_amt,0) = 0 then -999999
				                else 1 - t1.equip_rev_cons_after_amt / t1.equip_rev_cons_before_amt
				           end)
				     else null
			  end) as mca_adjust_ratio  /*mca调整率 = 1 – 对价后收入/对价前收入*/
     , (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_amt,0) <> 0 then t2.equip_cost_cons_before_amt  / t2.equip_rev_cons_before_amt
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt  / t1.equip_rev_cons_before_amt
					         end)
				     else null
			  end) as mgp_ratio_before  /*l1对价前成本金额/l1对价前收入金额*/
     , (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_amt,0) <> 0 then t2.equip_cost_cons_after_amt  / t2.equip_rev_cons_after_amt
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_amt,0) <> 0 then t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
						       end)
				      else null
				 end) as mgp_ratio_after  /*l1对价后成本金额/l1对价后收入金额*/
     , (case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 and t1.spart_qty = 0 then null
				     when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 then -999999
				     when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_qty / t1.ship_qty
				     else null
			  end) as carryover_ratio  /*结转率 = l1收入数量/l1发货数量*/
     , (case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				     when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				     when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt / t1.spart_qty
				     else null
			  end) as unit_cost	 /*单位成本 = l1对价前成本金额/l1收入数量*/
     , (case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				     when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				     when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_rev_cons_before_amt / t1.spart_qty
				     else null
			  end) as unit_price	/*单位价格 = l1对价前收入金额/l1收入数量*/
     , t1.articulation_flag
  from bg_name_temp t1
  left join adjust_rate_sceno_tmp t2
    on t1.version_code = t2.version_code
   and t1.period_id = t2.period_id
	 and t1.bg_code      = t2.bg_code
	 and t1.oversea_desc = t2.oversea_desc
	 and t1.lv1_code     = t2.lv1_code
	 and t1.currency     = t2.currency
;

/*数据入到 dm_fop_spart_l1_info_his_t 目标表*/
cache lazy table dm_fop_spart_l1_info_his_tmp
as
select version_code
     , period_id
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , currency
     , ship_qty
     , spart_qty
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , mgp_ratio
     , mca_adjust_ratio
     , (case when mgp_ratio_before <> -999999 and mgp_ratio_after <> -999999 then mgp_ratio_before - mgp_ratio_after
				     when mgp_ratio_before = -999999 and mgp_ratio_after = -999999 then 0
				     when mgp_ratio_before = -999999 or mgp_ratio_after = -999999  then -999999
			  end) as mgp_adjust_ratio  /*制毛调整率*/
     , carryover_ratio
     , unit_cost
     , unit_price
     , articulation_flag
     , '' as remark
     , '-1' as created_by
     , current_timestamp as creation_date
     , '-1' as last_updated_by
     , current_timestamp as last_update_date
     , 'N' as del_flag
  from mgp_adjust_rate_t
 where (ship_qty <> 0 or
   spart_qty <> 0 or
   equip_rev_cons_before_amt <> 0 or
   equip_cost_cons_before_amt <> 0 or
   equip_rev_cons_after_amt <> 0 or
   equip_cost_cons_after_amt <> 0 )
   and (l1_name is not null or l1_name <> '')
;

/*数据入到 dm_fop_spart_l1_info_t 目标表*/
select period_id
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , currency
     , ship_qty
     , spart_qty
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , mgp_ratio
     , mca_adjust_ratio
     , mgp_adjust_ratio
     , carryover_ratio
     , unit_cost
     , unit_price
     , articulation_flag
     , remark
     , created_by
     , creation_date
     , last_updated_by
     , last_update_date
     , del_flag
  from dm_fop_spart_l1_info_his_tmp
;

	  	-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_l1_info_t             模式：TRUNCATE_TABLE


数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_l1_info_his_t         模式：DELETE
                                              删除条件：version_code = '${V_L1_VERSION_CODE}'