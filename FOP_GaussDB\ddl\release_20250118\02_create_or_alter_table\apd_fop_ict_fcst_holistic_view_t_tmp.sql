-- ----------------------------
-- Table structure for apd_fop_ict_fcst_holistic_view_t_tmp
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t_tmp";
CREATE TABLE "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t_tmp" (
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv3_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv3_name" varchar(200) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "industry_type" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "analysis_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;

