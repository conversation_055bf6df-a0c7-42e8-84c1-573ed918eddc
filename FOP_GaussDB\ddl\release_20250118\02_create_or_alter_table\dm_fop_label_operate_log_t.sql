-- ----------------------------
-- Table structure for dm_fop_label_operate_log_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_label_operate_log_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_label_operate_log_t" (
  "object_id" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "method_name" varchar(500) COLLATE "pg_catalog"."default",
  "status" varchar(20) COLLATE "pg_catalog"."default",
  "count_num" int8,
  "upsert_num" int8,
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6)
)
;

