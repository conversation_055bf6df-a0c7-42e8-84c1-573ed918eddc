-- ----------------------------
-- Function structure for f_dm_fop_snop_forecasts_sum_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_snop_forecasts_sum_t"("p_period_begin" varchar, "p_period_end" varchar, "p_year" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_snop_forecasts_sum_t"(IN "p_period_begin" varchar=NULL::character varying, IN "p_period_end" varchar=NULL::character varying, IN "p_year" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-10-10
创建人  ：鲁广武  lwx1186472
更新时间：2023-02-06
背景描述：产品线S&OP预测汇总表数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period_begin)：传入会计期(年月)起始日期
		  参数二(p_period_end)：传入会计期(年月)截止日期
		  参数三(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_snop_forecasts_sum_t()
修改记录：2023-02-06 lwx1186472   新增会计期参数，新增版本字段
          传入会计期，有值：传入值格式：f_dm_fop_snop_forecasts_sum_t(20221205,20230116)
          传入会计期，无值：传入值格式：f_dm_fop_snop_forecasts_sum_t()
		      2023-04-19 lwx1186472   新增字段;修改为两个月期间(上月6号~下月6号);
		                        coa_no字段更名为prod_code;修改取最大版本逻辑
		      2023-6-6 qwx1110218 修改逻辑：LV1~LV3从产品维表取；新增直取 is_release_version_flag 字段
			  20230731 lwx1186472 上游集成表切换BCM的表,表名更改为 fop_dwk_grp_pln_pub_snop_product_i
			  20231220 qwx1110218 1月版新增S&OP预测国内、海外集成表，与全球的集成表不是同一张表，国内+海外不等于全球
			  20240417 qwx1110218 5月版修改：全球集成表取<2024年的数据；国内海外集成表取>=2024年，全球=国内+海外；
			  20240926 qwx1110218 24年9月版：由于国内海外集成表没有 is_release_version_flag 字段，所以根据上游提供的逻辑进行加工，主要提供给FOX下游使用；
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_snop_forecasts_sum_t('||p_period_begin||','||p_period_end||','||p_year||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t';
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月_V1...VN
	v_new_version_code varchar(50);  -- 新生成的版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;


begin
	set enable_force_vector_engine to on;

	x_success_flag := '1';        --1表示成功


	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as version_code into v_current_max_version_code from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取当前最大版本号


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '产品线S&OP预测汇总表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的最大版本编码:'||v_current_max_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


  -- 创建临时表
  drop table if exists sop_act_fcst_qty_i_tmp;
  create temporary table sop_act_fcst_qty_i_tmp(
          lv1_prod_rnd_team_code    varchar(200)
        , lv1_prod_rd_team_cn_name  varchar(600)
        , lv1_prod_rd_team_en_name  varchar(600)
        , lv2_prod_rnd_team_code    varchar(200)
        , lv2_prod_rd_team_cn_name  varchar(600)
        , lv2_prod_rd_team_en_name  varchar(600)
        , lv3_prod_rnd_team_code    varchar(200)
        , lv3_prod_rd_team_cn_name  varchar(600)
        , lv3_prod_rd_team_en_name  varchar(600)
        , bg_code       varchar(200)
        , bg_cn_name    varchar(600)
  )on commit preserve rows distribute by replication
  ;

	-- 创建 snop_forecasts_temp1 临时表，取最大版本的数据或者没有版本的全量数据
	drop table if exists snop_forecasts_temp1;
    create temporary table snop_forecasts_temp1
	(
	version_code varchar(100),								--版本
	month varchar(100) NOT NULL,                          		---会计期
	phase_date varchar(100),                              		---期次分区字段
	item_code varchar(400),                              		---ITEM编码
	plan_unit_quantity numeric(38,10),                             		---计划单元计划量
	unit varchar(1000),                                   		---单位
	quantity numeric(38,10),                                       		---SNOP数量
	prod_code varchar(500),                                 	---产品编码
	bg_code varchar(100),                                 		---BG编码
	bg_cn_name varchar(1000),                             		---BG中文名
	lst_lv0_prod_rnd_team_code varchar(50),              		---重量级团队LV0编码
	lst_lv0_prod_rd_team_cn_name varchar(600),           		---重量级团队LV0中文描述
	lst_lv0_prod_rd_team_en_name varchar(600),           		---重量级团队LV0英文描述
	lst_lv1_prod_rnd_team_code varchar(50),              		---重量级团队LV1编码
	lst_lv1_prod_rd_team_cn_name varchar(600),           	    ---重量级团队LV1中文描述
	lst_lv1_prod_rd_team_en_name varchar(600),           		---重量级团队LV1英文描述
	lst_lv2_prod_rnd_team_code varchar(50),              		---重量级团队LV2编码
	lst_lv2_prod_rd_team_cn_name varchar(600),           	    ---重量级团队LV2中文描述
	lst_lv2_prod_rd_team_en_name varchar(600),           		---重量级团队LV2英文描述
	lst_lv3_prod_rnd_team_code varchar(50),              		---重量级团队LV3编码
	lst_lv3_prod_rd_team_cn_name varchar(600),           		---重量级团队LV3中文描述
	lst_lv3_prod_rd_team_en_name varchar(600),           	    ---重量级团队LV3英文描述
	plan_type varchar(300),                                   ---计划类型
	plan_com_lv1 varchar(1000),                                ---一级计委包
	plan_com_lv2 varchar(1000),                                ---二级计委包
	plan_com_lv3 varchar(1000),                                ---三级计委包
	busi_lv4 varchar(1000),                                    ---四级计委包
	port_qty numeric,                                                   ---端口数
	prod_key numeric,                                                   ---产品KEY
	prod_cn_name varchar(600),                                ---产品名称
	prod_en_name varchar(600),                                ---产品英文描述
	bg_en_name varchar(1000),                                ---BG英文名称
	is_release_version_flag text,                      -- 是否发布版本（Y、N、空值）  --  20230617版本新增字段
	oversea_desc varchar(100)                          -- 区域（全球、国内、海外）  -- 1月版新增
	) on commit preserve rows distribute by replication;

  -- 从集成表中取LV1~LV3 入到临时表
   insert into sop_act_fcst_qty_i_tmp(
          lv1_prod_rnd_team_code
        , lv1_prod_rd_team_cn_name
        , lv1_prod_rd_team_en_name
        , lv2_prod_rnd_team_code
        , lv2_prod_rd_team_cn_name
        , lv2_prod_rd_team_en_name
        , lv3_prod_rnd_team_code
        , lv3_prod_rd_team_cn_name
        , lv3_prod_rd_team_en_name
        , bg_code
        , bg_cn_name
   )
   select distinct lv1_prod_rnd_team_code
        , lv1_prod_rd_team_cn_name
        , lv1_prod_rd_team_en_name
        , lv2_prod_rnd_team_code
        , lv2_prod_rd_team_cn_name
        , lv2_prod_rd_team_en_name
        , lv3_prod_rnd_team_code
        , lv3_prod_rd_team_cn_name
        , lv3_prod_rd_team_en_name
        , bg_code
        , bg_cn_name
     from fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i
    where measure_code = '滚动S&OP计划'
      and instr(nvl(lv1_prod_rnd_team_code,'SNULL'),'SNULL') = 0
  ;

    --参数一非空、参数二非空
  if((p_period_begin is not null or p_period_begin <> '') and (p_period_end is not null or p_period_end <> '')) then

	-- 传入参数格式
	 if(length(p_period_begin) <> 8 or length(p_period_end) <> 8) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数:'||p_period_begin||','||p_period_end||',传入参数格式有误，正确格式：yyyymmdd ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
	 end if;

      -- 从集成表取数入到临时表1
      insert into snop_forecasts_temp1(
             version_code                                   -- 版本
           , month								        		              -- 会计期
           , phase_date                                 		-- 期次分区字段
           , item_code                                  		-- ITEM编码
           , plan_unit_quantity                         		-- 计划单元计划量
           , unit                                       		-- 单位
           , prod_code                                     	-- 产品编码
           , bg_code                                    		-- BG编码
           , bg_cn_name	                                		-- BG中文名
           , lst_lv0_prod_rnd_team_code	                		-- 重量级团队LV0编码
           , lst_lv0_prod_rd_team_cn_name	            		  -- 重量级团队LV0中文描述
           , lst_lv0_prod_rd_team_en_name	            		  -- 重量级团队LV0英文描述
           , lst_lv1_prod_rnd_team_code	                		-- 重量级团队LV1编码
           , lst_lv1_prod_rd_team_cn_name	            		  -- 重量级团队LV1中文描述
           , lst_lv1_prod_rd_team_en_name	            		  -- 重量级团队LV1英文描述
           , lst_lv2_prod_rnd_team_code	                		-- 重量级团队LV1编码
           , lst_lv2_prod_rd_team_cn_name	            		  -- 重量级团队LV2中文描述
           , lst_lv2_prod_rd_team_en_name	            		  -- 重量级团队LV2中文描述
           , lst_lv3_prod_rnd_team_code	                		-- 重量级团队LV3编码
           , lst_lv3_prod_rd_team_cn_name	            		  -- 重量级团队LV3中文描述
           , lst_lv3_prod_rd_team_en_name	            		  -- 重量级团队LV3英文描述
           , quantity				                    		        -- SNOP数量
           , plan_type                                      -- 计划类型
           , plan_com_lv1                                   -- 一级计委包
           , plan_com_lv2                                   -- 二级计委包
           , plan_com_lv3                                   -- 三级计委包
           , busi_lv4                                       -- 四级计委包
           , port_qty                                       -- 端口数
           , prod_key                                       -- 产品KEY
           , prod_cn_name                                   -- 产品名称
           , prod_en_name                                   -- 产品英文描述
           , bg_en_name	                                    -- BG英文名称
           , is_release_version_flag                        -- 是否发布版本（Y、N、空值）  --  20230617版本新增字段
           , oversea_desc                                   -- 区域（全球、国内、海外）  -- 1月版新增
		  )
		  with sop_act_fcst_qty_i_lv1_tmp as(
		  select distinct lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv1_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv1_prod_rnd_team_code is not null or lv1_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_lv2_tmp as(
		  select distinct lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name, lv2_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv2_prod_rnd_team_code is not null or lv2_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_lv3_tmp as(
		  select distinct lv3_prod_rnd_team_code, lv3_prod_rd_team_cn_name, lv3_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv3_prod_rnd_team_code is not null or lv3_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_bg_tmp as(
		  select distinct bg_code, bg_cn_name
		    from sop_act_fcst_qty_i_tmp
		   where (bg_code is not null or bg_code <> '')
		  ),
		  sop_act_fcst_qty_i_all_tmp as(
		  select t1.month								     			      -- 会计期
           , replace(t1.period,'/','') as phase_date -- 期次分区字段
           , sum(t1.qty) as plan_unit_quantity      -- 计划量
           , t1.unit                                -- 单位
           , t5.bg_code                      -- BG编码
           , t5.bg_cn_name                   -- BG中文名
           , t2.lv1_prod_rnd_team_code   as lst_lv1_prod_rnd_team_code	     -- 重量级团队LV1编码
           , t2.lv1_prod_rd_team_cn_name as lst_lv1_prod_rd_team_cn_name	   -- 重量级团队LV1中文描述
           , t2.lv1_prod_rd_team_en_name as lst_lv1_prod_rd_team_en_name	   -- 重量级团队LV1英文描述
           , t3.lv2_prod_rnd_team_code   as lst_lv2_prod_rnd_team_code	     -- 重量级团队LV2编码
           , t3.lv2_prod_rd_team_cn_name as lst_lv2_prod_rd_team_cn_name	   -- 重量级团队LV2中文描述
           , t3.lv2_prod_rd_team_en_name as lst_lv2_prod_rd_team_en_name	   -- 重量级团队LV2中文描述
           , t4.lv3_prod_rnd_team_code   as lst_lv3_prod_rnd_team_code	     -- 重量级团队LV3编码
           , t4.lv3_prod_rd_team_cn_name as lst_lv3_prod_rd_team_cn_name	   -- 重量级团队LV3中文描述
           , t4.lv3_prod_rd_team_en_name as lst_lv3_prod_rd_team_en_name	   -- 重量级团队LV3英文描述
           , sum(t1.qty) as quantity				    		        -- SNOP数量
           , t1.plan_com_lv1                                -- 一级计委包
           , t1.plan_com_lv2                                -- 二级计委包
           , t1.plan_com_lv3                                -- 三级计委包
           , t1.attr4 as busi_lv4                           -- 四级计委包
           , t1.bg_en_name	                                -- BG英文名称
           , t1.ict_domestic_overseas_cn_name as oversea_desc
        from fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i t1    -- 新增国内海外集成表
        left join sop_act_fcst_qty_i_lv1_tmp t2
          on t1.lv1_prod_rd_team_cn_name = t2.lv1_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_lv2_tmp t3
          on t1.lv2_prod_rd_team_cn_name = t3.lv2_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_lv3_tmp t4
          on t1.lv3_prod_rd_team_cn_name = t4.lv3_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_bg_tmp t5
          on t1.bg_cn_name = t5.bg_cn_name
       where substr(t1.period,1,4) >= '2024'  -- 取>=2024年的数据
         and replace(t1.period,'/','') >= p_period_begin                         -- 起始日期
         and replace(t1.period,'/','') < p_period_end                            -- 截止日期
         and substring(t1.month,1,4) >= nvl(p_year,substring(p_period_end,1,4))  -- 取大于等于截止日期所在年
         and t1.measure_code = '滚动S&OP计划'
         and (instr(upper(t1.plan_com_lv1),'NA') = 0 or instr(upper(t1.plan_com_lv3),'NA') = 0 or instr(upper(t1.plan_com_lv3),'NA') = 0)
       group by t1.month
           , t1.period
           , t1.unit
           , t5.bg_code
           , t5.bg_cn_name
           , t2.lv1_prod_rnd_team_code
           , t2.lv1_prod_rd_team_cn_name
           , t2.lv1_prod_rd_team_en_name
           , t3.lv2_prod_rnd_team_code
           , t3.lv2_prod_rd_team_cn_name
           , t3.lv2_prod_rd_team_en_name
           , t4.lv3_prod_rnd_team_code
           , t4.lv3_prod_rd_team_cn_name
           , t4.lv3_prod_rd_team_en_name
           , t1.plan_com_lv1
           , t1.plan_com_lv2
           , t1.plan_com_lv3
           , t1.attr4
           , t1.bg_en_name
           , t1.ict_domestic_overseas_cn_name
		  ),
		  -- 全球
		  sop_act_fcst_qty_i_all_tmp2 as(
	    select month								     			        -- 会计期
           , phase_date                             -- 期次分区字段
           , sum(plan_unit_quantity) as plan_unit_quantity -- 计划量
           , unit                                   -- 单位
           , bg_code                                -- BG编码
           , bg_cn_name                             -- BG中文名
           , lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , sum(quantity) as quantity              -- SNOP数量
           , plan_com_lv1                           -- 一级计委包
           , plan_com_lv2                           -- 二级计委包
           , plan_com_lv3                           -- 三级计委包
           , busi_lv4                               -- 四级计委包
           , bg_en_name	                            -- BG英文名称
           , '全球' as oversea_desc
        from sop_act_fcst_qty_i_all_tmp    -- 国内海外集成临时表
       where oversea_desc in('国内','海外','中国区')
       group by month
           , phase_date
           , unit
           , bg_code
           , bg_cn_name
           , lst_lv1_prod_rnd_team_code
           , lst_lv1_prod_rd_team_cn_name
           , lst_lv1_prod_rd_team_en_name
           , lst_lv2_prod_rnd_team_code
           , lst_lv2_prod_rd_team_cn_name
           , lst_lv2_prod_rd_team_en_name
           , lst_lv3_prod_rnd_team_code
           , lst_lv3_prod_rd_team_cn_name
           , lst_lv3_prod_rd_team_en_name
           , plan_com_lv1
           , plan_com_lv2
           , plan_com_lv3
           , busi_lv4
           , bg_en_name
		  ),
		  -- 取全球的最小期次
		  min_phase_date_tmp as(
		  select min(phase_date) as min_phase_date
		    from sop_act_fcst_qty_i_all_tmp2
		  )
		  select '' as version_code                                -- 版本
            , t1.month								     			               -- 会计期
            , t1.phase_date                              			 -- 期次分区字段
            , t1.item_code                               			 -- ITEM编码
            , sum(t1.plan_unit_quantity) as plan_unit_quantity -- 计划单元计划量
            , t1.unit                                    			 -- 单位
            , t1.coa_no as prod_code                           -- 产品编码
            , t1.bg_code                                 			 -- BG编码
            , t1.bg_cn_name	                             			 -- BG中文名
            , t2.lv0_prod_rnd_team_code   as lst_lv0_prod_rnd_team_code	     -- 重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
            , t2.lv0_prod_rd_team_cn_name as lst_lv0_prod_rd_team_cn_name	   -- 重量级团队LV0中文描述
            , t2.lv0_prod_rd_team_en_name as lst_lv0_prod_rd_team_en_name	   -- 重量级团队LV0英文描述
            , t2.lv1_prod_rnd_team_code   as lst_lv1_prod_rnd_team_code	     -- 重量级团队LV1编码
            , t2.lv1_prod_rd_team_cn_name as lst_lv1_prod_rd_team_cn_name	   -- 重量级团队LV1中文描述
            , t2.lv1_prod_rd_team_en_name as lst_lv1_prod_rd_team_en_name	   -- 重量级团队LV1英文描述
            , t2.lv2_prod_rnd_team_code   as lst_lv2_prod_rnd_team_code	     -- 重量级团队LV2编码
            , t2.lv2_prod_rd_team_cn_name as lst_lv2_prod_rd_team_cn_name	   -- 重量级团队LV2中文描述
            , t2.lv2_prod_rd_team_en_name as lst_lv2_prod_rd_team_en_name	   -- 重量级团队LV2中文描述
            , t2.lv3_prod_rnd_team_code   as lst_lv3_prod_rnd_team_code	     -- 重量级团队LV3编码
            , t2.lv3_prod_rd_team_cn_name as lst_lv3_prod_rd_team_cn_name	   -- 重量级团队LV3中文描述
            , t2.lv3_prod_rd_team_en_name as lst_lv3_prod_rd_team_en_name	   -- 重量级团队LV3英文描述
            , sum(t1.quantity) as quantity				    		     -- SNOP数量
            , t1.plan_type                                     -- 计划类型
            , t1.plan_com_lv1                                  -- 一级计委包
            , t1.plan_com_lv2                                  -- 二级计委包
            , t1.plan_com_lv3                                  -- 三级计委包
            , t1.attr4 as busi_lv4                             -- 四级计委包
            , t1.port_qty                                      -- 端口数
            , t1.prod_key                                      -- 产品KEY
            , t2.prod_cn_name                                  -- 产品名称
            , t2.prod_en_name                                  -- 产品英文描述
            , t2.lv0_prod_list_en_name as bg_en_name	         -- BG英文名称
            , t1.is_release_version_flag
            , '全球' as oversea_desc
		     -- from fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i t1           -- 产品线S&OP预测（原始表）
		     from fin_dm_opt_fop.fop_dwk_grp_pln_pub_snop_product_i t1      -- 20230731 切换BCM的表
         left join dmdim.dm_dim_product_d t2  -- 产品维表
	   	     on t1.prod_key = t2.prod_key
			    --and t2.scd_active_ind = 1
	   	    and t2.del_flag = 'N'
	      where substr(t1.phase_date,1,4) < '2024'  -- 只取2024年之前的数据
	        and t1.phase_date >= p_period_begin                      -- 起始日期
	        and t1.phase_date < p_period_end                         -- 截止日期
	        and substring(t1.month,1,4) >= nvl(p_year,substring(p_period_end,1,4))   -- 取大于等于截止日期所在年
          and upper(t1.measure_code) = 'ADJUST HQ SNOP'
          and upper(t1.site_code) <> 'GLOBAL'
          and upper(t1.area_type)='HQ_M'
          and t1.del_flag = 'N'
        group by t1.month
            , t1.phase_date
            , t1.item_code
            , t1.unit
            , t1.coa_no
            , t1.bg_code
            , t1.bg_cn_name
            , t2.lv0_prod_rnd_team_code
            , t2.lv0_prod_rd_team_cn_name
            , t2.lv0_prod_rd_team_en_name
            , t2.lv1_prod_rnd_team_code
            , t2.lv1_prod_rd_team_cn_name
            , t2.lv1_prod_rd_team_en_name
            , t2.lv2_prod_rnd_team_code
            , t2.lv2_prod_rd_team_cn_name
            , t2.lv2_prod_rd_team_en_name
            , t2.lv3_prod_rnd_team_code
            , t2.lv3_prod_rd_team_cn_name
            , t2.lv3_prod_rd_team_en_name
            , t1.plan_type
            , t1.plan_com_lv1
            , t1.plan_com_lv2
            , t1.plan_com_lv3
            , t1.attr4
            , t1.port_qty
            , t1.prod_key
            , t2.prod_cn_name
            , t2.prod_en_name
            , t2.lv0_prod_list_en_name
            , t1.is_release_version_flag
	    union all
	    -- S&OP预测国内海外集成表，包括全球、国内、海外的数据，>=2024年的全球=国内+海外
	    select '' as version_code                     -- 版本
           , month								     			        -- 会计期
           , phase_date                             -- 期次分区字段
           , '' as item_code                        -- ITEM编码
           , plan_unit_quantity                     -- 计划量
           , unit                                   -- 单位
           , '' as prod_code                        -- 产品编码
           , bg_code                                -- BG编码
           , bg_cn_name                             -- BG中文名
           , '' as lst_lv0_prod_rnd_team_code	      -- 重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
           , '' as lst_lv0_prod_rd_team_cn_name	    -- 重量级团队LV0中文描述
           , '' as lst_lv0_prod_rd_team_en_name	    -- 重量级团队LV0英文描述
           , lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , quantity				    		                -- SNOP数量
           , '' as plan_type                        -- 计划类型
           , plan_com_lv1                           -- 一级计委包
           , plan_com_lv2                           -- 二级计委包
           , plan_com_lv3                           -- 三级计委包
           , busi_lv4                               -- 四级计委包
           , null as port_qty                       -- 端口数
           , null as prod_key                       -- 产品KEY
           , '' as prod_cn_name                     -- 产品名称
           , '' as prod_en_name                     -- 产品英文描述
           , bg_en_name	                            -- BG英文名称
           , '' as is_release_version_flag
           , oversea_desc
        from sop_act_fcst_qty_i_all_tmp    -- 国内海外集成临时表
	     union all
	    select '' as version_code                     -- 版本
           , t1.month								     			        -- 会计期
           , t1.phase_date                             -- 期次分区字段
           , '' as item_code                        -- ITEM编码
           , t1.plan_unit_quantity                     -- 计划量
           , t1.unit                                   -- 单位
           , '' as prod_code                        -- 产品编码
           , t1.bg_code                                -- BG编码
           , t1.bg_cn_name                             -- BG中文名
           , '' as lst_lv0_prod_rnd_team_code	      -- 重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
           , '' as lst_lv0_prod_rd_team_cn_name	    -- 重量级团队LV0中文描述
           , '' as lst_lv0_prod_rd_team_en_name	    -- 重量级团队LV0英文描述
           , t1.lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , t1.lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , t1.lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , t1.lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , t1.lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , t1.lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , t1.lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , t1.lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , t1.lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , t1.quantity                               -- SNOP数量
           , '' as plan_type                        -- 计划类型
           , t1.plan_com_lv1                           -- 一级计委包
           , t1.plan_com_lv2                           -- 二级计委包
           , t1.plan_com_lv3                           -- 三级计委包
           , t1.busi_lv4                               -- 四级计委包
           , null as port_qty                       -- 端口数
           , null as prod_key                       -- 产品KEY
           , '' as prod_cn_name                     -- 产品名称
           , '' as prod_en_name                     -- 产品英文描述
           , t1.bg_en_name	                            -- BG英文名称
           , (case when t1.phase_date = min(t1.phase_date) over(partition by to_char(to_date(t1.phase_date,'YYYYMMDD')+3,'YYYYMM') order by t1.phase_date) 
                    and t1.phase_date = t2.min_phase_date
                    and substr(t2.min_phase_date,7) < 6 
                   then 'Y'
                   when t1.phase_date = min(t1.phase_date) over(partition by to_char(to_date(t1.phase_date,'YYYYMMDD')+3,'YYYYMM') order by t1.phase_date) 
                    and t1.phase_date <> t2.min_phase_date
                   then 'Y'
                   else 'N'
              end) as is_release_version_flag
           , t1.oversea_desc
        from sop_act_fcst_qty_i_all_tmp2 t1    -- 全球临时表
        left join min_phase_date_tmp t2
          on 1=1
	    ;

      v_dml_row_count := sql%rowcount;

  	  -- 记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期:'||p_period_begin||','||p_period_end||',snop_forecasts_temp1 临时表的数据量:'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;

	-- 传入参数一空，传入参数二非空
elseif((p_period_begin is null or p_period_begin = '') and (p_period_end is not null or p_period_end <> '')) then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数:'||p_period_begin||',传入参数格式有误，正确格式：yyyymmdd ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;

	-- 传入参数一非空，传入参数二空
elseif((p_period_begin is not null or p_period_begin <> '') and (p_period_end is null or p_period_end = '')) then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数:'||p_period_end||',传入参数格式有误，正确格式：yyyymmdd ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
	-- 传入参数一空，传入参数二空
	--取上个月6号到这个月6号的数据，左闭右开
  else

         -- 从集成表取数入到临时表1
    insert into snop_forecasts_temp1
				(
				 version_code,                                      --版本
				 month,								        		---会计期
				 phase_date,                                 		---期次分区字段
				 item_code,                                  		---ITEM编码
				 plan_unit_quantity,                         		---计划单元计划量
				 unit,                                       		---单位
				 prod_code,                                     		---产品编码
				 bg_code,                                    		---BG编码
				 bg_cn_name,	                                		---BG中文名
				 lst_lv0_prod_rnd_team_code,	                		---重量级团队LV0编码
				 lst_lv0_prod_rd_team_cn_name,	            		  ---重量级团队LV0中文描述
				 lst_lv0_prod_rd_team_en_name,	            		  ---重量级团队LV0英文描述
				 lst_lv1_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv1_prod_rd_team_cn_name,	            		  ---重量级团队LV1中文描述
				 lst_lv1_prod_rd_team_en_name,	            		  ---重量级团队LV1英文描述
				 lst_lv2_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv2_prod_rd_team_cn_name,	            		  ---重量级团队LV2中文描述
				 lst_lv2_prod_rd_team_en_name,	            		  ---重量级团队LV2中文描述
				 lst_lv3_prod_rnd_team_code,	                		---重量级团队LV3编码
				 lst_lv3_prod_rd_team_cn_name,	            		  ---重量级团队LV3中文描述
				 lst_lv3_prod_rd_team_en_name,	            		  ---重量级团队LV3英文描述
				 quantity,				                    		        ---SNOP数量
				 plan_type,                                      ---计划类型
                 plan_com_lv1,                                   ---一级计委包
                 plan_com_lv2,                                   ---二级计委包
                 plan_com_lv3,                                   ---三级计委包
                 busi_lv4,                                       ---四级计委包
                 port_qty,                                       ---端口数
				 prod_key,                                       ---产品KEY
				 prod_cn_name,                                   ---产品名称
				 prod_en_name,                                   ---产品英文描述
				 bg_en_name,                                     ---BG英文名称
				 is_release_version_flag,                      -- 是否发布版本（Y、N、空值）  --  20230617版本新增字段
				 oversea_desc
			)
		  with sop_act_fcst_qty_i_lv1_tmp as(
		  select distinct lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv1_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv1_prod_rnd_team_code is not null or lv1_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_lv2_tmp as(
		  select distinct lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name, lv2_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv2_prod_rnd_team_code is not null or lv2_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_lv3_tmp as(
		  select distinct lv3_prod_rnd_team_code, lv3_prod_rd_team_cn_name, lv3_prod_rd_team_en_name
		    from sop_act_fcst_qty_i_tmp
		   where (lv3_prod_rnd_team_code is not null or lv3_prod_rnd_team_code <> '')
		  ),
		  sop_act_fcst_qty_i_bg_tmp as(
		  select distinct bg_code, bg_cn_name
		    from sop_act_fcst_qty_i_tmp
		   where (bg_code is not null or bg_code <> '')
		  ),
		  sop_act_fcst_qty_i_all_tmp as(
		  select t1.month								     			      -- 会计期
           , replace(t1.period,'/','') as phase_date -- 期次分区字段
           , sum(t1.qty) as plan_unit_quantity      -- 计划量
           , t1.unit                                -- 单位
           , t5.bg_code                      -- BG编码
           , t5.bg_cn_name                   -- BG中文名
           , t2.lv1_prod_rnd_team_code   as lst_lv1_prod_rnd_team_code	     -- 重量级团队LV1编码
           , t2.lv1_prod_rd_team_cn_name as lst_lv1_prod_rd_team_cn_name	   -- 重量级团队LV1中文描述
           , t2.lv1_prod_rd_team_en_name as lst_lv1_prod_rd_team_en_name	   -- 重量级团队LV1英文描述
           , t3.lv2_prod_rnd_team_code   as lst_lv2_prod_rnd_team_code	     -- 重量级团队LV2编码
           , t3.lv2_prod_rd_team_cn_name as lst_lv2_prod_rd_team_cn_name	   -- 重量级团队LV2中文描述
           , t3.lv2_prod_rd_team_en_name as lst_lv2_prod_rd_team_en_name	   -- 重量级团队LV2中文描述
           , t4.lv3_prod_rnd_team_code   as lst_lv3_prod_rnd_team_code	     -- 重量级团队LV3编码
           , t4.lv3_prod_rd_team_cn_name as lst_lv3_prod_rd_team_cn_name	   -- 重量级团队LV3中文描述
           , t4.lv3_prod_rd_team_en_name as lst_lv3_prod_rd_team_en_name	   -- 重量级团队LV3英文描述
           , sum(t1.qty) as quantity				    		        -- SNOP数量
           , t1.plan_com_lv1                                -- 一级计委包
           , t1.plan_com_lv2                                -- 二级计委包
           , t1.plan_com_lv3                                -- 三级计委包
           , t1.attr4 as busi_lv4                           -- 四级计委包
           , t1.bg_en_name	                                -- BG英文名称
           , t1.ict_domestic_overseas_cn_name as oversea_desc
        from fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i t1    -- 新增国内海外集成表
        left join sop_act_fcst_qty_i_lv1_tmp t2
          on t1.lv1_prod_rd_team_cn_name = t2.lv1_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_lv2_tmp t3
          on t1.lv2_prod_rd_team_cn_name = t3.lv2_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_lv3_tmp t4
          on t1.lv3_prod_rd_team_cn_name = t4.lv3_prod_rd_team_cn_name
        left join sop_act_fcst_qty_i_bg_tmp t5
          on t1.bg_cn_name = t5.bg_cn_name
       where substr(t1.period,1,4) >= '2024'  -- 取>=2024年的数据
         and replace(t1.period,'/','') >= to_char(add_months(current_date,-1),'yyyymm')||'06'   --上个月6号
         and replace(t1.period,'/','') < to_char(add_months(current_date,1),'yyyymm')||'06'     --下个月6号
         and substring(t1.month,1,4) >= to_char(current_date,'yyyy')        --取年份大于等于当年
         and t1.measure_code = '滚动S&OP计划'
         and (instr(upper(t1.plan_com_lv1),'NA') = 0 or instr(upper(t1.plan_com_lv3),'NA') = 0 or instr(upper(t1.plan_com_lv3),'NA') = 0)
       group by t1.month
           , t1.period
           , t1.unit
           , t5.bg_code
           , t5.bg_cn_name
           , t2.lv1_prod_rnd_team_code
           , t2.lv1_prod_rd_team_cn_name
           , t2.lv1_prod_rd_team_en_name
           , t3.lv2_prod_rnd_team_code
           , t3.lv2_prod_rd_team_cn_name
           , t3.lv2_prod_rd_team_en_name
           , t4.lv3_prod_rnd_team_code
           , t4.lv3_prod_rd_team_cn_name
           , t4.lv3_prod_rd_team_en_name
           , t1.plan_com_lv1
           , t1.plan_com_lv2
           , t1.plan_com_lv3
           , t1.attr4
           , t1.bg_en_name
           , t1.ict_domestic_overseas_cn_name
		  ),
		  -- 全球
		  sop_act_fcst_qty_i_all_tmp2 as(
	    select month								     			        -- 会计期
           , phase_date                             -- 期次分区字段
           , sum(plan_unit_quantity) as plan_unit_quantity -- 计划量
           , unit                                   -- 单位
           , bg_code                                -- BG编码
           , bg_cn_name                             -- BG中文名
           , lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , sum(quantity) as quantity              -- SNOP数量
           , plan_com_lv1                           -- 一级计委包
           , plan_com_lv2                           -- 二级计委包
           , plan_com_lv3                           -- 三级计委包
           , busi_lv4                               -- 四级计委包
           , bg_en_name	                            -- BG英文名称
           , '全球' as oversea_desc
        from sop_act_fcst_qty_i_all_tmp    -- 国内海外集成临时表
       where oversea_desc in('国内','海外','中国区')
       group by month
           , phase_date
           , unit
           , bg_code
           , bg_cn_name
           , lst_lv1_prod_rnd_team_code
           , lst_lv1_prod_rd_team_cn_name
           , lst_lv1_prod_rd_team_en_name
           , lst_lv2_prod_rnd_team_code
           , lst_lv2_prod_rd_team_cn_name
           , lst_lv2_prod_rd_team_en_name
           , lst_lv3_prod_rnd_team_code
           , lst_lv3_prod_rd_team_cn_name
           , lst_lv3_prod_rd_team_en_name
           , plan_com_lv1
           , plan_com_lv2
           , plan_com_lv3
           , busi_lv4
           , bg_en_name
		  ),
		  -- 取全球的最小期次
		  min_phase_date_tmp as(
		  select min(phase_date) as min_phase_date
		    from sop_act_fcst_qty_i_all_tmp2
		  )
      -- 从集成表取数入到临时表1
		  select '' as version_code,                                      --版本
			t1.month,								     			 ---会计期
			t1.phase_date,                              			---期次分区字段
			t1.item_code,                               			---ITEM编码
			sum(t1.plan_unit_quantity) as plan_unit_quantity,      ---计划单元计划量
			t1.unit,                                    			---单位
			t1.coa_no as prod_code,                                  			---产品编码
			t1.bg_code,                                 			---BG编码
			t1.bg_cn_name,	                             			---BG中文名
			t2.lv0_prod_rnd_team_code   as lst_lv0_prod_rnd_team_code,	     ---重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
			t2.lv0_prod_rd_team_cn_name as lst_lv0_prod_rd_team_cn_name,	   ---重量级团队LV0中文描述
			t2.lv0_prod_rd_team_en_name as lst_lv0_prod_rd_team_en_name,	   ---重量级团队LV0英文描述
			t2.lv1_prod_rnd_team_code   as lst_lv1_prod_rnd_team_code,	     ---重量级团队LV1编码
			t2.lv1_prod_rd_team_cn_name as lst_lv1_prod_rd_team_cn_name,	   ---重量级团队LV1中文描述
			t2.lv1_prod_rd_team_en_name as lst_lv1_prod_rd_team_en_name,	   ---重量级团队LV1英文描述
			t2.lv2_prod_rnd_team_code   as lst_lv2_prod_rnd_team_code,	     ---重量级团队LV1编码
			t2.lv2_prod_rd_team_cn_name as lst_lv2_prod_rd_team_cn_name,	   ---重量级团队LV2中文描述
			t2.lv2_prod_rd_team_en_name as lst_lv2_prod_rd_team_en_name,	   ---重量级团队LV2中文描述
			t2.lv3_prod_rnd_team_code   as lst_lv3_prod_rnd_team_code,	     ---重量级团队LV3编码
			t2.lv3_prod_rd_team_cn_name as lst_lv3_prod_rd_team_cn_name,	   ---重量级团队LV3中文描述
			t2.lv3_prod_rd_team_en_name as lst_lv3_prod_rd_team_en_name,	   ---重量级团队LV3英文描
			sum(t1.quantity) as quantity,				    		        ---SNOP数量
			t1.plan_type,                                     ---计划类型
      t1.plan_com_lv1,                                  ---一级计委包
      t1.plan_com_lv2,                                  ---二级计委包
      t1.plan_com_lv3,                                  ---三级计委包
      t1.attr4 as busi_lv4,                             ---四级计委包
      t1.port_qty,                                       ---端口数
			t1.prod_key,                                       ---产品KEY
			t2.prod_cn_name,                                   ---产品名称
			t2.prod_en_name,                                   ---产品英文描述
			t2.lv0_prod_list_en_name as bg_en_name,            ---BG英文名称
			t1.is_release_version_flag,
			'全球' as oversea_desc
		--from fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i t1            	---产品线S&OP预测（原始表）
		from fin_dm_opt_fop.fop_dwk_grp_pln_pub_snop_product_i t1      --20230731 切换BCM的表
   left join dmdim.dm_dim_product_d t2 --产品维表
	   	  on t1.prod_key = t2.prod_key
			--and t2.scd_active_ind = 1
	   	 and t2.del_flag = 'N'
	   where substr(t1.phase_date,1,4) < '2024'  -- 只取2024年之前的数据
	     and t1.phase_date >= to_char(add_months(current_date,-1),'yyyymm')||'06'   --上个月6号
	     and t1.phase_date < to_char(add_months(current_date,1),'yyyymm')||'06'     --下个月6号
	     and substring(t1.month,1,4) >= to_char(current_date,'yyyy')        --取年份大于等于当年
		 and upper(t1.measure_code) = 'ADJUST HQ SNOP'
		 and upper(t1.site_code) <> 'GLOBAL'
		 and upper(t1.area_type)='HQ_M'
		 and t1.del_flag = 'N'
		group by
  			 t1.month,
  			 t1.phase_date,
  			 t1.item_code,
  			 t1.unit,
  			 t1.coa_no,
  			 t1.bg_code,
  			 t1.bg_cn_name,
  			 t2.lv0_prod_rnd_team_code,
			   t2.lv0_prod_rd_team_cn_name,
			   t2.lv0_prod_rd_team_en_name,
			   t2.lv1_prod_rnd_team_code,
			   t2.lv1_prod_rd_team_cn_name,
			   t2.lv1_prod_rd_team_en_name,
			   t2.lv2_prod_rnd_team_code,
			   t2.lv2_prod_rd_team_cn_name,
			   t2.lv2_prod_rd_team_en_name,
			   t2.lv3_prod_rnd_team_code,
			   t2.lv3_prod_rd_team_cn_name,
			   t2.lv3_prod_rd_team_en_name,
  			 t1.plan_type,
         t1.plan_com_lv1,
         t1.plan_com_lv2,
         t1.plan_com_lv3,
         t1.attr4,
         t1.port_qty,
			   t1.prod_key,
			   t2.prod_cn_name,
			   t2.prod_en_name,
			   t2.lv0_prod_list_en_name,
			   t1.is_release_version_flag
      union all
	    -- S&OP预测国内海外集成表，包括全球、国内、海外的数据，>=2024年的全球=国内+海外
	    select '' as version_code                     -- 版本
           , month								     			        -- 会计期
           , phase_date                             -- 期次分区字段
           , '' as item_code                        -- ITEM编码
           , plan_unit_quantity                     -- 计划量
           , unit                                   -- 单位
           , '' as prod_code                        -- 产品编码
           , bg_code                                -- BG编码
           , bg_cn_name                             -- BG中文名
           , '' as lst_lv0_prod_rnd_team_code	      -- 重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
           , '' as lst_lv0_prod_rd_team_cn_name	    -- 重量级团队LV0中文描述
           , '' as lst_lv0_prod_rd_team_en_name	    -- 重量级团队LV0英文描述
           , lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , quantity				    		                -- SNOP数量
           , '' as plan_type                        -- 计划类型
           , plan_com_lv1                           -- 一级计委包
           , plan_com_lv2                           -- 二级计委包
           , plan_com_lv3                           -- 三级计委包
           , busi_lv4                               -- 四级计委包
           , null as port_qty                       -- 端口数
           , null as prod_key                       -- 产品KEY
           , '' as prod_cn_name                     -- 产品名称
           , '' as prod_en_name                     -- 产品英文描述
           , bg_en_name	                            -- BG英文名称
           , '' as is_release_version_flag
           , oversea_desc
        from sop_act_fcst_qty_i_all_tmp    -- 国内海外集成临时表
	     union all
	    select '' as version_code                     -- 版本
           , t1.month								     			        -- 会计期
           , t1.phase_date                             -- 期次分区字段
           , '' as item_code                        -- ITEM编码
           , t1.plan_unit_quantity                     -- 计划量
           , t1.unit                                   -- 单位
           , '' as prod_code                        -- 产品编码
           , t1.bg_code                                -- BG编码
           , t1.bg_cn_name                             -- BG中文名
           , '' as lst_lv0_prod_rnd_team_code	      -- 重量级团队LV0编码      -- 20230606 update by qwx1110218 修改重量级团结LV0~LV3从产品维表取，原逻辑是直取
           , '' as lst_lv0_prod_rd_team_cn_name	    -- 重量级团队LV0中文描述
           , '' as lst_lv0_prod_rd_team_en_name	    -- 重量级团队LV0英文描述
           , t1.lst_lv1_prod_rnd_team_code	            -- 重量级团队LV1编码
           , t1.lst_lv1_prod_rd_team_cn_name	          -- 重量级团队LV1中文描述
           , t1.lst_lv1_prod_rd_team_en_name	          -- 重量级团队LV1英文描述
           , t1.lst_lv2_prod_rnd_team_code	            -- 重量级团队LV2编码
           , t1.lst_lv2_prod_rd_team_cn_name	          -- 重量级团队LV2中文描述
           , t1.lst_lv2_prod_rd_team_en_name	          -- 重量级团队LV2中文描述
           , t1.lst_lv3_prod_rnd_team_code	            -- 重量级团队LV3编码
           , t1.lst_lv3_prod_rd_team_cn_name	          -- 重量级团队LV3中文描述
           , t1.lst_lv3_prod_rd_team_en_name	          -- 重量级团队LV3英文描述
           , t1.quantity                               -- SNOP数量
           , '' as plan_type                        -- 计划类型
           , t1.plan_com_lv1                           -- 一级计委包
           , t1.plan_com_lv2                           -- 二级计委包
           , t1.plan_com_lv3                           -- 三级计委包
           , t1.busi_lv4                               -- 四级计委包
           , null as port_qty                       -- 端口数
           , null as prod_key                       -- 产品KEY
           , '' as prod_cn_name                     -- 产品名称
           , '' as prod_en_name                     -- 产品英文描述
           , t1.bg_en_name	                            -- BG英文名称
           , (case when t1.phase_date = min(t1.phase_date) over(partition by to_char(to_date(t1.phase_date,'YYYYMMDD')+3,'YYYYMM') order by t1.phase_date) 
                    and t1.phase_date = t2.min_phase_date
                    and substr(t2.min_phase_date,7) < 6 
                   then 'Y'
                   when t1.phase_date = min(t1.phase_date) over(partition by to_char(to_date(t1.phase_date,'YYYYMMDD')+3,'YYYYMM') order by t1.phase_date) 
                    and t1.phase_date <> t2.min_phase_date
                   then 'Y'
                   else 'N'
              end) as is_release_version_flag
           , t1.oversea_desc
        from sop_act_fcst_qty_i_all_tmp2 t1
        left join min_phase_date_tmp t2
          on 1=1
	    ;

   v_dml_row_count := sql%rowcount;      -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入会计期:无,截止当前年月'||to_char(current_date,'yyyymm')||'的数据,snop_forecasts_temp1 临时表的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  end if;


  -- 取当前日期的最大版本数据，如果有值，则版本号+1；如果没有值，则版本编码=当前年月日_V1
  if(substring(v_current_max_version_code,9) is null or substring(v_current_max_version_code,9) = '') then
    -- 如果当前日期没有版本，则生成新的版本编码，数据入到目标表
	    insert into fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
				(
				 version_code,                                      --版本
				 month,								        		---会计期
				 phase_date,                                 		---期次分区字段
				 item_code,                                  		---ITEM编码
				 plan_unit_quantity,                         		---计划单元计划量
				 unit,                                       		---单位
				 prod_code,                                     		---产品编码
				 bg_code,                                    		---BG编码
				 bg_cn_name,	                                		---BG中文名
				 lst_lv0_prod_rnd_team_code,	                		---重量级团队LV0编码
				 lst_lv0_prod_rd_team_cn_name,	            		  ---重量级团队LV0中文描述
				 lst_lv0_prod_rd_team_en_name,	            		  ---重量级团队LV0英文描述
				 lst_lv1_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv1_prod_rd_team_cn_name,	            		  ---重量级团队LV1中文描述
				 lst_lv1_prod_rd_team_en_name,	            		  ---重量级团队LV1英文描述
				 lst_lv2_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv2_prod_rd_team_cn_name,	            		  ---重量级团队LV2中文描述
				 lst_lv2_prod_rd_team_en_name,	            		  ---重量级团队LV2中文描述
				 lst_lv3_prod_rnd_team_code,	                		---重量级团队LV3编码
				 lst_lv3_prod_rd_team_cn_name,	            		  ---重量级团队LV3中文描述
				 lst_lv3_prod_rd_team_en_name,	            		  ---重量级团队LV3英文描述
				 quantity,				                    		        ---SNOP数量
				 plan_type,                                      ---计划类型
                 plan_com_lv1,                                   ---一级计委包
                 plan_com_lv2,                                   ---二级计委包
                 plan_com_lv3,                                   ---三级计委包
                 busi_lv4,                                       ---四级计委包
                 port_qty,                                       ---端口数
				 prod_key,                                       ---产品KEY
				 prod_cn_name,                                   ---产品名称
				 prod_en_name,                                   ---产品英文描述
				 bg_en_name,	                                 ---BG英文名称
				 is_release_version_flag,                      -- 是否发布版本（Y、N、空值）  --  20230617版本新增字段
				 oversea_desc,                                 -- 区域（全球、国内、海外）  -- 1月版新增
				 remark,                            ---备注
				 created_by,          ---创建人
				 creation_date,       ---创建时间
				 last_updated_by,     ---修改人
				 last_update_date,    ---修改时间
				 del_flag             ---是否删除
				)
		  select ((select to_char(current_date,'yyyymm'))||'_'||'V1') as version_code, --版本
				 month,								        		---会计期
				 phase_date,                                 		---期次分区字段
				 item_code,                                  		---ITEM编码
				 plan_unit_quantity,                         		---计划单元计划量
				 unit,                                       		---单位
				 prod_code,                                     		---产品编码
				 bg_code,                                    		---BG编码
				 bg_cn_name,	                                		---BG中文名
				 lst_lv0_prod_rnd_team_code,	                		---重量级团队LV0编码
				 lst_lv0_prod_rd_team_cn_name,	            		  ---重量级团队LV0中文描述
				 lst_lv0_prod_rd_team_en_name,	            		  ---重量级团队LV0英文描述
				 lst_lv1_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv1_prod_rd_team_cn_name,	            		  ---重量级团队LV1中文描述
				 lst_lv1_prod_rd_team_en_name,	            		  ---重量级团队LV1英文描述
				 lst_lv2_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv2_prod_rd_team_cn_name,	            		  ---重量级团队LV2中文描述
				 lst_lv2_prod_rd_team_en_name,	            		  ---重量级团队LV2中文描述
				 lst_lv3_prod_rnd_team_code,	                		---重量级团队LV3编码
				 lst_lv3_prod_rd_team_cn_name,	            		  ---重量级团队LV3中文描述
				 lst_lv3_prod_rd_team_en_name,	            		  ---重量级团队LV3英文描述
				 quantity,				                    		        ---SNOP数量
				 plan_type,                                      ---计划类型
                 plan_com_lv1,                                   ---一级计委包
                 plan_com_lv2,                                   ---二级计委包
                 plan_com_lv3,                                   ---三级计委包
                 busi_lv4,                                       ---四级计委包
                 port_qty,                                       ---端口数
				 prod_key,                                       ---产品KEY
				 prod_cn_name,                                   ---产品名称
				 prod_en_name,                                   ---产品英文描述
				 bg_en_name,	                                     ---BG英文名称
				 is_release_version_flag,
				 oversea_desc,
				 '' as remark,							 ---备注
				 -1 as created_by,                       ---创建人
				 current_timestamp as creation_date,     ---创建时间
				 -1 as last_updated_by,                  ---修改人
				 current_timestamp as last_update_date,  ---修改时间
				 'N' as del_flag                         ---是否删除
		   from snop_forecasts_temp1
		   ;
  else
    -- 如果有当前日期的版本，则版本+1，数据入到目标表
	    insert into fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
				(
				 version_code,                                      --版本
				 month,								        		---会计期
				 phase_date,                                 		---期次分区字段
				 item_code,                                  		---ITEM编码
				 plan_unit_quantity,                         		---计划单元计划量
				 unit,                                       		---单位
				 prod_code,                                     		---产品编码
				 bg_code,                                    		---BG编码
				 bg_cn_name,	                                		---BG中文名
				 lst_lv0_prod_rnd_team_code,	                		---重量级团队LV0编码
				 lst_lv0_prod_rd_team_cn_name,	            		  ---重量级团队LV0中文描述
				 lst_lv0_prod_rd_team_en_name,	            		  ---重量级团队LV0英文描述
				 lst_lv1_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv1_prod_rd_team_cn_name,	            		  ---重量级团队LV1中文描述
				 lst_lv1_prod_rd_team_en_name,	            		  ---重量级团队LV1英文描述
				 lst_lv2_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv2_prod_rd_team_cn_name,	            		  ---重量级团队LV2中文描述
				 lst_lv2_prod_rd_team_en_name,	            		  ---重量级团队LV2中文描述
				 lst_lv3_prod_rnd_team_code,	                		---重量级团队LV3编码
				 lst_lv3_prod_rd_team_cn_name,	            		  ---重量级团队LV3中文描述
				 lst_lv3_prod_rd_team_en_name,	            		  ---重量级团队LV3英文描述
				 quantity,				                    		        ---SNOP数量
				 plan_type,                                      ---计划类型
                 plan_com_lv1,                                   ---一级计委包
                 plan_com_lv2,                                   ---二级计委包
                 plan_com_lv3,                                   ---三级计委包
                 busi_lv4,                                       ---四级计委包
                 port_qty,                                       ---端口数
				 prod_key,                                       ---产品KEY
				 prod_cn_name,                                   ---产品名称
				 prod_en_name,                                   ---产品英文描述
				 bg_en_name,	                                     ---BG英文名称
				 is_release_version_flag,                      -- 是否发布版本（Y、N、空值）  --  20230617版本新增字段
				 oversea_desc,                                 -- 区域（全球、国内、海外、中国区）  -- 1月版新增
				 remark,                            ---备注
				 created_by,          ---创建人
				 creation_date,       ---创建时间
				 last_updated_by,     ---修改人
				 last_update_date,    ---修改时间
				 del_flag             ---是否删除
				)
		  select (substring(v_current_max_version_code,1,8)||(substring(v_current_max_version_code,9)+1)) as version_code, --版本
				 month,								        		---会计期
				 phase_date,                                 		---期次分区字段
				 item_code,                                  		---ITEM编码
				 plan_unit_quantity,                         		---计划单元计划量
				 unit,                                       		---单位
				 prod_code,                                     		---产品编码
				 bg_code,                                    		---BG编码
				 bg_cn_name,	                                		---BG中文名
				 lst_lv0_prod_rnd_team_code,	                		---重量级团队LV0编码
				 lst_lv0_prod_rd_team_cn_name,	            		  ---重量级团队LV0中文描述
				 lst_lv0_prod_rd_team_en_name,	            		  ---重量级团队LV0英文描述
				 lst_lv1_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv1_prod_rd_team_cn_name,	            		  ---重量级团队LV1中文描述
				 lst_lv1_prod_rd_team_en_name,	            		  ---重量级团队LV1英文描述
				 lst_lv2_prod_rnd_team_code,	                		---重量级团队LV1编码
				 lst_lv2_prod_rd_team_cn_name,	            		  ---重量级团队LV2中文描述
				 lst_lv2_prod_rd_team_en_name,	            		  ---重量级团队LV2中文描述
				 lst_lv3_prod_rnd_team_code,	                		---重量级团队LV3编码
				 lst_lv3_prod_rd_team_cn_name,	            		  ---重量级团队LV3中文描述
				 lst_lv3_prod_rd_team_en_name,	            		  ---重量级团队LV3英文描述
				 quantity,				                    		        ---SNOP数量
				 plan_type,                                      ---计划类型
                 plan_com_lv1,                                   ---一级计委包
                 plan_com_lv2,                                   ---二级计委包
                 plan_com_lv3,                                   ---三级计委包
                 busi_lv4,                                       ---四级计委包
                 port_qty,                                       ---端口数
				 prod_key,                                       ---产品KEY
				 prod_cn_name,                                   ---产品名称
				 prod_en_name,                                   ---产品英文描述
				 bg_en_name,	                                     ---BG英文名称
				 is_release_version_flag,
				 oversea_desc,
				 '' as remark,							 ---备注
				 -1 as created_by,                       ---创建人
				 current_timestamp as creation_date,     ---创建时间
				 -1 as last_updated_by,                  ---修改人
				 current_timestamp as last_update_date,  ---修改时间
				 'N' as del_flag                         ---是否删除
		   from snop_forecasts_temp1
		 ;

	end if
	 ;

	v_dml_row_count := sql%rowcount;         -- 收集数据量

	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_new_version_code from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
	where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取当前生成的版本


-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,   --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_new_version_code||',dm_fop_snop_forecasts_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


--处理异常信息
	exception
		when others then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	         --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

