-- ----------------------------
-- Table structure for fop_dwk_pln_pub_snop_product_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwk_pln_pub_snop_product_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwk_pln_pub_snop_product_i" (
  "phase_no" varchar(60) COLLATE "pg_catalog"."default",
  "phase_date" varchar(60) COLLATE "pg_catalog"."default",
  "month" varchar(60) COLLATE "pg_catalog"."default",
  "snop_code" varchar(300) COLLATE "pg_catalog"."default",
  "item_code" varchar(200) COLLATE "pg_catalog"."default",
  "item_key" numeric,
  "coa_no" varchar(200) COLLATE "pg_catalog"."default",
  "prod_key" numeric,
  "measure_code" varchar(90) COLLATE "pg_catalog"."default",
  "bucket_id" numeric,
  "area_id" varchar(180) COLLATE "pg_catalog"."default",
  "area_key" numeric,
  "area_type" varchar(180) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "site_code" varchar(180) COLLATE "pg_catalog"."default",
  "site_key" numeric,
  "header_id" numeric,
  "line_id" numeric,
  "bucket_desc" varchar(200) COLLATE "pg_catalog"."default",
  "review_status" varchar(30) COLLATE "pg_catalog"."default",
  "pgroup_code" varchar(100) COLLATE "pg_catalog"."default",
  "end_date" timestamp(0),
  "port_qty" numeric,
  "plan_type" varchar(30) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(600) COLLATE "pg_catalog"."default",
  "attr4" varchar(600) COLLATE "pg_catalog"."default",
  "plan_unit_quantity" numeric,
  "prod_type_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_type" varchar(600) COLLATE "pg_catalog"."default",
  "prod_sous_type_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_sous_type" varchar(600) COLLATE "pg_catalog"."default",
  "unit" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "material_id" numeric,
  "product_id" numeric,
  "product_main_yn" varchar(10) COLLATE "pg_catalog"."default",
  "quantity" numeric,
  "ss_id" numeric,
  "bd_bg_code" varchar(4000) COLLATE "pg_catalog"."default",
  "bd_bg_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bg_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bu_code" varchar(4000) COLLATE "pg_catalog"."default",
  "bd_bu_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bu_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_1" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_2" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_3" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_4" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_5" varchar(1000) COLLATE "pg_catalog"."default",
  "last_update_date_code" timestamp(0),
  "del_flag" varchar(1) COLLATE "pg_catalog"."default",
  "crt_cycle_id" numeric,
  "last_upd_cycle_id" numeric,
  "crt_job_instance_id" numeric,
  "upd_job_instance_id" numeric,
  "dw_last_update_date" timestamp(0),
  "is_release_version_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;

