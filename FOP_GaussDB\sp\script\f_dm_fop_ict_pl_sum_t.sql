-- ----------------------------
-- Function structure for f_dm_fop_ict_pl_sum_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_ict_pl_sum_t"("p_schedule" varchar, "p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_ict_pl_sum_t"(IN "p_schedule" varchar=NULL::character varying, IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-10-12
创建人  ：鲁广武
背景描述：ICT损益汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(schedule)：调度类型 增量(I) 或 全量(F)
          参数二(scene)：场景S1、S2
          参数三(p_period)：传入会计期（年月）数
		      参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_ict_pl_sum_t();
修改记录：20230206 qwx1110218 新增会计期参数，新增版本字段
          调度类型+掺入会计期，有值：1、增量：从集成表取上月数据 + 最大版本的数据（所有数据关联维度表刷新）；传入值格式：f_dm_fop_ict_pl_sum_t('I',202210)
                                          2、全量：从集成表取period_id<系统当前年月的数据 + 最大版本的数据（所有数据关联维度表刷新）；传入值格式：f_dm_fop_ict_pl_sum_t('F',202210)
          调度类型+掺入会计期，无值：从集成表取period_id<系统当前年月的数据 +  最大版本的数据（所有数据关联维度表刷新）；传入值格式：f_dm_fop_ict_pl_sum_t()
          2024-6-13 qwx1110218 “BG去掉'数字能源'和'云原生公有云客户'”排除这2个BG的原因是：旧表或者法人的变化（未知），但是新表：现在从ICT换成了P&S，就不需要排除这2个BG；
                               <23年的从旧表取，排除'数字能源'和'云原生公有云客户'2个BG；=23年的从新表取，排除'数字能源'和'云原生公有云客户'2个BG；>23年的从新表取，不排除'数字能源'和'云原生公有云客户'2个BG；
          2024-9-5 qwx1110218   通过 geo_pc_key 关联 dm_dim_region_rc_d 维表，24年9月版修改为取ICT的国内海外字段：domestic_or_oversea_code（GH0002 中国区、GH0003 海外、GH0004 其他）
*/


declare
	v_sp_name varchar(100)  := 'fin_dm_opt_fop.f_dm_fop_ict_pl_sum_t('''||p_schedule||''','||p_period||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t';
	v_max_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月_V1...VN
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月_V1...VN
	v_new_version_code varchar(50);  -- 新生成的版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;
	v_del_row_count  number default 0 ;
	v_add_row_count  number default 0 ;
	v_count          number default 0;  -- 根据传入会计期取来源表的数据


begin
	x_success_flag := '1';                                 --1表示成功
  
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code
      from fin_dm_opt_fop.dm_fop_ict_pl_sum_t 
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_ict_pl_sum_t)
     group by substr(version_code,1,6)
    ;
  
	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as version_code 
	  into v_current_max_version_code 
	  from fin_dm_opt_fop.dm_fop_ict_pl_sum_t 
	 where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取当前日期的最大版本号
   
   if(substr(v_current_max_version_code,8) = 'V') then
     v_current_max_version_code := '';
	   --写日志,开始
     perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 1,
          p_log_cal_log_desc => 'ICT损益汇总表'||v_tbl_name||'，目标表中 '||to_char(current_date,'yyyymm')||' 当前年月没有最大版本，开始运行',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
  else
    --写日志,开始
     perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'ICT损益汇总表'||v_tbl_name||'，目标表中 '||to_char(current_date,'yyyymm')||' 目前对应的最大版本编码：'||v_current_max_version_code||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  end if;
  
  
	-- 创建 fin_rpt_item_grp_f_tmp1 临时表，取最大版本的数据或者没有版本的全量数据
	drop table if exists fin_rpt_item_grp_f_tmp1;
	create temporary table fin_rpt_item_grp_f_tmp1(
     version_code                  varchar(100)          -- 版本编码
     ,period_id                    numeric not null      ---会计期
     ,prod_key                     numeric               ---产品KEY
     ,prod_code                    varchar(50)           ---产品编码
     ,prod_cn_name                 varchar(600)          ---产品名称
     ,prod_en_name	               varchar(600)          ---产品英文描述
     ,geo_pc_key                   numeric               ---区域责任中心KEY
     ,region_code	                 varchar(50)           ---地区部编码
     ,region_cn_name	             varchar(200)          ---地区部中文名称
     ,region_en_name	             varchar(200)          ---地区部英文名称
     ,rep_office_code	             varchar(50)           ---代表处编码
     ,rep_office_cn_name	         varchar(200)          ---代表处中文名称
     ,rep_office_en_name	         varchar(200)          ---代表处英文名称
     ,rmb_fact_ex_rate_ptd_amt     numeric(38,10)        ---人民币实际汇率当期发生额
     ,usd_fact_ex_rate_ptd_amt     numeric(38,10)        ---美元实际汇率当期发生额
     ,bg_code                      varchar(50)           ---BG编码
     ,bg_name                      varchar(200)          ---BG中文描述
     ,bg_en_name	                 varchar(200)          ---BG英文名称
     ,lv0_prod_rnd_team_code       varchar(50)           ---重量级团队LV0编码
     ,lv0_prod_rd_team_cn_name     varchar(600)          ---重量级团队LV0中文描述
     ,lv0_prod_rd_team_en_name     varchar(600)          ---重量级团队LV0英文描述
     ,lv1_prod_rnd_team_code       varchar(50)           ---重量级团队LV1编码
     ,lv1_prod_rd_team_cn_name     varchar(600)          ---重量级团队LV1中文描述
     ,lv1_prod_rd_team_en_name     varchar(600)          ---重量级团队LV1英文描述
     ,lv2_prod_rnd_team_code       varchar(50)           ---重量级团队LV1编码
     ,lv2_prod_rd_team_cn_name     varchar(600)          ---重量级团队LV2中文描述
     ,lv2_prod_rd_team_en_name     varchar(600)          ---重量级团队LV2中文描述
     ,lv3_prod_rnd_team_code       varchar(50)           ---重量级团队LV3编码
     ,lv3_prod_rd_team_cn_name     varchar(600)          ---重量级团队LV3中文描述
     ,lv3_prod_rd_team_en_name     varchar(600)          ---重量级团队LV3英文描述
     ,oversea_flag                 varchar(20)           ---海外标志
     ,report_item_l1_code          varchar(50)           ---报表项1级编码
     ,report_item_l1_cn_name       varchar(200)          ---报表项1级中文名
     ,report_item_l2_code          varchar(50)           ---报表项2级编码
     ,report_item_l2_cn_name       varchar(200)          ---报表项2级中文名
	)on commit preserve rows distribute by hash(period_id, prod_key, geo_pc_key)
	;
	
	-- 传入参数一非空，传入参数二非空
	if((p_schedule is not null or p_schedule <> '') and (p_period is not null or p_period <> '')) then
	   
	   -- 如果传入会计期<=202212，则从原来的集成表取；否则从新表取。
	   if(p_period <= 202301 ) then
	   -- 生成版本之前需要先判断来源表中是否有传入参数的数据，如没有则不用生成版本
     select count(*) as cnt into v_count
       from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	     left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	       on t1.major_prod_key = t3.prod_key
        and t3.del_flag = 'N'
 	     left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	       on t1.geo_pc_key = t4.geo_pc_key
 	      and t4.del_flag = 'N'
 	     join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
         on t1.data_category_id = t5.data_category_id
	      and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	     join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	       on t1.report_item_id = t8.report_item_l5_id
 	      and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	      and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	      and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
      where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')  -- 取上个月
        and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  
     ;
     
     elseif(p_period > 202301 and  p_period <= 202401) then
       select count(*) as cnt into v_count
         from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
  	     left join dmdim.dm_dim_product_d t3               			    ---产品维表
  	       on t1.major_prod_key = t3.prod_key
          and t3.del_flag = 'N'
  	     left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
  	       on t1.geo_pc_key = t4.geo_pc_key
  	      and t4.del_flag = 'N'
  	     join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
           on t1.data_category_id = t5.data_category_id
 	        and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
  	     join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
  	       on t1.report_item_id = t8.report_item_l5_id
  	      and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
  	      and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
  	      and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
        where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')  -- 取上个月
          and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
      ;
     
     else
     -- 从新表取
     select count(*) as cnt into v_count
       from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
 	     left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	       on t1.major_prod_key = t3.prod_key
        and t3.del_flag = 'N'
 	     left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	       on t1.geo_pc_key = t4.geo_pc_key
 	      and t4.del_flag = 'N'
 	     join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
         on t1.data_category_id = t5.data_category_id
	      and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	     join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	       on t1.report_item_id = t8.report_item_l5_id
 	      and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	      and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	      and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
      where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')  -- 取上个月
     ;
	  end if;
	  
	  -- 判断传入场景是否标准且传入会计期格式有误
	  if((upper(p_schedule) not in('I','F')) and (p_period is not null or p_period <> '')) then
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，第一个参数只能是I、F；第二个参数格式：yyyymm ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	  x_success_flag := '2001';
	  return;
	  
	  -- 判断传入场景是否标准且传入会计期格式有误
	  elseif((upper(p_schedule) not in('I','F')) and length(p_period) <> 6) then
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，第一个参数只能是I、F；第二个参数只能是6位，格式：yyyymm ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	  x_success_flag := '2001';
	  return;
	  
	  -- 判断传入场景是否标准且传入会计期是否有值
	  elseif((upper(p_schedule) not in('I','F')) and v_count = 0) then
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，第一个参数只能是I、F；第二个参数来源表没数据！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	  x_success_flag := '2001';
	  return;
	  
	  -- 判断传入调度类型是否标准
	  elseif(upper(p_schedule) not in('I','F')) then
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'传入第二个参数：'||p_period||'，第一个参数传入值不对，只能是I、F ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	  x_success_flag := '2001';
	  return;
	  
	  -- 判断传入会计期必须是6位
	  elseif(length(p_period) <> 6) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入第二个参数：'||p_period||'，传入参数格式有误，正确格式：yyyymm ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	    return;
	  
	  -- 判断传入会计期在来源表是否有数据
	  elseif(v_count = 0) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，来源表中没有传入会计期数据，请重新传入会计期！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	    return;
	  
	  -- 调度类型+传入会计期，有值：1、增量：从集成表取上月数据 + 最大版本的数据（所有数据关联维度表刷新）
    -- 更新了传入会计期的数据，最大版本的所有数据都用最新的维表刷新
	  elseif(upper(p_schedule) = 'I') then
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name	                   ---产品英文描述
           , geo_pc_key                      ---区域责任中心KEY
           , region_code	                   ---地区部编码
           , region_cn_name	                 ---地区部中文名称
           , region_en_name	                 ---地区部英文名称
           , rep_office_code	               ---代表处编码
           , rep_office_cn_name	             ---代表处中文名称
           , rep_office_en_name	             ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG中文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      -- 取最大版本数据，所有数据关联维度表刷新
      select t1.version_code,                             -- 版本编码
		         t1.period_id,										            -- 会计期
		         t1.prod_key,          		                  	-- 产品KEY
		         t3.prod_code,	                        		  -- 产品编码
		         t3.prod_cn_name,                        			-- 产品中文名称
		         t3.prod_en_name,                        			-- 产品英文名称
		         t1.geo_pc_key,                          			-- 区域责任中心KEY
		         t4.region_code,	                            -- 地区部编码
             t4.region_cn_name,	                          -- 地区部中文名称
             t4.region_en_name,	                          -- 地区部英文名称
             t4.repoffice_code as rep_office_code,	      -- 代表处编码
             t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
             t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		         t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		         t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		         t3.lv0_prod_list_code as bg_code,       			-- BG编码
		         t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		         t3.lv0_prod_list_en_name as bg_en_name,      -- BG英文描述
		         t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		         t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		         t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		         t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		         t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		         t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		         t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		         t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		         -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		         t1.report_item_l1_code,	     						    -- 报表项1级编码
		         t1.report_item_l1_cn_name,   					      -- 报表项1级中文名
		         t1.report_item_l2_code,	      						  -- 报表项2级编码
		         t1.report_item_l2_cn_name 	  						    -- 报表项2级中文名
        from fin_dm_opt_fop.dm_fop_ict_pl_sum_t t1
        left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.prod_key = t3.prod_key
         and t3.del_flag = 'N'
        left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
       where t1.version_code = v_max_version_code  -- 取最大版本数据
      ;
      
      v_dml_row_count := sql%rowcount;  -- 收集数据量
      
      -- 删除最大版本数据中上月数据
      delete fin_rpt_item_grp_f_tmp1 where period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm');
      
      if(p_period <= 202301 ) then
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      -- 新集成表只能取到>=2023年之后的数据（需要排除2个BG）
      select '' as version_code,                          -- 版本编码
		         t1.period_id,										            -- 会计期
		         t1.major_prod_key as prod_key,          			-- 产品KEY
		         t3.prod_code,	                        		  -- 产品编码
		         t3.prod_cn_name,                        			-- 产品名称
		         t3.prod_en_name,                             -- 产品英文名称
		         t1.geo_pc_key,                          			-- 区域责任中心KEY
		         t4.region_code,                              -- 地区部编码  
             t4.region_cn_name,                           -- 地区部中文名称
             t4.region_en_name,                           -- 地区部英文名称
             t4.repoffice_code as rep_office_code,	      -- 代表处编码
             t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
             t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		         t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		         t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		         t3.lv0_prod_list_code as bg_code,       			-- BG编码
		         t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		         t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		         t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		         t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		         t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		         t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		         t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		         t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		         t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		         t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		         -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		         t8.report_item_l1_code,	     						    -- 报表项1级编码
		         t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		         t8.report_item_l2_code,	      						  -- 报表项2级编码
		         t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')   -- 取集成表的上月数据
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
      ;
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，增量：从集成表(fop_dwr_fin_rpt_item_ps_fv)取上月数据 + 最大版本的数据（所有数据关联维度表刷新），fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
      
      elseif(p_period > 202301 and p_period <= 202401) then
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      -- 新集成表只能取到>=2023年之后的数据
      select '' as version_code,                          -- 版本编码
		         t1.period_id,										            -- 会计期
		         t1.major_prod_key as prod_key,          			-- 产品KEY
		         t3.prod_code,	                        		  -- 产品编码
		         t3.prod_cn_name,                        			-- 产品名称
		         t3.prod_en_name,                             -- 产品英文名称
		         t1.geo_pc_key,                          			-- 区域责任中心KEY
		         t4.region_code,                              -- 地区部编码  
             t4.region_cn_name,                           -- 地区部中文名称
             t4.region_en_name,                           -- 地区部英文名称
             t4.repoffice_code as rep_office_code,	       -- 代表处编码
             t4.repoffice_cn_name as rep_office_cn_name,	 -- 代表处中文名称
             t4.repoffice_en_name as rep_office_en_name,	 -- 代表处英文名称
		         t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		         t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		         t3.lv0_prod_list_code as bg_code,       			-- BG编码
		         t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		         t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		         t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		         t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		         t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		         t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		         t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		         t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		         t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		         t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		         -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		         t8.report_item_l1_code,	     						    -- 报表项1级编码
		         t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		         t8.report_item_l2_code,	      						  -- 报表项2级编码
		         t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')   -- 取集成表的上月数据
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
      ;
      
      else
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      -- 新集成表只能取到>=2023年之后的数据
      select '' as version_code,                          -- 版本编码
		         t1.period_id,										            -- 会计期
		         t1.major_prod_key as prod_key,          			-- 产品KEY
		         t3.prod_code,	                        		  -- 产品编码
		         t3.prod_cn_name,                        			-- 产品名称
		         t3.prod_en_name,                             -- 产品英文名称
		         t1.geo_pc_key,                          			-- 区域责任中心KEY
		         t4.region_code,                              -- 地区部编码  
             t4.region_cn_name,                           -- 地区部中文名称
             t4.region_en_name,                           -- 地区部英文名称
             t4.repoffice_code as rep_office_code,	       -- 代表处编码
             t4.repoffice_cn_name as rep_office_cn_name,	 -- 代表处中文名称
             t4.repoffice_en_name as rep_office_en_name,	 -- 代表处英文名称
		         t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		         t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		         t3.lv0_prod_list_code as bg_code,       			-- BG编码
		         t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		         t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		         t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		         t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		         t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		         t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		         t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		         t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		         t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		         t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		         t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		         t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		         -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		         t8.report_item_l1_code,	     						    -- 报表项1级编码
		         t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		         t8.report_item_l2_code,	      						  -- 报表项2级编码
		         t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id = to_char(to_date(p_period||'01','yyyymmdd') - interval'1 month','yyyymm')   -- 取集成表的上月数据
      ;
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，增量：从集成表(fop_mr_dm_ps_for_opt_pl_dtl_v)取上月数据 + 最大版本的数据（所有数据关联维度表刷新），fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
      
      end if;  
      
    -- 调度类型+传入会计期，有值：2、全量：从集成表取period_id<传入会计期的数据 + 最大版本的数据（所有数据关联维度表刷新）
    -- 更新了传入会计期的数据，最大版本的所有数据都用最新的维表刷新
	  elseif(upper(p_schedule) = 'F') then
	    -- 如果传入会计期<=202212，则从原来的集成表取值（需要排除2个BG）；否则从2张集成表取值。
	    if(p_period <= 202212) then 
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	       -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	 -- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	 -- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id <= p_period   -- 取集成表period_id<传入会计期的数据
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
      ;
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，全量：从集成表(fop_dwr_fin_rpt_item_ps_fv)取period_id<传入会计期的数据 + 最大版本的数据（所有数据关联维度表刷新），fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
       ) ;
      
      -- 如果传入会计期<202401，则<23年的数据从旧集成表取（需要排除2个BG），23年数据从新集成表取，且需要排除2个BG
      elseif(p_period > 202212 and p_period < 202401) then 
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	       -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	 -- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	 -- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id < 202301   -- 取集成表period_id<传入会计期的数据
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
       union all
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	      -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id >= 202301    -- 20230426 update by qwx1110218 2023年5月版切换新表，新表从202301开始取数 
         and t1.period_id <= p_period
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
      ;
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，全量：从集成表(fop_dwr_fin_rpt_item_ps_fv)取period_id<传入会计期的数据 + 最大版本的数据（所有数据关联维度表刷新），fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
       ) ;
      
      -- 传入会计期>202401，则<23年的数据从旧表取（需要排除2个BG），=23年的从新表取，且需要排除2个BG，>23年的从新表取，且不需要排除BG；
      else
      -- 从集成表取数入到临时表1
      insert into fin_rpt_item_grp_f_tmp1(
             version_code                     -- 版本编码
           , period_id                       ---会计期
           , prod_key                        ---产品KEY
           , prod_code                       ---产品编码
           , prod_cn_name                    ---产品中文名称
           , prod_en_name                    ---产品英文名称
           , geo_pc_key                      ---区域责任中心KEY
           , region_code                     ---地区部编码  
           , region_cn_name                  ---地区部中文名称
           , region_en_name                  ---地区部英文名称
           , rep_office_code                 ---代表处编码  
           , rep_office_cn_name              ---代表处中文名称
           , rep_office_en_name              ---代表处英文名称
           , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
           , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
           , bg_code                         ---BG编码
           , bg_name                         ---BG中文描述
           , bg_en_name                      ---BG英文描述
           , lv0_prod_rnd_team_code          ---重量级团队LV0编码
           , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
           , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
           , lv1_prod_rnd_team_code          ---重量级团队LV1编码
           , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
           , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
           , lv2_prod_rnd_team_code          ---重量级团队LV1编码
           , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
           , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
           , lv3_prod_rnd_team_code          ---重量级团队LV3编码
           , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
           , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
           , oversea_flag                    ---海外标志
           , report_item_l1_code             ---报表项1级编码
           , report_item_l1_cn_name          ---报表项1级中文名
           , report_item_l2_code             ---报表项2级编码
           , report_item_l2_cn_name          ---报表项2级中文名
      )
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	      -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       --where t1.period_id < p_period   -- 取集成表period_id<传入会计期的数据
       where t1.period_id <= 202212    -- 20230426 update by qwx1110218 2023年5月版切换新表，新表从202301开始取数 
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
       union all
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	      -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id between 202301 and 202312
         and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
       union all
      select '' as version_code,                          -- 版本编码
		          t1.period_id,										            -- 会计期
		          t1.major_prod_key as prod_key,          			-- 产品KEY
		          t3.prod_code,	                        		  -- 产品编码
		          t3.prod_cn_name,                        			-- 产品名称
		          t3.prod_en_name,                             -- 产品英文名称
		          t1.geo_pc_key,                          			-- 区域责任中心KEY
		          t4.region_code,                              -- 地区部编码  
              t4.region_cn_name,                           -- 地区部中文名称
              t4.region_en_name,                           -- 地区部英文名称
              t4.repoffice_code as rep_office_code,	      -- 代表处编码
              t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
              t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		          t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		          t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		          t3.lv0_prod_list_code as bg_code,       			-- BG编码
		          t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		          t3.lv0_prod_list_en_name as bg_en_name,       -- BG英文描述
		          t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		          t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		          t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		          t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		          t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		          t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		          t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		          t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		          t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		          t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		          -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		          t8.report_item_l1_code,	     						    -- 报表项1级编码
		          t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		          t8.report_item_l2_code,	      						  -- 报表项2级编码
		          t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	      from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益
 	      left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	        on t1.major_prod_key = t3.prod_key
         and t3.del_flag = 'N'
 	      left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	        on t1.geo_pc_key = t4.geo_pc_key
 	       and t4.del_flag = 'N'
 	      join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
          on t1.data_category_id = t5.data_category_id
	       and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	      join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	        on t1.report_item_id = t8.report_item_l5_id
 	       and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	       and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	       and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
       where t1.period_id > 202312  
         and t1.period_id <= p_period
      ;
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，全量：从2张集成表取period_id<传入会计期的数据 + 最大版本的数据（所有数据关联维度表刷新），fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
       ) ;
      
      end if;
      
	  end if;
	
	-- 传入参数一空，传入参数二非空
	elseif((p_schedule is null or p_schedule = '') and (p_period is not null or p_period <> '')) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，第一个参数不能为空，只能是I、F；第二个参数格式：yyyymm ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	  x_success_flag := '2001';
	  return;
	
	-- 传入参数一非空，传入参数二空
	elseif(((upper(p_schedule) not in('I','F')) or (upper(p_schedule) in('I','F')))and (p_period is null or p_period = '')) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，第一个参数只能是I、F；第二个参数不能为空，格式：yyyymm ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	  x_success_flag := '2001';
	  return;
	
	-- 传入参数一空，传入参数二空
	-- 调度类型+传入会计期，无值：3、从集成表取上月数据 + 最大版本的数据（period_id<当前年月-1的数据关联维度表刷新）
	-- 传入会计期>202401，则<23年的数据从旧表取（需要排除2个BG），=23年的从新表取，且需要排除2个BG，>23年的从新表取，且不需要排除BG；
	else
	  insert into fin_rpt_item_grp_f_tmp1(
           version_code                     -- 版本编码
         , period_id                       ---会计期
         , prod_key                        ---产品KEY
         , prod_code                       ---产品编码
         , prod_cn_name                    ---产品名称
         , prod_en_name                    ---产品英文名称
         , geo_pc_key                      ---区域责任中心KEY
         , region_code                     ---地区部编码  
         , region_cn_name                  ---地区部中文名称
         , region_en_name                  ---地区部英文名称
         , rep_office_code                 ---代表处编码  
         , rep_office_cn_name              ---代表处中文名称
         , rep_office_en_name              ---代表处英文名称
         , rmb_fact_ex_rate_ptd_amt        ---人民币实际汇率当期发生额
         , usd_fact_ex_rate_ptd_amt        ---美元实际汇率当期发生额
         , bg_code                         ---BG编码
         , bg_name                         ---BG中文描述
         , bg_en_name                      ---BG英文描述
         , lv0_prod_rnd_team_code          ---重量级团队LV0编码
         , lv0_prod_rd_team_cn_name        ---重量级团队LV0中文描述
         , lv0_prod_rd_team_en_name        ---重量级团队LV0英文描述
         , lv1_prod_rnd_team_code          ---重量级团队LV1编码
         , lv1_prod_rd_team_cn_name        ---重量级团队LV1中文描述
         , lv1_prod_rd_team_en_name        ---重量级团队LV1英文描述
         , lv2_prod_rnd_team_code          ---重量级团队LV1编码
         , lv2_prod_rd_team_cn_name        ---重量级团队LV2中文描述
         , lv2_prod_rd_team_en_name        ---重量级团队LV2中文描述
         , lv3_prod_rnd_team_code          ---重量级团队LV3编码
         , lv3_prod_rd_team_cn_name        ---重量级团队LV3中文描述
         , lv3_prod_rd_team_en_name        ---重量级团队LV3英文描述
         , oversea_flag                    ---海外标志
         , report_item_l1_code             ---报表项1级编码
         , report_item_l1_cn_name          ---报表项1级中文名
         , report_item_l2_code             ---报表项2级编码
         , report_item_l2_cn_name          ---报表项2级中文名
    )
    -- 从集成表取数入到临时表1
    select '' as version_code,                          -- 版本编码
		       t1.period_id,										            -- 会计期
		       t1.major_prod_key as prod_key,          			-- 产品KEY
		       t3.prod_code,	                        		  -- 产品编码
		       t3.prod_cn_name,                        			-- 产品名称
		       t3.prod_en_name,                             -- 产品英文名称
		       t1.geo_pc_key,                          			-- 区域责任中心KEY
		       t4.region_code,                              -- 地区部编码  
           t4.region_cn_name,                           -- 地区部中文名称
           t4.region_en_name,                           -- 地区部英文名称
           t4.repoffice_code as rep_office_code,	      -- 代表处编码
           t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
           t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		       t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		       t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		       t3.lv0_prod_list_code as bg_code,       			-- BG编码
		       t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		       t3.lv0_prod_list_en_name as bg_en_name,      -- BG英文描述 
		       t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		       t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		       t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		       t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		       t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		       t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		       t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		       t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		       -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		       t8.report_item_l1_code,	     						    -- 报表项1级编码
		       t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		       t8.report_item_l2_code,	      						  -- 报表项2级编码
		       t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	    from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
     --where t1.period_id < (to_char(current_date,'yyyymm'))   -- 取集成表period_id<系统当前年月的数据
     where t1.period_id <= 202212    -- 20230426 update by qwx1110218 2023年5月版切换新表，新表从202301开始取数 
       and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
     union all
    -- 新增从新表取数的逻辑
    select '' as version_code,                          -- 版本编码
		       t1.period_id,										            -- 会计期
		       t1.major_prod_key as prod_key,          			-- 产品KEY
		       t3.prod_code,	                        		  -- 产品编码
		       t3.prod_cn_name,                        			-- 产品名称
		       t3.prod_en_name,                             -- 产品英文名称
		       t1.geo_pc_key,                          			-- 区域责任中心KEY
		       t4.region_code,                              -- 地区部编码  
           t4.region_cn_name,                           -- 地区部中文名称
           t4.region_en_name,                           -- 地区部英文名称
           t4.repoffice_code as rep_office_code,	      -- 代表处编码
           t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
           t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		       t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		       t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		       t3.lv0_prod_list_code as bg_code,       			-- BG编码
		       t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		       t3.lv0_prod_list_en_name as bg_en_name,      -- BG英文描述
		       t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		       t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		       t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		       t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		       t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		       t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		       t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		       t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		       -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		       t8.report_item_l1_code,	     						    -- 报表项1级编码
		       t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		       t8.report_item_l2_code,	      						  -- 报表项2级编码
		       t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	    from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益（202305月版切的新表）
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
     where t1.period_id between 202301 and 202312
       and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
     union all
    -- 新增从新表取数的逻辑
    select '' as version_code,                          -- 版本编码
		       t1.period_id,										            -- 会计期
		       t1.major_prod_key as prod_key,          			-- 产品KEY
		       t3.prod_code,	                        		  -- 产品编码
		       t3.prod_cn_name,                        			-- 产品名称
		       t3.prod_en_name,                             -- 产品英文名称
		       t1.geo_pc_key,                          			-- 区域责任中心KEY
		       t4.region_code,                              -- 地区部编码  
           t4.region_cn_name,                           -- 地区部中文名称
           t4.region_en_name,                           -- 地区部英文名称
           t4.repoffice_code as rep_office_code,	      -- 代表处编码
           t4.repoffice_cn_name as rep_office_cn_name,	-- 代表处中文名称
           t4.repoffice_en_name as rep_office_en_name,	-- 代表处英文名称
		       t1.rmb_fact_ex_rate_ptd_amt,            			-- 人民币实际汇率当期发生额
		       t1.usd_fact_ex_rate_ptd_amt,            			-- 美元实际汇率当期发生额
		       t3.lv0_prod_list_code as bg_code,       			-- BG编码
		       t3.lv0_prod_list_cn_name as bg_name,    			-- BG中文描述
		       t3.lv0_prod_list_en_name as bg_en_name,      -- BG英文描述
		       t3.lv0_prod_rnd_team_code,	            			-- 重量级团队LV0编码
		       t3.lv0_prod_rd_team_cn_name,	        			  -- 重量级团队LV0中文描述
		       t3.lv0_prod_rd_team_en_name,	        			  -- 重量级团队LV0英文描述
		       t3.lv1_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv1_prod_rd_team_cn_name,	        			  -- 重量级团队LV1中文描述
		       t3.lv1_prod_rd_team_en_name,	        			  -- 重量级团队LV1英文描述
		       t3.lv2_prod_rnd_team_code,	            			-- 重量级团队LV1编码
		       t3.lv2_prod_rd_team_cn_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv2_prod_rd_team_en_name,	        			  -- 重量级团队LV2中文描述
		       t3.lv3_prod_rnd_team_code,	            			-- 重量级团队LV3编码
		       t3.lv3_prod_rd_team_cn_name,	        			  -- 重量级团队LV3中文描述
		       t3.lv3_prod_rd_team_en_name,	        			  -- 重量级团队LV3英文描述
		       -- t4.oversea_flag,									            -- 海外标志（Y 海外、N 国内）
		         (case when t4.domestic_or_oversea_code = 'GH0002' then 'N'
				           when t4.domestic_or_oversea_code = 'GH0003' then 'Y'
				           when t4.domestic_or_oversea_code = 'GH0004' then 'OTH'
				      end) as oversea_flag, -- 海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)  -- 24年9月版修改为取 domestic_or_oversea_code
		       t8.report_item_l1_code,	     						    -- 报表项1级编码
		       t8.report_item_l1_cn_name,   					      -- 报表项1级中文名
		       t8.report_item_l2_code,	      						  -- 报表项2级编码
		       t8.report_item_l2_cn_name 	  						    -- 报表项2级中文名
 	    from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益（202305月版切的新表）
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
     where t1.period_id > 202312
       and t1.period_id < (to_char(current_date,'yyyymm'))   -- 取集成表period_id<系统当前年月的数据
    ;

    v_dml_row_count := sql%rowcount;   -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数：无，从2张集成表取period_id<系统当前年月的数据，fin_rpt_item_grp_f_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  end if;


  -- 取当前日期的最大版本数据，如果有最大版本，则版本号+1；如果没有最大版本，则版本编码=当前年月日_V1
  if((select to_char(current_date,'yyyymm')) = substr(v_current_max_version_code,1,6)) then
	  -- 版本编码=最大版本+1，数据入到目标表
	  insert into fin_dm_opt_fop.dm_fop_ict_pl_sum_t(            ---ICT损益汇总表
	         version_code,                                       -- 版本编码
	         period_id,									                         ---会计期
	         prod_key,                                           ---产品KEY
	         prod_code,	                                         ---产品编码
	         prod_cn_name,                                       ---产品名称
	         prod_en_name,                                       ---产品英文名称
           geo_pc_key,                                         ---区域责任中心KEY
           region_code,                                        ---地区部编码  
           region_cn_name,                                     ---地区部中文名称
           region_en_name,                                     ---地区部英文名称
           rep_office_code,                                    ---代表处编码  
           rep_office_cn_name,                                 ---代表处中文名称
           rep_office_en_name,                                 ---代表处英文名称
	         rmb_fact_ex_rate_ptd_amt,                           ---人民币实际汇率当期发生额
	         usd_fact_ex_rate_ptd_amt,                           ---美元实际汇率当期发生额
	         bg_code,                                            ---BG编码
	         bg_name,                                            ---BG中文描述
	         bg_en_name,                                         ---BG英文描述
	         lv0_prod_rnd_team_code,	                           ---重量级团队LV0编码
	         lv0_prod_rd_team_cn_name,                           ---重量级团队LV0中文描述
	         lv0_prod_rd_team_en_name,                           ---重量级团队LV0英文描述
	         lv1_prod_rnd_team_code,	                           ---重量级团队LV1编码
	         lv1_prod_rd_team_cn_name,                           ---重量级团队LV1中文描述
	         lv1_prod_rd_team_en_name,                           ---重量级团队LV1英文描述
	         lv2_prod_rnd_team_code,	                           ---重量级团队LV1编码
	         lv2_prod_rd_team_cn_name,                           ---重量级团队LV2中文描述
	         lv2_prod_rd_team_en_name,                           ---重量级团队LV2中文描述
	         lv3_prod_rnd_team_code,	                           ---重量级团队LV3编码
	         lv3_prod_rd_team_cn_name,                           ---重量级团队LV3中文描述
	         lv3_prod_rd_team_en_name,                           ---重量级团队LV3英文描述
	         oversea_flag,                                       ---海外标志
	         report_item_l1_code,	     						               ---报表项1级编码
	         report_item_l1_cn_name,   					 	               ---报表项1级中文名
	         report_item_l2_code,	      						             ---报表项2级编码
	         report_item_l2_cn_name,	  						             ---报表项2级中文名
	         remark,                                             ---备注
	         created_by,                                         ---创建人
	         creation_date,                                      ---创建时间
	         last_updated_by,                                    ---修改人
	         last_update_date,                                   ---修改时间
	         del_flag                                            ---是否删除
	  )
	  select (substr(v_current_max_version_code,1,8)||(substr(v_current_max_version_code,9)+1)) as version_code, -- 202301_V1
	         period_id,
	         prod_key,
	         prod_code,
	         prod_cn_name,
	         prod_en_name,                                       ---产品英文名称
           geo_pc_key,                                         ---区域责任中心KEY
           region_code,                                        ---地区部编码  
           region_cn_name,                                     ---地区部中文名称
           region_en_name,                                     ---地区部英文名称
           rep_office_code,                                    ---代表处编码  
           rep_office_cn_name,                                 ---代表处中文名称
           rep_office_en_name,                                 ---代表处英文名称
	         rmb_fact_ex_rate_ptd_amt,
	         usd_fact_ex_rate_ptd_amt,
	         bg_code,
	         bg_name,
	         bg_en_name,
	         lv0_prod_rnd_team_code,
	         lv0_prod_rd_team_cn_name,
	         lv0_prod_rd_team_en_name,
	         lv1_prod_rnd_team_code,
	         lv1_prod_rd_team_cn_name,
	         lv1_prod_rd_team_en_name,
	         lv2_prod_rnd_team_code,
	         lv2_prod_rd_team_cn_name,
	         lv2_prod_rd_team_en_name,
	         lv3_prod_rnd_team_code,
	         lv3_prod_rd_team_cn_name,
	         lv3_prod_rd_team_en_name,
	         oversea_flag,
	         report_item_l1_code,
	         report_item_l1_cn_name,
	         report_item_l2_code,
	         report_item_l2_cn_name,
	         '' as remark,
	  	     -1 as created_by,
	  	     current_timestamp as creation_date,
	  	     -1 as last_updated_by,
	  	     current_timestamp as last_update_date,
	  	     'N' as del_flag
	    from fin_rpt_item_grp_f_tmp1
	  ;

	  v_dml_row_count := sql%rowcount;   -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码：'||(substr(v_current_max_version_code,1,8)||(substr(v_current_max_version_code,9)+1))||'，dm_fop_ict_pl_sum_t 目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  else
      -- 版本编码=当前年月日_V1，数据入到目标表
	    insert into fin_dm_opt_fop.dm_fop_ict_pl_sum_t(                 ---ICT损益汇总表
	           version_code,                                       -- 版本编码
	           period_id,									                        ---会计期
	           prod_key,                                           ---产品KEY
	           prod_code,	                                        ---产品编码
	           prod_cn_name,                                       ---产品名称
	           prod_en_name,                                       ---产品英文名称
             geo_pc_key,                                         ---区域责任中心KEY
             region_code,                                        ---地区部编码  
             region_cn_name,                                     ---地区部中文名称
             region_en_name,                                     ---地区部英文名称
             rep_office_code,                                    ---代表处编码  
             rep_office_cn_name,                                 ---代表处中文名称
             rep_office_en_name,                                 ---代表处英文名称
	           rmb_fact_ex_rate_ptd_amt,                           ---人民币实际汇率当期发生额
	           usd_fact_ex_rate_ptd_amt,                           ---美元实际汇率当期发生额
	           bg_code,                                            ---BG编码
	           bg_name,                                            ---BG中文描述
	           bg_en_name,                                          ---BG中文描述
	           lv0_prod_rnd_team_code,	                            ---重量级团队LV0编码
	           lv0_prod_rd_team_cn_name,                           ---重量级团队LV0中文描述
	           lv0_prod_rd_team_en_name,                           ---重量级团队LV0英文描述
	           lv1_prod_rnd_team_code,	                            ---重量级团队LV1编码
	           lv1_prod_rd_team_cn_name,                           ---重量级团队LV1中文描述
	           lv1_prod_rd_team_en_name,                           ---重量级团队LV1英文描述
	           lv2_prod_rnd_team_code,	                            ---重量级团队LV1编码
	           lv2_prod_rd_team_cn_name,                           ---重量级团队LV2中文描述
	           lv2_prod_rd_team_en_name,                           ---重量级团队LV2中文描述
	           lv3_prod_rnd_team_code,	                            ---重量级团队LV3编码
	           lv3_prod_rd_team_cn_name,                           ---重量级团队LV3中文描述
	           lv3_prod_rd_team_en_name,                           ---重量级团队LV3英文描述
	           oversea_flag,                                       ---海外标志
	           report_item_l1_code,	     						              ---报表项1级编码
	           report_item_l1_cn_name,   					 	              ---报表项1级中文名
	           report_item_l2_code,	      						            ---报表项2级编码
	           report_item_l2_cn_name,	  						              ---报表项2级中文名
	           remark,                                             ---备注
	           created_by,                                         ---创建人
	           creation_date,                                      ---创建时间
	           last_updated_by,                                    ---修改人
	           last_update_date,                                   ---修改时间
	           del_flag                                            ---是否删除
	    )
	    select ((select to_char(current_date,'yyyymm'))||'_'||'V1') as version_code,
	           period_id,
	           prod_key,
	           prod_code,
	           prod_cn_name,
	           prod_en_name,                                       ---产品英文名称
             geo_pc_key,                                         ---区域责任中心KEY
             region_code,                                        ---地区部编码  
             region_cn_name,                                     ---地区部中文名称
             region_en_name,                                     ---地区部英文名称
             rep_office_code,                                    ---代表处编码  
             rep_office_cn_name,                                 ---代表处中文名称
             rep_office_en_name,                                 ---代表处英文名称
	           rmb_fact_ex_rate_ptd_amt,
	           usd_fact_ex_rate_ptd_amt,
	           bg_code,
	           bg_name,
	           bg_en_name,
	           lv0_prod_rnd_team_code,
	           lv0_prod_rd_team_cn_name,
	           lv0_prod_rd_team_en_name,
	           lv1_prod_rnd_team_code,
	           lv1_prod_rd_team_cn_name,
	           lv1_prod_rd_team_en_name,
	           lv2_prod_rnd_team_code,
	           lv2_prod_rd_team_cn_name,
	           lv2_prod_rd_team_en_name,
	           lv3_prod_rnd_team_code,
	           lv3_prod_rd_team_cn_name,
	           lv3_prod_rd_team_en_name,
	           oversea_flag,
	           report_item_l1_code,
	           report_item_l1_cn_name,
	           report_item_l2_code,
	           report_item_l2_cn_name,
	           '' as remark,
	    	     -1 as created_by,
	    	     current_timestamp as creation_date,
	    	     -1 as last_updated_by,
	    	     current_timestamp as last_update_date,
	    	     'N' as del_flag
	      from fin_rpt_item_grp_f_tmp1
      ;

      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => '生成的版本编码：'||((select to_char(current_date,'yyyymm'))||'_'||'V1')||'，dm_fop_ict_pl_sum_t 目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
 end if;


  exception
    	when others then

        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
          p_log_formula_sql_txt => sqlerrm,--错误信息
          p_log_errbuf => sqlstate  --错误编码
        ) ;
  	x_success_flag := '2001';		        --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_fop.dm_fop_ict_pl_sum_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

