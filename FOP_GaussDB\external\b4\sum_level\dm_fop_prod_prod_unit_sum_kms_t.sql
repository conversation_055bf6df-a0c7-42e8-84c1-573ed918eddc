--Step 2：数据提取

标签名称： dm_dim_product_d_tmp   数据源：fin_dm_opt_fop_uat
select distinct prod_key
     , prod_code
     , prod_cn_name
     , lv0_prod_list_code         as bg_code
     , lv0_prod_list_cn_name      as bg_name
     , lv0_prod_rnd_team_code
     , lv0_prod_rd_team_cn_name
     , lv0_prod_rd_team_en_name
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv1_prod_rd_team_en_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , lv2_prod_rd_team_en_name
     , lv3_prod_rnd_team_code
     , lv3_prod_rd_team_cn_name
     , lv3_prod_rd_team_en_name
     , prod_en_name
     , lv0_prod_list_en_name as bg_en_name
  from dmdim.dm_dim_product_d
 where del_flag = 'N'
;

标签名称： dwr_dim_material_d_tmp   数据源：fin_dm_opt_fop_uat
select material_code
     , scd_active_begin_date
     , master_org_material_cn_desc
     , row_number()over() as sort_id
  from dwrdim.dwr_dim_material_d
 where scd_active_ind = 1
   and master_org_material_cn_desc is not null
   and master_org_material_cn_desc <> '源为空'
   and master_org_material_cn_desc <> '--'
   and del_flag = 'N'
;

标签名称： dm_dim_region_rc_d_tmp   数据源：fin_dm_opt_fop_uat
select distinct cast(geo_pc_key as numeric(38,0)) as geo_pc_key
     , domestic_or_oversea_code
     , domestic_or_oversea_cname
     , domestic_or_oversea_ename
     , region_code
     , region_cn_name
     , region_en_name
     , repoffice_code as rep_office_code
     , repoffice_cn_name as rep_office_cn_name
     , repoffice_en_name as rep_office_en_name
  from dmdim.dm_dim_region_rc_d
 where del_flag = 'N'
;

标签名称： fop_dwl_prod_prod_unit_i_tmp   数据源：fin_dm_opt_fop_uat
select period_id
     , spart_code
     , p_flag
     , scenario
     , prod_key
     , prod_code
     , geo_pc_key
     , dimension_key
     , part_qty
     , rmb_fact_rate_amt
     , usd_fact_rate_amt
  from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i
 where period_id < to_char(current_date,'yyyymm')
   and scenario <> 'GC'
;

标签名称： fop_dwl_prod_prod_unit_kms_i_tmp   数据源：fin_dm_opt_fop_uat

select period_id
     , spart_code
     , p_flag
     , scenario
     , prod_key
     , prod_code
     , geo_pc_key
     , dimension_key
     , part_qty
     , rmb_fact_rate_amt
     , usd_fact_rate_amt
  from fin_dm_opt_fop.fop_dwl_prod_prod_unit_kms_i
 where period_id < to_char(current_date,'yyyymm')
;

-- Step 3：SQL-Script
cache lazy table material_tamp1 as
select
  material_code,
  scd_active_begin_date,
  master_org_material_cn_desc,
  row_number() over (
    partition by
      material_code
    order by
      scd_active_begin_date desc,
      master_org_material_cn_desc
  ) as rn
from
  dwr_dim_material_d_tmp;


cache lazy table material_tamp2 as
select
  cast(
    (
      case
        when length(material_code) < 8 then lpad(material_code, 8, '0')
        else material_code
      end
    ) as varchar(500)
  ) as material_code,
  scd_active_begin_date,
  master_org_material_cn_desc
from
  material_tamp1
where
  rn = 1;


cache lazy table prod_unit_kms_tmp as
select
  period_id,
  spart_code,
  p_flag,
  scenario,
  prod_key,
  prod_code,
  geo_pc_key,
  dimension_key,
  sum(part_qty) as part_qty,
  sum(rmb_fact_rate_amt) as rmb_fact_rate_amt,
  sum(usd_fact_rate_amt) as usd_fact_rate_amt
from
  fop_dwl_prod_prod_unit_kms_i_tmp
group by
  period_id,
  spart_code,
  p_flag,
  scenario,
  prod_key,
  prod_code,
  geo_pc_key,
  dimension_key;


cache lazy table prod_unit_tmp as
select
  cast(period_id as numeric(20, 0)) as period_id,
  cast(
    (
      case
        when length(spart_code) < 8 then lpad(spart_code, 8, '0')
        else spart_code
      end
    ) as varchar(500)
  ) as spart_code,
  p_flag,
  scenario,
  prod_key,
  prod_code,
  cast(geo_pc_key as numeric(38, 0)) as geo_pc_key,
  cast(dimension_key as numeric(38, 0)) as dimension_key,
  cast(part_qty as numeric(38, 10)) as part_qty,
  rmb_fact_rate_amt,
  usd_fact_rate_amt,
  row_number() over (
    partition by
      period_id,
      spart_code,
      p_flag,
      scenario,
      prod_key,
      prod_code,
      geo_pc_key
    order by
      dimension_key
  ) as rn
from
  fop_dwl_prod_prod_unit_i_tmp
union all
select
  cast(period_id as numeric(20, 0)) as period_id,
  cast(
    (
      case
        when length(spart_code) < 8 then lpad(spart_code, 8, '0')
        else spart_code
      end
    ) as varchar(500)
  ) as spart_code,
  p_flag,
  scenario,
  prod_key,
  prod_code,
  cast(geo_pc_key as numeric(38, 0)) as geo_pc_key,
  cast(dimension_key as numeric(38, 0)) as dimension_key,
  cast(part_qty as numeric(38, 10)) as part_qty,
  rmb_fact_rate_amt,
  usd_fact_rate_amt,
  row_number() over (
    partition by
      period_id,
      spart_code,
      p_flag,
      scenario,
      prod_key,
      prod_code,
      geo_pc_key
    order by
      dimension_key
  ) as rn
from
  prod_unit_kms_tmp;


cache lazy table prod_unit_tmp1 as
select
  '' as version_code,
  aa.period_id,
  cast(aa.prod_key as numeric(38, 0)) as prod_key,
  aa.geo_pc_key,
  cc.prod_code,
  cast(
    (
      case
        when upper(aa.scenario) = 'GC' then aa.rmb_fact_rate_amt
      end
    ) as varchar(5000)
  ) as rmb_cost,
  cast(
    (
      case
        when upper(aa.scenario) = 'GC' then aa.usd_fact_rate_amt
      end
    ) as varchar(5000)
  ) as usd_cost,
  (
    case
      when upper(aa.scenario) = 'REV_SPLIT' then aa.rmb_fact_rate_amt
    end
  ) as rmb_revenue,
  (
    case
      when upper(aa.scenario) = 'REV_SPLIT' then aa.usd_fact_rate_amt
    end
  ) as usd_revenue,
  (
    case
      when upper(aa.scenario) = 'REV' then aa.part_qty
    end
  ) as spart_qty,
  aa.spart_code,
  ee.master_org_material_cn_desc as spart_desc,
  cc.prod_cn_name,
  cc.bg_code,
  cc.bg_name,
  cc.lv0_prod_rnd_team_code,
  cc.lv0_prod_rd_team_cn_name,
  cc.lv0_prod_rd_team_en_name,
  cc.lv1_prod_rnd_team_code,
  cc.lv1_prod_rd_team_cn_name,
  cc.lv1_prod_rd_team_en_name,
  cc.lv2_prod_rnd_team_code,
  cc.lv2_prod_rd_team_cn_name,
  cc.lv2_prod_rd_team_en_name,
  cc.lv3_prod_rnd_team_code,
  cc.lv3_prod_rd_team_cn_name,
  cc.lv3_prod_rd_team_en_name,
  (case when dd.domestic_or_oversea_code = 'GH0002' then 'N'
				when dd.domestic_or_oversea_code = 'GH0003' then 'Y'
				when dd.domestic_or_oversea_code = 'GH0004' then 'OTH'
	 end) as oversea_flag, /*海外标志(GH0002	中国区、GH0003	海外、GH0004	其他)   24年9月版修改为取 domestic_or_oversea_code*/
  cc.prod_en_name,
  cc.bg_en_name,
  dd.region_code,
  dd.region_cn_name,
  dd.region_en_name,
  dd.rep_office_code,
  dd.rep_office_cn_name,
  dd.rep_office_en_name
from
  prod_unit_tmp aa
  left join dm_dim_product_d_tmp cc on aa.prod_key = cc.prod_key
  left join dm_dim_region_rc_d_tmp dd on aa.geo_pc_key = dd.geo_pc_key
  left join material_tamp2 ee on aa.spart_code = ee.material_code
where
  aa.rn = 1;


select
  cast(
    '${V_VERSION_CODE}' as varchar(100)
  ) as version_code,
  period_id,
  prod_key,
  geo_pc_key,
  prod_code,
  rmb_cost,
  usd_cost,
  rmb_revenue,
  usd_revenue,
  spart_qty,
  spart_code,
  spart_desc,
  prod_cn_name,
  bg_code,
  bg_name,
  lv0_prod_rnd_team_code,
  lv0_prod_rd_team_cn_name,
  lv0_prod_rd_team_en_name,
  lv1_prod_rnd_team_code,
  lv1_prod_rd_team_cn_name,
  lv1_prod_rd_team_en_name,
  lv2_prod_rnd_team_code,
  lv2_prod_rd_team_cn_name,
  lv2_prod_rd_team_en_name,
  lv3_prod_rnd_team_code,
  lv3_prod_rd_team_cn_name,
  lv3_prod_rd_team_en_name,
  oversea_flag,
  prod_en_name,
  bg_en_name,
  region_code,
  region_cn_name,
  region_en_name,
  rep_office_code,
  rep_office_cn_name,
  rep_office_en_name,
  '' as remark,
  -1 as created_by,
  current_timestamp as creation_date,
  -1 as last_updated_by,
  current_timestamp as last_update_date,
  'N' as del_flag
from
  prod_unit_tmp1;
  
  
-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                  目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_prod_prod_unit_sum_kms_t  模式：NO_DELETE
