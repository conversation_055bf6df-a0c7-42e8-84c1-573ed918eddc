-- Step 2：数据提取

标签名称：spart_detail_info_kms   数据源：fin_dm_opt_fop_uat
select t.version_code  
	       , t.period_id  
	  		 , t.spart_code 
	  		 , t.spart_desc 
	  		 , t.bg_code   
	  		 , t.bg_name
	  		 , t.bg_en_name             
	  		 , t.oversea_flag           
	  		 , t.lv1_prod_rnd_team_code 
	  		 , t.lv1_prod_rd_team_cn_name  
	  		 , t.lv1_prod_rd_team_en_name  
	  		 , t.lv2_prod_rnd_team_code    
	  		 , t.lv2_prod_rd_team_cn_name  
	  		 , t.lv2_prod_rd_team_en_name  
	  		 , t.l1_name                   
	  		 , t.l2_name                   
	  		 , t.coa_l2_name               
	  		 , t.l2_coefficient  
	  		 , t.articulation_flag
	  		 ,t.rmb_revenue  
	  		 , t.rmb_cost
         , t.usd_revenue
         , t.usd_cost
	  		 , t.spart_qty
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t 
	   where t.articulation_flag in('SCENO1','SCENO2','SCENO3') 
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = '${V_MAX_VERSION_CODE}' 
	     and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t') 
	     and t.del_flag = 'N'     
    ;
	
	-- Step 3：SQL-Script
	cache lazy table spart_detail_info_tmp1
  as
  select t.version_code
	       , cast(t.period_id as int) as period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name as bg_cn_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6) as l2_coefficient
	  		 , t.articulation_flag
	  		 , 'CNY' as currency
	  		 , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt
	  		 , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty
	    from spart_detail_info_kms t
     group by t.version_code
	       , cast(t.period_id as int)
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
	   union all
	  select t.version_code
	       , cast(t.period_id as int) as period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name as bg_cn_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6) as l2_coefficient
	  		 , t.articulation_flag
	  		 , 'USD' as currency
	  		 , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt
	  		 , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty
	    from spart_detail_info_kms t
     group by t.version_code
	       , cast(t.period_id as int)
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
   ;

  cache lazy table spart_detail_info_tmp
  as
  select t.version_code
         , t.period_id
         , t.spart_code
         , t.spart_desc
         , t.bg_code
         , t.bg_cn_name
         , t.bg_en_name
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
                 when t.oversea_flag = 'OTH' then '其他'
            end) as oversea_desc
         , t.lv1_prod_rnd_team_code
         , t.lv1_prod_rd_team_cn_name
         , t.lv1_prod_rd_team_en_name
         , t.lv2_prod_rnd_team_code
         , t.lv2_prod_rd_team_cn_name
         , t.lv2_prod_rd_team_en_name
         , t.l1_name
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name
         , t.l2_coefficient
         , t.currency
         , t.articulation_flag
         , t.equip_rev_cons_before_amt
         , t.equip_cost_cons_before_amt
         , t.spart_qty
      from spart_detail_info_tmp1 t
		;

	cache lazy table oversea_desc_temp
  as
  select version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		   , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)      as equip_rev_cons_before_amt
		   , sum(equip_cost_cons_before_amt)     as equip_cost_cons_before_amt
		   , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty
	  from spart_detail_info_tmp
     where oversea_desc is not null
   group by version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		   , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
union all
  select version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		   , '全球' as oversea_desc   /*全球=国内+海外+其他*/
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)      as equip_rev_cons_before_amt
		   , sum(equip_cost_cons_before_amt)     as equip_cost_cons_before_amt
		   , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty
	  from spart_detail_info_tmp
   group by version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   ;

		cache lazy table bg_name_temp
		as
		select version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , 'PROD0002' as bg_code
		   , 'ICT' as bg_cn_name
		   , 'ICT' as bg_en_name
		   , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt) as equip_rev_cons_before_amt
		   , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
		   , sum(spart_qty) as spart_qty
	  from oversea_desc_temp
   group by version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
   union all
  select version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
		   , bg_en_name
		   , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , equip_rev_cons_before_amt
		   , equip_cost_cons_before_amt
		   , spart_qty
	  from oversea_desc_temp
	  ;

	cache lazy table l2_temp
  as
  select version_code
	     , period_id
		   , bg_code
		   , oversea_desc
		   , lv1_prod_rnd_team_code
		   , lv2_prod_rnd_team_code
		   , l1_name
		   , l2_name
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
		   , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
		   , sum(spart_qty)                  as spart_qty
	  from bg_name_temp
   group by version_code
	     , period_id
		   , bg_code
		   , oversea_desc
		   , lv1_prod_rnd_team_code
		   , lv2_prod_rnd_team_code
		   , l1_name
		   , l2_name
		   , currency
		   , articulation_flag
		   ;

	cache lazy table all_temp_01
	as
	select t1.version_code
	     , t1.period_id
		   , t1.spart_code
	     , t1.spart_desc
		   , t1.bg_code
		   , t1.bg_cn_name
		   , t1.bg_en_name
		   , t1.oversea_desc
		   , t1.lv1_prod_rnd_team_code
	     , t1.lv1_prod_rd_team_cn_name
	     , t1.lv1_prod_rd_team_en_name
	     , t1.lv2_prod_rnd_team_code
	     , t1.lv2_prod_rd_team_cn_name
	     , t1.lv2_prod_rd_team_en_name
		   , t2.l1_name
		   , t1.l2_name
		   , t1.l2_coefficient
		   , t1.currency
		   , t1.articulation_flag
		   , t1.equip_rev_cons_before_amt
		   , t1.equip_cost_cons_before_amt
		   , t1.spart_qty
		   , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				       when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
				       else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt end) as rev_percent
		   , (case when nvl(t2.equip_cost_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				       when nvl(t2.equip_cost_cons_before_amt,0) = 0 then -999999
				       else t1.equip_cost_cons_before_amt / t2.equip_cost_cons_before_amt end) as cost_percent
		   , (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) = 0 then 0
				       when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) <> 0 then -999999
				       else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt end) as mgp_ratio
	  from bg_name_temp t1
    left join l2_temp t2
		  on t1.version_code = t2.version_code
		 and t1.period_id = t2.period_id
	   and t1.bg_code = t2.bg_code
	   and t1.oversea_desc = t2.oversea_desc
	   and t1.lv1_prod_rnd_team_code = t2.lv1_prod_rnd_team_code
	   and t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code
	   and t1.l1_name = t2.l1_name
	   and t1.l2_name = t2.l2_name
	   and t1.currency = t2.currency
	   and t1.articulation_flag = t2.articulation_flag
	 where t1.l2_name is not null or t1.l2_name <> ''
	 ;

	 cache lazy table all_temp
	as
	select t1.version_code
	     , t1.period_id
		   , t1.spart_code
	     , t1.spart_desc
		   , t1.bg_code
		   , t1.bg_cn_name
		   , t1.bg_en_name
		   , t1.oversea_desc
		   , t1.lv1_prod_rnd_team_code
	     , t1.lv1_prod_rd_team_cn_name
	     , t1.lv1_prod_rd_team_en_name
	     , t1.lv2_prod_rnd_team_code
	     , t1.lv2_prod_rd_team_cn_name
	     , t1.lv2_prod_rd_team_en_name
		   , t1.l1_name
		   , t1.l2_name
		   , t1.l2_coefficient
		   , t1.currency
		   , t1.articulation_flag
		   , t1.equip_rev_cons_before_amt
		   , t1.equip_cost_cons_before_amt
		   , t1.spart_qty
		   , (case when nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
	             when nvl(t1.spart_qty,0) = 0 then -999999
	             else t1.equip_cost_cons_before_amt / t1.spart_qty end) as avg_cost
		   , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
               when nvl(t1.spart_qty,0) = 0 then -999999
               else t1.equip_rev_cons_before_amt / t1.spart_qty end) as avg_price
		   , t1.rev_percent
		   , t1.cost_percent
		   , t1.mgp_ratio
    from all_temp_01 t1
   where nvl(t1.equip_rev_cons_before_amt,0) > 0
      or nvl(t1.equip_rev_cons_before_amt,0) < 0
			or nvl(t1.equip_cost_cons_before_amt,0) > 0
			or nvl(t1.equip_cost_cons_before_amt,0) < 0
			or nvl(t1.spart_qty,0) > 0
			or nvl(t1.spart_qty,0) < 0
			;

	select version_code
       , period_id
       , spart_code
       , spart_desc
       , bg_code
       , bg_cn_name
       , bg_en_name
       , oversea_desc
       , lv1_prod_rnd_team_code
       , lv1_prod_rd_team_cn_name
       , lv1_prod_rd_team_en_name
       , lv2_prod_rnd_team_code
       , lv2_prod_rd_team_cn_name
       , lv2_prod_rd_team_en_name
       , l1_name
       , l2_name
       , l2_coefficient
       , currency
       , equip_rev_cons_before_amt
       , equip_cost_cons_before_amt
       , spart_qty
       , avg_cost
       , avg_price
       , rev_percent
       , cost_percent
       , mgp_ratio
       , articulation_flag
       , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	  from all_temp

	;
	
	-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                  目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_data_kms_t          模式：TRUNCATE_TABLE


数据源：fin_dm_opt_fop_uat                  目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_data_his_kms_t      模式：DELETE
                                            删除条件：version_code = '${V_MAX_VERSION_CODE}'
	
