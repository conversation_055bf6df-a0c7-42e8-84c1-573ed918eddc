-- ----------------------------
-- Function structure for f_dm_fop_spart_relation_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_relation_info_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_relation_info_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-10-13
创建人  ：鲁广武
更新时间：2023-02-08
更新人  ：鲁广武  lwx1186472
背景描述：Spart与作业对象关系表(提供给知识表示),然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_relation_info_t('202302_V1')
修改记录：2023-02-08 lwx1186472   新增版本参数
          传入版本参数，有值：传入值格式：f_dm_fop_spart_relation_info_t('202302_V1') 
          传入版本参数，无值：传入值格式：f_dm_fop_spart_relation_info_t()
          2023-04-19 lwx1186472   修改取最大版本逻辑   
          2023-12-28 qwx1110218 只提供“分析场景”=Y的数据给知识表示打标签                  
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_relation_info_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_relation_info_t';
	v_tbl_name2 varchar(50) := 'fin_dm_opt_fop.dm_fop_spart_relation_info_his_t';	
	v_max_version_code varchar(50);  -- 宽表的最大版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;


begin
  set enable_force_vector_engine to on;
	x_success_flag := '1';      --1表示成功
	
	
	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'Spart与作业对象关系表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的版本编码:'||p_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
  ---判断p_version_code在宽表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t where version_code = p_version_code)
   then  
     ---删除重跑目标表中已有传入参数的数据
	delete from fin_dm_opt_fop.dm_fop_spart_relation_info_his_t where version_code = p_version_code;    	  

  -- his_t目标表取宽表传入参数的数据
	insert into fin_dm_opt_fop.dm_fop_spart_relation_info_his_t    
	(
	 version_code,                      --版本
	 period_id,							---会计期
	 item_code,                         ---ITEM编码
	 item_desc,                         ---ITEM描述
	 lv1_code,                          ---重量级团队LV1编码
	 lv1_name,                          ---重量级团队LV1描述
	 l1_name,                           ---L1名称
	 l2_name,                           ---L2名称
	 l3_name,                           ---L3名称
	 l1_coefficient,                    ---L1系数
	 l2_coefficient,                    ---L2系数
	 l3_coefficient,                    ---L3系数
	 data_type,                         ---数据类型（Manual、AI、Adjust、New）
	 remark,                                           ---备注
	 created_by,                                       ---创建人
	 creation_date,                                    ---创建时间
	 last_updated_by,                                  ---修改人
	 last_update_date,                                 ---修改时间
	 del_flag                                          ---是否删除
	 )	

	---宽表处理成临时表，从宽表取New类型数据
	with spart_detail_tamp as (
	select
	t.version_code,                                   --版本
	t.period_id,                                      ---会计期
	t.item_code,                                      ---ITEM编码
	t.item_desc,                                      ---ITEM描述
	t.lv1_code,                                       ---重量级团队LV1编码
	t.lv1_name,                                       ---重量级团队LV1描述
	t.l1_name,                                        ---L1名称
	t.l2_name,                                        ---L2名称
	t.l3_name,                                        ---L3名称
	t.l1_coefficient,                                 ---L1系数
	t.l2_coefficient,                                 ---L2系数
	t.l3_coefficient,                                 ---L3系数
	t.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	t.source_table                                    ---来源表
	from (
	select
	t1.version_code,                                   --版本
	substring(regexp_replace(current_date,'-',''),1,6)::numeric as period_id,   ---会计期处理成当前年月
	t1.spart_code as item_code,                        ---ITEM编码
	t1.spart_desc as item_desc,                        ---ITEM描述
	t1.lv1_prod_rnd_team_code as lv1_code,             ---重量级团队LV1编码
	t1.lv1_prod_rd_team_cn_name as lv1_name,           ---重量级团队LV1描述
	t1.l1_name,                                        ---L1名称
	t1.l2_name,                                        ---L2名称
	t1.l3_name,                                        ---L3名称
	t1.l1_coefficient,                                 ---L1系数
	t1.l2_coefficient,                                 ---L2系数
	t1.l3_coefficient,                                 ---L3系数
	t1.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	t1.source_table,                                   ---来源表
	row_number()over(partition by t1.spart_code,
								                t1.lv1_prod_rnd_team_code,
								                t1.l1_name,
								                t1.data_type
						           order by t1.spart_desc) as rn    ---去重
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1           ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
 where t1.version_code = p_version_code               --取宽表中传入的版本编码
   and t1.del_flag = 'N'
   and upper(t1.data_type) = 'NEW'
	 and t1.source_table in ('fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t','fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t')  ---取收入与发货时点
	 and (t1.l1_name is not null or t1.l1_name <> '')
	 and t1.analysis_flag = 'Y'   -- 取“分析场景”是“Y”的数据
	) t
 where t.rn = 1                                           ---取第一条(不为空)
 union all
 -- 从Spart对象关系表（提供给前台的表）取最大会计期的His数据
 select t.version_code,                                   --版本
	      t.period_id,                                      ---会计期
	      t.item_code,                                      ---ITEM编码
	      t.item_desc,                                      ---ITEM描述
	      t.lv1_code,                                       ---重量级团队LV1编码
	      t.lv1_name,                                       ---重量级团队LV1描述
	      t.l1_name,                                        ---L1名称
	      t.l2_name,                                        ---L2名称
	      t.l3_name,                                        ---L3名称
	      t.l1_coefficient,                                 ---L1系数
	      t.l2_coefficient,                                 ---L2系数
	      t.l3_coefficient,                                 ---L3系数
	      t.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	      t.source_table                                    ---来源表
   from (
         select p_version_code as version_code,                               --版本
	        t.period_id,                                      ---会计期
	        (case when length(t.item_code) < 8 then lpad(t.item_code,8,'0') else t.item_code end) as item_code, ---ITEM编码
	        t.item_desc,                                      ---ITEM描述
	        t.lv1_code,                                       ---重量级团队LV1编码
	        t.lv1_name,                                       ---重量级团队LV1描述
	        t.l1_name,                                        ---L1名称
	        t.l2_name,                                        ---L2名称
	        t.l3_name,                                        ---L3名称
	        t.l1_coefficient,                                 ---L1系数
	        t.l2_coefficient,                                 ---L2系数
	        t.l3_coefficient,                                 ---L3系数
	        'Manual' as data_type,                            ---数据类型（Manual、AI、Adjust、New）
	        '' as source_table,                                ---来源表
	        row_number()over(partition by (case when length(t.item_code) < 8 then lpad(t.item_code,8,'0') else t.item_code end),
	        							                t.lv1_code,
	        							                t.l1_name
	        					           order by t.item_desc) as rn    ---去重
          from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t t  -- Spart对象与L1、L2、L3关系表
          where t.period_id = (select max(period_id) from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t t)  -- 取最大会计期
            and upper(t.status) = 'SUBMIT' 
            and upper(t.data_type) = 'HIS'
            and update_flag = 'N'
            and del_flag = 'N' 
        ) t
   where t.rn = 1
   ),  
	---物料维表处理成临时表,对物料维表spart编码不足8位的，需要前面补0
	  material_tamp as (
	select
	case when length(t1.material_code) < 8 then lpad(t1.material_code,8,'0') else t1.material_code end as material_code, ---物料编码
	t1.scd_active_begin_date,
	t1.master_org_material_cn_desc
	from (
	select
	t.material_code,
	t.scd_active_begin_date,
	t.master_org_material_cn_desc,
	row_number()over(partition by t.material_code
					             order by t.scd_active_begin_date desc,
					                      t.master_org_material_cn_desc) as rn   ---取最大的 scd_active_begin_date 
	from dwrdim.dwr_dim_material_d t                                     ---物料维表
 where t.scd_active_ind = 1
	 and t.master_org_material_cn_desc is not null                       ---排除空值
	 and t.master_org_material_cn_desc <> '源为空'                       ---排除'源为空'
	 and t.master_org_material_cn_desc <> '--'							             ---排除'--'
	 and t.del_flag = 'N'
	) t1
 where t1.rn = 1                                                       ---取第一条最大的
	)

	select t1.version_code,--版本
	       t1.period_id,                                      ---会计期
	       t1.item_code,                                      ---ITEM编码
	       (case when (t1.item_desc is null or t1.item_desc='') then t2.master_org_material_cn_desc else t1.item_desc end) as item_desc,       ---ITEM描述
	       t1.lv1_code,                                       ---重量级团队LV1编码
	       t1.lv1_name,                                       ---重量级团队LV1描述
	       t1.l1_name,                                        ---L1名称
	       t1.l2_name,                                        ---L2名称
	       t1.l3_name,                                        ---L3名称
	       t1.l1_coefficient,                                 ---L1系数
	       t1.l2_coefficient,                                 ---L2系数
	       t1.l3_coefficient,                                 ---L3系数
	       t1.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
		   '' as remark,							---备注
		   -1 as created_by,                       ---创建人
		   current_timestamp as creation_date,     ---创建时间
		   -1 as last_updated_by,                  ---修改人
		   current_timestamp as last_update_date,  ---修改时间
		   'N' as del_flag                         ---是否删除
	  from spart_detail_tamp t1                          ---SPART明细+L2~L3/L1~L3系数+type类型(宽表临时表)
 left join material_tamp t2                             ---物料维表（临时表）
	    on t1.item_code = t2.material_code                ---物料编码
     where t1.data_type is not null
      ;

	v_dml_row_count := sql%rowcount;		--- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>2,
        p_log_cal_log_desc => 'Spart与作业对象关系表'||v_tbl_name2||',传入的版本编码:'||p_version_code||',数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;	  


     ---删除重跑目标表中的数据，只留一个版本的数据
	truncate table fin_dm_opt_fop.dm_fop_spart_relation_info_t;

  -- 目标表取his_t表传入参数的数据
	insert into fin_dm_opt_fop.dm_fop_spart_relation_info_t    
	(
	 period_id,							---会计期
	 item_code,                         ---ITEM编码
	 item_desc,                         ---ITEM描述
	 lv1_code,                          ---重量级团队LV1编码
	 lv1_name,                          ---重量级团队LV1描述
	 l1_name,                           ---L1名称
	 l2_name,                           ---L2名称
	 l3_name,                           ---L3名称
	 l1_coefficient,                    ---L1系数
	 l2_coefficient,                    ---L2系数
	 l3_coefficient,                    ---L3系数
	 data_type,                         ---数据类型（Manual、AI、Adjust、New）
	 remark,                                           ---备注
	 created_by,                                       ---创建人
	 creation_date,                                    ---创建时间
	 last_updated_by,                                  ---修改人
	 last_update_date,                                 ---修改时间
	 del_flag                                          ---是否删除
	 )	
	select distinct period_id,                                      ---会计期
	       item_code,                                      ---ITEM编码
	       item_desc,                                      ---ITEM描述
	       lv1_code,                                       ---重量级团队LV1编码
	       lv1_name,                                       ---重量级团队LV1描述
	       l1_name,                                        ---L1名称
	       l2_name,                                        ---L2名称
	       l3_name,                                        ---L3名称
	       l1_coefficient,                                 ---L1系数
	       l2_coefficient,                                 ---L2系数
	       l3_coefficient,                                 ---L3系数
	       data_type,                                      ---数据类型（Manual、AI、Adjust、New）
		   '' as remark,							---备注
		   -1 as created_by,                       ---创建人
		   current_timestamp as creation_date,     ---创建时间
		   -1 as last_updated_by,                  ---修改人
		   current_timestamp as last_update_date,  ---修改时间
		   'N' as del_flag                         ---是否删除
	  from fin_dm_opt_fop.dm_fop_spart_relation_info_his_t
     where version_code = p_version_code      --his_t表传入版本号	  
  ;

	v_dml_row_count := sql%rowcount;		--- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => 'Spart与作业对象关系表'||v_tbl_name||',传入的版本编码:'||p_version_code||',数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  else
		
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => '宽表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;
	  
--当p_version_code无值时，取最大的版本号
else

    --取宽表最大版本号
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code 
	  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t)
     group by substr(version_code,1,6);  -- 取宽表最大版本号

     ---删除重跑目标表中最大的版本号的数据
	delete from fin_dm_opt_fop.dm_fop_spart_relation_info_his_t where version_code = v_max_version_code;
	
  -- his_t目标表取宽表最大版本数据
	insert into fin_dm_opt_fop.dm_fop_spart_relation_info_his_t    
	(
	 version_code,                      --版本
	 period_id,							---会计期
	 item_code,                         ---ITEM编码
	 item_desc,                         ---ITEM描述
	 lv1_code,                          ---重量级团队LV1编码
	 lv1_name,                          ---重量级团队LV1描述
	 l1_name,                           ---L1名称
	 l2_name,                           ---L2名称
	 l3_name,                           ---L3名称
	 l1_coefficient,                    ---L1系数
	 l2_coefficient,                    ---L2系数
	 l3_coefficient,                    ---L3系数
	 data_type,                         ---数据类型（Manual、AI、Adjust、New）
	 remark,                                           ---备注
	 created_by,                                       ---创建人
	 creation_date,                                    ---创建时间
	 last_updated_by,                                  ---修改人
	 last_update_date,                                 ---修改时间
	 del_flag                                          ---是否删除
	 )	

	---宽表处理成临时表，从宽表取New类型数据
	with spart_detail_tamp as (
	select
	t.version_code,                                   --版本
	t.period_id,                                      ---会计期
	t.item_code,                                      ---ITEM编码
	t.item_desc,                                      ---ITEM描述
	t.lv1_code,                                       ---重量级团队LV1编码
	t.lv1_name,                                       ---重量级团队LV1描述
	t.l1_name,                                        ---L1名称
	t.l2_name,                                        ---L2名称
	t.l3_name,                                        ---L3名称
	t.l1_coefficient,                                 ---L1系数
	t.l2_coefficient,                                 ---L2系数
	t.l3_coefficient,                                 ---L3系数
	t.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	t.source_table                                    ---来源表
	from (
	select
	t1.version_code,                                   --版本
	substring(regexp_replace(current_date,'-',''),1,6)::numeric as period_id,   ---会计期处理成当前年月
	t1.spart_code as item_code,                        ---ITEM编码
	t1.spart_desc as item_desc,                        ---ITEM描述
	t1.lv1_prod_rnd_team_code as lv1_code,             ---重量级团队LV1编码
	t1.lv1_prod_rd_team_cn_name as lv1_name,           ---重量级团队LV1描述
	t1.l1_name,                                        ---L1名称
	t1.l2_name,                                        ---L2名称
	t1.l3_name,                                        ---L3名称
	t1.l1_coefficient,                                 ---L1系数
	t1.l2_coefficient,                                 ---L2系数
	t1.l3_coefficient,                                 ---L3系数
	t1.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	t1.source_table,                                   ---来源表
	row_number()over(partition by t1.spart_code,
								                t1.lv1_prod_rnd_team_code,
								                t1.l1_name,
								                t1.data_type
						           order by t1.spart_desc) as rn    ---去重
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1           ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
 where t1.version_code = v_max_version_code               --取宽表中最大的版本号
   and t1.del_flag = 'N'
   and upper(t1.data_type) = 'NEW'
	 and t1.source_table in ('fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t','fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t')  ---取收入与发货时点
	 and (t1.l1_name is not null or t1.l1_name <> '')
	 and t1.analysis_flag = 'Y'   -- 取“分析场景”是“Y”的数据
	) t
 where t.rn = 1                                           ---取第一条(不为空)
 union all
 -- 从Spart对象关系表（提供给前台的表）取最大会计期的His数据
 select t.version_code,                                   --版本
	      t.period_id,                                      ---会计期
	      t.item_code,                                      ---ITEM编码
	      t.item_desc,                                      ---ITEM描述
	      t.lv1_code,                                       ---重量级团队LV1编码
	      t.lv1_name,                                       ---重量级团队LV1描述
	      t.l1_name,                                        ---L1名称
	      t.l2_name,                                        ---L2名称
	      t.l3_name,                                        ---L3名称
	      t.l1_coefficient,                                 ---L1系数
	      t.l2_coefficient,                                 ---L2系数
	      t.l3_coefficient,                                 ---L3系数
	      t.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
	      t.source_table                                    ---来源表
   from (
          select
             v_max_version_code as version_code,                               --版本
	         t.period_id,                                      ---会计期
	         (case when length(t.item_code) < 8 then lpad(t.item_code,8,'0') else t.item_code end) as item_code, ---ITEM编码
	         t.item_desc,                                      ---ITEM描述
	         t.lv1_code,                                       ---重量级团队LV1编码
	         t.lv1_name,                                       ---重量级团队LV1描述
	         t.l1_name,                                        ---L1名称
	         t.l2_name,                                        ---L2名称
	         t.l3_name,                                        ---L3名称
	         t.l1_coefficient,                                 ---L1系数
	         t.l2_coefficient,                                 ---L2系数
	         t.l3_coefficient,                                 ---L3系数
	         'Manual' as data_type,                            ---数据类型（Manual、AI、Adjust、New）
	         '' as source_table,                                ---来源表
	         row_number()over(partition by (case when length(t.item_code) < 8 then lpad(t.item_code,8,'0') else t.item_code end),
	        							                t.lv1_code,
	        							                t.l1_name
	        					           order by t.item_desc) as rn    ---去重
           from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t t  -- Spart对象与L1、L2、L3关系表
           where t.period_id = (select max(period_id) from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t t)  -- 取最大会计期
             and upper(t.status) = 'SUBMIT' 
             and upper(t.data_type) = 'HIS'
             and update_flag = 'N'
             and del_flag = 'N' 
        )t
   where t.rn = 1
   ),  
	---物料维表处理成临时表,对物料维表spart编码不足8位的，需要前面补0
	  material_tamp as (
	select
	case when length(t1.material_code) < 8 then lpad(t1.material_code,8,'0') else t1.material_code end as material_code, ---物料编码
	t1.scd_active_begin_date,
	t1.master_org_material_cn_desc
	from (
	select
	t.material_code,
	t.scd_active_begin_date,
	t.master_org_material_cn_desc,
	row_number()over(partition by t.material_code
					             order by t.scd_active_begin_date desc,
					                      t.master_org_material_cn_desc) as rn   ---取最大的 scd_active_begin_date 
	from dwrdim.dwr_dim_material_d t                                     ---物料维表
 where t.scd_active_ind = 1
	 and t.master_org_material_cn_desc is not null                       ---排除空值
	 and t.master_org_material_cn_desc <> '源为空'                       ---排除'源为空'
	 and t.master_org_material_cn_desc <> '--'							             ---排除'--'
	 and t.del_flag = 'N'
	) t1
 where t1.rn = 1                                                       ---取第一条最大的
	)

	select t1.version_code,--版本
	       t1.period_id,                                      ---会计期
	       t1.item_code,                                      ---ITEM编码
	       (case when (t1.item_desc is null or t1.item_desc='') then t2.master_org_material_cn_desc else t1.item_desc end) as item_desc,       ---ITEM描述
	       t1.lv1_code,                                       ---重量级团队LV1编码
	       t1.lv1_name,                                       ---重量级团队LV1描述
	       t1.l1_name,                                        ---L1名称
	       t1.l2_name,                                        ---L2名称
	       t1.l3_name,                                        ---L3名称
	       t1.l1_coefficient,                                 ---L1系数
	       t1.l2_coefficient,                                 ---L2系数
	       t1.l3_coefficient,                                 ---L3系数
	       t1.data_type,                                      ---数据类型（Manual、AI、Adjust、New）
		   '' as remark,							---备注
		   -1 as created_by,                       ---创建人
		   current_timestamp as creation_date,     ---创建时间
		   -1 as last_updated_by,                  ---修改人
		   current_timestamp as last_update_date,  ---修改时间
		   'N' as del_flag                         ---是否删除
	  from spart_detail_tamp t1                          ---SPART明细+L2~L3/L1~L3系数+type类型(宽表临时表)
 left join material_tamp t2                             ---物料维表（临时表）
	    on t1.item_code = t2.material_code                ---物料编码
     where t1.data_type is not null
      ;

	v_dml_row_count := sql%rowcount;		--- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>2,
        p_log_cal_log_desc => 'Spart与作业对象关系表'||v_tbl_name2||',宽表中最大的版本编码:'||v_max_version_code||',数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    --从his_t目标表中取最大版本号
    ---删除重跑目标表中的数据，只留一个版本的数据
	truncate table fin_dm_opt_fop.dm_fop_spart_relation_info_t;

  -- 目标表取his_t表最大版本数据
	insert into fin_dm_opt_fop.dm_fop_spart_relation_info_t    
	(
	 period_id,							---会计期
	 item_code,                         ---ITEM编码
	 item_desc,                         ---ITEM描述
	 lv1_code,                          ---重量级团队LV1编码
	 lv1_name,                          ---重量级团队LV1描述
	 l1_name,                           ---L1名称
	 l2_name,                           ---L2名称
	 l3_name,                           ---L3名称
	 l1_coefficient,                    ---L1系数
	 l2_coefficient,                    ---L2系数
	 l3_coefficient,                    ---L3系数
	 data_type,                         ---数据类型（Manual、AI、Adjust、New）
	 remark,                                           ---备注
	 created_by,                                       ---创建人
	 creation_date,                                    ---创建时间
	 last_updated_by,                                  ---修改人
	 last_update_date,                                 ---修改时间
	 del_flag                                          ---是否删除
	 )	
	select distinct period_id,                                      ---会计期
	       item_code,                                      ---ITEM编码
	       item_desc,                                      ---ITEM描述
	       lv1_code,                                       ---重量级团队LV1编码
	       lv1_name,                                       ---重量级团队LV1描述
	       l1_name,                                        ---L1名称
	       l2_name,                                        ---L2名称
	       l3_name,                                        ---L3名称
	       l1_coefficient,                                 ---L1系数
	       l2_coefficient,                                 ---L2系数
	       l3_coefficient,                                 ---L3系数
	       data_type,                                      ---数据类型（Manual、AI、Adjust、New）
		   '' as remark,							---备注
		   -1 as created_by,                       ---创建人
		   current_timestamp as creation_date,     ---创建时间
		   -1 as last_updated_by,                  ---修改人
		   current_timestamp as last_update_date,  ---修改时间
		   'N' as del_flag                         ---是否删除
	  from fin_dm_opt_fop.dm_fop_spart_relation_info_his_t
     where version_code = v_max_version_code      --his_t表最大版本号	  
  ;

	v_dml_row_count := sql%rowcount;		--- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => 'Spart与作业对象关系表'||v_tbl_name||',最大的版本编码:'||v_max_version_code||',数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

end if
    ;

	
exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	--2001表示失败
	
    --收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_relation_info_his_t;
    analyse fin_dm_opt_fop.dm_fop_spart_relation_info_t;	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

