-- ----------------------------
-- Table structure for dm_ed_chnl_bci_cn_phone_w_v
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_ed_chnl_bci_cn_phone_w_v";
CREATE TABLE "fin_dm_opt_fop"."dm_ed_chnl_bci_cn_phone_w_v" (
  "period_id" varchar(1000) COLLATE "pg_catalog"."default",
  "year" text COLLATE "pg_catalog"."default",
  "week" text COLLATE "pg_catalog"."default",
  "province_name" varchar(1000) COLLATE "pg_catalog"."default",
  "price_range" varchar(1000) COLLATE "pg_catalog"."default",
  "brand" varchar(1000) COLLATE "pg_catalog"."default",
  "prod_series" varchar(1000) COLLATE "pg_catalog"."default",
  "brand_series" varchar(1000) COLLATE "pg_catalog"."default",
  "network_pattern" varchar(1000) COLLATE "pg_catalog"."default",
  "sales" varchar(1000) COLLATE "pg_catalog"."default",
  "sales_amt" varchar(1000) COLLATE "pg_catalog"."default",
  "avg_price" varchar(1000) COLLATE "pg_catalog"."default",
  "gtm_year_month" varchar(1000) COLLATE "pg_catalog"."default",
  "memory_comb" varchar(1000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "dw_creation_date" timestamp(6),
  "dw_last_modified_date" timestamp(6),
  "cycle_id" numeric
)
;

