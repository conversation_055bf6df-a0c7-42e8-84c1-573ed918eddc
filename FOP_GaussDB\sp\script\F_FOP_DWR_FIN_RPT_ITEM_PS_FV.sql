-- ----------------------------
-- Function structure for F_FOP_DWR_FIN_RPT_ITEM_PS_FV
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."F_FOP_DWR_FIN_RPT_ITEM_PS_FV"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."F_FOP_DWR_FIN_RPT_ITEM_PS_FV"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-03-10
创建人  ：柳兴旺 l00521248
背景描述：P&S_损益明细报告数据表数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.F_FOP_DWR_FIN_RPT_ITEM_PS_FV()

*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.F_FOP_DWR_FIN_RPT_ITEM_PS_FV';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.FOP_DWR_FIN_RPT_ITEM_PS_FV';
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';       --1表示成功
	

	 --写日志,开始
	insert into fin_dm_opt_fop.dm_pf_log_t
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(fin_dm_opt_fop.dm_pf_log_s.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 'P&S_损益明细报告数据表数据'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_success_flag,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_fop.FOP_DWR_FIN_RPT_ITEM_PS_FV 
		where period_id in (select distinct period_id from fin_dm_opt_fop.fop_DWR_FIN_RPT_ITEM_PS_FV_tmp);    ---temp表的日期 

		---插入目标表数据
		insert into fin_dm_opt_fop.FOP_DWR_FIN_RPT_ITEM_PS_FV            ----供应中心发货时点明细数据
		(
				period_id, 
				report_item_id,
				geo_pc_key,
				major_prod_key,
				data_category_id,
				report_scope_code,
				rmb_fact_ex_rate_ptd_amt,
				usd_fact_ex_rate_ptd_amt
		)
				select 	
				period_id, 
				report_item_id,
				geo_pc_key,
				major_prod_key,
				data_category_id,
				report_scope_code,
				rmb_fact_ex_rate_ptd_amt,
				usd_fact_ex_rate_ptd_amt
				from fop_DWR_FIN_RPT_ITEM_PS_FV_tmp t1      ---PS损益
  		  ;

	v_dml_row_count := sql%rowcount;          -- 收集数据量

	 -- 写结束日志
	insert into fin_dm_opt_fop.dm_pf_log_t
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		fin_dm_opt_fop.dm_pf_log_s.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'P&S_损益明细报告数据表'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		

exception
  	when others then

      perform fin_dm_opt_fop.p_dm_pf_capture_exception(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败
	
    --收集统计信息
    analyse fin_dm_opt_fop.fop_dwr_fin_rpt_item_grp_f;	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

