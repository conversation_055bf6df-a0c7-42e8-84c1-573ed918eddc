-- ----------------------------
-- Table structure for dm_fop_spart_relation_info_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_spart_relation_info_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_spart_relation_info_t" (
  "period_id" numeric,
  "item_code" varchar(150) COLLATE "pg_catalog"."default",
  "item_desc" varchar(2000) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(20) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(200) COLLATE "pg_catalog"."default",
  "l2_name" varchar(200) COLLATE "pg_catalog"."default",
  "l2_name_prob" numeric(38,6),
  "l3_name" varchar(200) COLLATE "pg_catalog"."default",
  "l3_name_prob" numeric(38,6),
  "l1_coefficient" numeric(38,6),
  "l1_coefficient_prob" numeric(38,6),
  "l2_coefficient" numeric(38,6),
  "l2_coefficient_prob" numeric(38,6),
  "l3_coefficient" numeric(38,6),
  "l3_coefficient_prob" numeric(38,6),
  "data_type" varchar(50) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."item_code" IS 'ITEM编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."item_desc" IS 'ITEM描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l2_name_prob" IS 'L2名称预测概率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l3_name" IS 'L3名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l3_name_prob" IS 'L3名称预测概率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l1_coefficient" IS 'L1系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l1_coefficient_prob" IS 'L1系数预测概率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l2_coefficient" IS 'L2系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l2_coefficient_prob" IS 'L2系数预测概率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l3_coefficient" IS 'L3系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."l3_coefficient_prob" IS 'L3系数预测概率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."data_type" IS '数据类型（Manual、AI、Adjust、New）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."update_date" IS '知识表示提供的更新时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_relation_info_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_spart_relation_info_t" IS 'Spart与作业对象关系';

