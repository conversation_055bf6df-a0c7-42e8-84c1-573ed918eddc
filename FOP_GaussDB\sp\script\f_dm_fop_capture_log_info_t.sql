-- ----------------------------
-- Function structure for f_dm_fop_capture_log_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_capture_log_info_t"("p_log_version_id" int4, "p_log_sp_name" varchar, "p_log_para_list" varchar, "p_log_step_num" int4, "p_log_cal_log_desc" varchar, "p_log_formula_sql_txt" text, "p_log_row_count" int4, "p_log_errbuf" varchar);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_capture_log_info_t"("p_log_version_id" int4=NULL::integer, "p_log_sp_name" varchar=NULL::character varying, "p_log_para_list" varchar=NULL::character varying, "p_log_step_num" int4=NULL::integer, "p_log_cal_log_desc" varchar=NULL::character varying, "p_log_formula_sql_txt" text=NULL::text, "p_log_row_count" int4=NULL::integer, "p_log_errbuf" varchar=NULL::character varying)
  RETURNS "pg_catalog"."void" AS $BODY$ 

 --===============================================================
  -- package name :  f_dm_fop_capture_log_info_t
  -- purpose      :  盈利预测-记录日志信息，包括异常信息
  -- parameter    :  
  -- author       :  
  -- date         :  
  -- history      :  修改记录:20230817  9月版本修改日志表
  --===============================================================

declare
  v_sp_name varchar(50) = 'f_dm_fop_capture_log_info_t';

begin
  insert into fin_dm_opt_fop.dm_fop_capture_log_info_t
    (log_id,
     version_id,
     sp_name,
     para_list,
     step_num,
     cal_log_desc,
     formula_sql_txt,
     dml_row_count,
     result_status,
     errbuf,
     created_by,
     creation_date)
  values
    (fin_dm_opt_fop.dm_fop_capture_log_info_s.nextval,
     p_log_version_id,
     p_log_sp_name,
     p_log_para_list,
     p_log_step_num,
     p_log_cal_log_desc,
     p_log_formula_sql_txt,
     p_log_row_count,
     '1',  -- 执行成功
     p_log_errbuf,
     - 1,
     clock_timestamp());
     
 -- 异常处理   
 exception
  when others then
  --raise notice 'fin_dm_opt_fop.f_dm_fop_capture_log_info_t 日志函数报错,请检查!';
  insert into fin_dm_opt_fop.dm_fop_capture_log_info_t
    (log_id,
     version_id,
     sp_name,
     para_list,
     step_num,
     cal_log_desc,
     formula_sql_txt,
     dml_row_count,
     result_status,
     errbuf,
     created_by,
     creation_date)
  VALUES
    (fin_dm_opt_fop.dm_fop_capture_log_info_s.nextval,
     null, --版本信息
		 v_sp_name, --sp名称
		 null, --输入参数
		 null, --执行步骤
		 v_sp_name||'：运行错误', --日志描述
		 sqlerrm, --异常信息
		 null,    --数据量
		 '2001',  -- 执行失败
		 sqlstate, --异常编码
		 -1,       --创建人
     clock_timestamp());

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

