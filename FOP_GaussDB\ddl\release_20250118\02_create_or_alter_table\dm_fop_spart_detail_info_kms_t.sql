-- ----------------------------
-- Table structure for dm_fop_spart_detail_info_kms_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t" (
  "period_id" numeric NOT NULL,
  "spart_code" varchar(150) COLLATE "pg_catalog"."default",
  "spart_desc" varchar(2000) COLLATE "pg_catalog"."default",
  "spart_qty" numeric(38,10),
  "ship_qty" numeric(38,10),
  "snop_quantity" numeric(38,10),
  "snop_plan_quantity" numeric(38,10),
  "rmb_revenue" numeric(38,10),
  "usd_revenue" numeric(38,10),
  "rmb_cost" varchar(5000) COLLATE "pg_catalog"."default",
  "usd_cost" varchar(5000) COLLATE "pg_catalog"."default",
  "equip_rev_rmb_amt" numeric(38,10),
  "equip_rev_usd_amt" numeric(38,10),
  "equip_cost_rmb_amt" numeric(38,10),
  "equip_cost_usd_amt" numeric(38,10),
  "prod_key" numeric,
  "prod_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(600) COLLATE "pg_catalog"."default",
  "busi_lv4" varchar(600) COLLATE "pg_catalog"."default",
  "geo_pc_key" numeric,
  "oversea_flag" varchar(20) COLLATE "pg_catalog"."default",
  "l1_name" varchar(200) COLLATE "pg_catalog"."default",
  "l2_name" varchar(200) COLLATE "pg_catalog"."default",
  "coa_l2_name" varchar(200) COLLATE "pg_catalog"."default",
  "l3_name" varchar(200) COLLATE "pg_catalog"."default",
  "l1_coefficient" numeric(38,10),
  "l2_coefficient" numeric(38,10),
  "l3_coefficient" numeric(38,10),
  "data_type" varchar(50) COLLATE "pg_catalog"."default",
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "phase_date" varchar(60) COLLATE "pg_catalog"."default",
  "plan_unit_quantity" numeric(38,10),
  "unit" varchar(600) COLLATE "pg_catalog"."default",
  "source_table" varchar(100) COLLATE "pg_catalog"."default",
  "industry_type" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "version_code" varchar(100) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "analysis_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."spart_code" IS '物料编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."spart_desc" IS '物料描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."spart_qty" IS 'Part物料数量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."ship_qty" IS 'Part发货数量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."snop_quantity" IS 'SNOP预测月计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."snop_plan_quantity" IS 'SNOP预算月计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."rmb_revenue" IS 'Spart收入金额_人民币';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."usd_revenue" IS 'Spart收入金额_美金';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."rmb_cost" IS 'Bpart成本金额_人民币';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."usd_cost" IS 'Bpart成本金额_美金';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."equip_rev_rmb_amt" IS '设备收入人民币金额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."equip_rev_usd_amt" IS '设备收入美元金额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."equip_cost_rmb_amt" IS '设备成本人民币金额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."equip_cost_usd_amt" IS '设备成本美元金额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."prod_key" IS '产品KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."prod_code" IS '产品编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."prod_cn_name" IS '产品名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv0_prod_rnd_team_code" IS '重量级团队LV0编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv0_prod_rd_team_cn_name" IS '重量级团队LV0中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv0_prod_rd_team_en_name" IS '重量级团队LV0英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv1_prod_rnd_team_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv1_prod_rd_team_cn_name" IS '重量级团队LV1中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv2_prod_rnd_team_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv2_prod_rd_team_cn_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv2_prod_rd_team_en_name" IS '重量级团队LV2英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv3_prod_rnd_team_code" IS '重量级团队LV3编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv3_prod_rd_team_cn_name" IS '重量级团队LV3中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."lv3_prod_rd_team_en_name" IS '重量级团队LV3英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."busi_lv4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."geo_pc_key" IS '区域责任中心KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."oversea_flag" IS '海外标志';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."coa_l2_name" IS 'L2名称（COA）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l3_name" IS 'L3名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l1_coefficient" IS 'L1系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l2_coefficient" IS 'L2系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."l3_coefficient" IS 'L3系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."data_type" IS '数据类型（Manual、AI、Adjust、New）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."articulation_flag" IS '勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."phase_date" IS '期次分区字段';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."plan_unit_quantity" IS '预算、预测计划单元计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."unit" IS '单位';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."source_table" IS '来源表';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."industry_type" IS '产业类型（TGT 目标产业、OTHR 其它产业）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."version_code" IS '版本编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t"."analysis_flag" IS '是否分析场景（Y 是、N 否）';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_spart_detail_info_kms_t" IS 'SPART明细+L1~L3/L1~L3系数+type类型加解密表';

