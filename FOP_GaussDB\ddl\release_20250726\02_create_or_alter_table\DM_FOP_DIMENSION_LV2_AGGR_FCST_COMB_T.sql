-- 盈利量纲-AI融合预测结果表
DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T;
CREATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T(
 PERIOD_ID            VARCHAR(50),
 TARGET_PERIOD        VARCHAR(100),
 BG_CODE              VARCHAR(50),
 BG_NAME              VARCHAR(200),
 OVERSEA_CODE         VARCHAR(50),
 OVERSEA_DESC         VARCHAR(50),
 LV1_CODE             VARCHAR(100),
 LV1_NAME             VARCHAR(600),
 LV2_CODE             VARCHAR(100),
 LV2_NAME             VARCHAR(600),
 CURRENCY             VARCHAR(50),
 FCST_TYPE            VARCHAR(50),
 EQUIP_REV_AFTER_FCST NUMERIC(38,10),
 MGP_RATE_AFTER_FCST  NUMERIC(38,10),
 PHASE_DATE           VARCHAR(60),
 AG<PERSON><PERSON>ATE_FLAG       VARCHAR(50),
 COMBINED_EXPERT      VARCHAR(500),
 <PERSON>EMARK               VARCHAR(500),
 CREATED_BY           INT8,
 CREATION_DATE        TIMESTAMP,
 LAST_UPDATED_BY      INT8,
 LAST_UPDATE_DATE     TIMESTAMP,
 DEL_FLAG             VARCHAR(10)
) WITH (ORIENTATION = COLUMN,COMPRESSION = LOW, COLVERSION = 2.0, ENABLE_DELTA = FALSE)   
DISTRIBUTE BY HASH(PHASE_DATE,PERIOD_ID,TARGET_PERIOD);
COMMENT ON TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T                       IS '盈利量纲-AI融合预测结果表';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.PERIOD_ID            IS '会计期';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.TARGET_PERIOD        IS '目标时点';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.BG_CODE              IS 'BG编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.BG_NAME              IS 'BG名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.OVERSEA_CODE         IS '区域编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.OVERSEA_DESC         IS '区域';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LV1_CODE             IS 'LV1编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LV1_NAME             IS 'LV1名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LV2_CODE             IS 'LV2编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LV2_NAME             IS 'LV2名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.CURRENCY             IS '币种';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.FCST_TYPE            IS '预测类型';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.EQUIP_REV_AFTER_FCST IS '对价后设备收入预测_预测值';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.MGP_RATE_AFTER_FCST  IS '制毛率预测（对价后)_预测值';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.PHASE_DATE           IS '期次';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.AGGREGATE_FLAG       IS '汇聚标志（无效，全都是全球的）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.COMBINED_EXPERT      IS '融合分析师';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.REMARK               IS '备注';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.CREATED_BY           IS '创建人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.CREATION_DATE        IS '创建时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LAST_UPDATED_BY      IS '修改人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.LAST_UPDATE_DATE     IS '修改时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_AGGR_FCST_COMB_T.DEL_FLAG             IS '是否删除标识';