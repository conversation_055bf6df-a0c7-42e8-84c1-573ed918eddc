DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T;
CREATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T(
 VERSION_CODE                            VARCHAR(100),
 PERIOD_ID                               NUMERIC,
 TIME_WINDOW_CODE                        VARCHAR(50),
 BG_CODE                                 VARCHAR(50),
 BG_NAME                                 VARCHAR(200),
 OVERSEA_CODE                            VARCHAR(50 ),
 OVERSEA_DESC                            VARCHAR(50 ),
 LV1_PROD_RND_TEAM_CODE                  VARCHAR(50),
 LV1_PROD_RD_TEAM_CN_NAME                VARCHAR(600),
 LV1_PROD_RD_TEAM_EN_NAME                VARCHAR(600),
 LV2_PROD_RND_TEAM_CODE                  VARCHAR(50),
 LV2_PROD_RD_TEAM_CN_NAME                VARCHAR(600),
 <PERSON>V2_PROD_RD_TEAM_EN_NAME                VARCHAR(600),
 SCENARIOS                               VARCHAR(50),
 <PERSON><PERSON>ENSION_GROUP_CODE                    VARCHAR(50),
 DIMENSION_GROUP_CN_NAME                 VARCHAR(600),
 DIMENSION_GROUP_EN_NAME                 VARCHAR(600),
 DIMENSION_SUBCATEGORY_CODE              VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME           VARCHAR(600),
 DIMENSION_SUBCATEGORY_EN_NAME           VARCHAR(600),
 SHIP_QTY                                NUMERIC(38,10),
 SOURCE_TABLE                            VARCHAR(100),
 REMARK                                  VARCHAR(500),
 CREATED_BY                              INT8,
 CREATION_DATE                           TIMESTAMP,
 LAST_UPDATED_BY                         INT8,
 LAST_UPDATE_DATE                        TIMESTAMP,
 DEL_FLAG                                VARCHAR(10)
) WITH (ORIENTATION = COLUMN,COMPRESSION = LOW, COLVERSION = 2.0, ENABLE_DELTA = FALSE)   
DISTRIBUTE BY HASH(VERSION_CODE,PERIOD_ID);
COMMENT ON TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T                                         IS '盈利量纲结转率汇总表';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.VERSION_CODE                            IS '版本编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.PERIOD_ID                               IS '会计期';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.TIME_WINDOW_CODE                        IS '统计时间窗';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.BG_CODE                                 IS 'BG编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.BG_NAME                                 IS 'BG名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.OVERSEA_CODE                            IS '区域编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.OVERSEA_DESC                            IS '区域名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV1_PROD_RND_TEAM_CODE                  IS '重量级团队LV1编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV1_PROD_RD_TEAM_CN_NAME                IS '重量级团队LV1中文描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV1_PROD_RD_TEAM_EN_NAME                IS '重量级团队LV1英文描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV2_PROD_RND_TEAM_CODE                  IS '重量级团队LV2编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV2_PROD_RD_TEAM_CN_NAME                IS '重量级团队LV2中文描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LV2_PROD_RD_TEAM_EN_NAME                IS '重量级团队LV2英文描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.SCENARIOS                               IS '场景';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_GROUP_CODE                    IS '量纲分组编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_GROUP_CN_NAME                 IS '量纲分组中文名';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_GROUP_EN_NAME                 IS '量纲分组英文名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_SUBCATEGORY_CODE              IS '量纲子类编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_SUBCATEGORY_CN_NAME           IS '量纲子类中文名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DIMENSION_SUBCATEGORY_EN_NAME           IS '量纲子类英文名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.SHIP_QTY                                IS '发货量（历史）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.SOURCE_TABLE                            IS '来源表';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.REMARK                                  IS '备注';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.CREATED_BY                              IS '创建人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.CREATION_DATE                           IS '创建时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LAST_UPDATED_BY                         IS '修改人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.LAST_UPDATE_DATE                        IS '修改时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_CARRYOVER_SUM_T.DEL_FLAG                                IS '是否删除';