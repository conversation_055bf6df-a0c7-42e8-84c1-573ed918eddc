-- ----------------------------
-- Function structure for f_dm_fop_spart_l2_info_ytd_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l2_info_ytd_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l2_info_ytd_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-06-01
创建人  ：鲁广武  lwx1186472
背景描述：作业对象L2层级数据年累计表（只保留一个版本的数据）(提供给知识表示)
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l2_info_ytd_t()										   		  
*/ 
	
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l2_info_ytd_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t';
	v_tbl_name2 varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t';	
	v_version_code varchar(50);  -- 版本编码
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';

  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L2层级数据年累计表'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	-- 创建 l2_info_his_t_temp 临时表
  drop table if exists l2_info_his_t_temp;
	create temporary table l2_info_his_t_temp (
	version_code character varying(50),
	period_id numeric,
	phase_date character varying(60),
	bg_code character varying(50),
	bg_name character varying(200),
	oversea_desc character varying(50),
	lv1_code character varying(100),
	lv1_name character varying(600),
	lv2_code character varying(100),
	lv2_name character varying(600),
	l1_name character varying(100),
	l2_name character varying(100),
	currency character varying(50),
	equip_rev_cons_before_amt numeric(38,10),
	equip_cost_cons_before_amt numeric(38,10),
	plan_qty numeric(38,10),
	ship_qty numeric(38,10),
	spart_qty numeric(38,10),
	unit_cost numeric(38,10),
	unit_price numeric(38,10),
	rev_percent numeric(38,10),
	mgp_ratio numeric(38,10),
	articulation_flag character varying(50)
)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
;

	-- 创建 l1_temp_ytd 临时表
  drop table if exists l1_temp_ytd;
	create temporary table l1_temp_ytd(
	    version_code  varchar(50),  -- 版本编码
	    period_id  numeric,--	会计期
			phase_date varchar(50),
			bg_code    varchar(50),--	bg编码
			bg_name    varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			lv2_code      varchar(50),--	重量级团队lv2编码
			l1_name       varchar(500),--	l1名称
			equip_rev_cons_before_ytd  numeric(38,10),-- l1对价前收入金额（本年累计）
			plan_ytd  numeric(38,10),--	发货量（snop本年累计）
			ship_ytd  numeric(38,10),--	发货量（本年累计）
			spart_ytd  numeric(38,10),--收入量（本年累计）
			currency      varchar(10),--币种
			articulation_flag  varchar(50)
	)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;


	-- 创建 l2_temp_ytd 临时表
  drop table if exists l2_temp_ytd;
	create temporary table l2_temp_ytd(
	    version_code  varchar(50),  -- 版本编码
	    period_id     numeric,--	会计期
			phase_date    varchar(50),
			bg_code       varchar(50),--	bg编码
			bg_name       varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			lv1_name      varchar(200),--	重量级团队lv1描述
			lv2_code      varchar(50),--	重量级团队lv2编码
			lv2_name      varchar(200),--	重量级团队lv2名称
			l2_name       varchar(200),--	l2名称
			l1_name       varchar(200),--	l1名称
			equip_rev_cons_before_ytd numeric(38,10), --设备收入本年累计（对价前）
			equip_cost_cons_before_ytd numeric(38,10),--设备成本本年累计（对价前）
			plan_ytd  numeric(38,10),                 --发货量（SNOP本年累计）
			ship_ytd  numeric(38,10),                 --发货量（本年累计）
			spart_ytd numeric(38,10),			      --收入量（本年累计）			
			currency  varchar(10),
			articulation_flag  varchar(50)
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
	

	-- 创建 all_temp_ytd 临时表
  drop table if exists all_temp_ytd;
	create temporary table all_temp_ytd(
	        version_code      varchar(50),  -- 版本编码
	        period_id		  numeric,	--会计期
			phase_date        varchar(50),
			bg_code           varchar(50),	--bg编码
			bg_name           varchar(100),	--bg名称
			oversea_desc      varchar(50),	--区域
			lv1_code          varchar(50),	--重量级团队lv1编码
			lv1_name					varchar(200),	--重量级团队lv1描述
			lv2_code					varchar(50),	--重量级团队lv2编码
			lv2_name					varchar(200),	--重量级团队lv2名称
			l1_name						varchar(200),	--l1名称
			l2_name						varchar(200),	--l2名称
			currency					varchar(10),	--币种
			equip_rev_cons_before_ytd		numeric(38,10),	
			equip_cost_cons_before_ytd	    numeric(38,10),	
		    plan_ytd	     numeric(38,10),	
			ship_ytd	     numeric(38,10),	
			spart_ytd	     numeric(38,10),	
			unit_cost_ytd	 numeric(38,10),	
			unit_price_ytd	 numeric(38,10),	
			rev_percent_ytd  numeric(38,10),	
			mgp_ratio_ytd 	 numeric(38,10),	
			articulation_flag  varchar(50),
			del_ind            varchar(10)
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
  ;
   

--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
   
  ---判断p_version_code在his_t表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t where version_code = p_version_code)
   then   


---把l2_his_t表中缺少的数据补0入临时表，为后面ytd做计算
     insert into l2_info_his_t_temp
	 (  version_code
       ,period_id
       ,phase_date
       ,bg_code
       ,bg_name
       ,oversea_desc
       ,lv1_code
       ,lv1_name
       ,lv2_code
       ,lv2_name
       ,l1_name
       ,l2_name
       ,currency
       ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
       ,articulation_flag
	 )

  ---找出缺少的月份数据
 with year_month_tmp as(
select distinct version_code,period_id,substr(period_id,1,4) as year, substr(period_id,5,2)::numeric as month,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
	   ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t 
 where version_code = p_version_code
   and phase_date is null
   and del_flag = 'N'
),
result_tmp as(
select version_code,year,month_cn,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
     , (case when year < to_char(current_date,'yyyy') and month_cn = 12 then 'OK' 
     	       when year = to_char(current_date,'yyyy') and month_cn = (to_char(current_date,'mm') - to_char(date_trunc('year',current_date),'mm')) then 'OK' 
     	       else 'NO'
     	  end) as result
  from (select version_code,year,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag, count(*) as month_cn 
          from year_month_tmp 
	     group by version_code,year,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag)
),
year_all_tmp as(
select version_code,year,month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from (
select version_code,year, unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from result_tmp
 where result = 'NO'
   and year = to_char(current_date,'yyyy')
)T
 where month <= (select max(substr(period_id,5))::numeric from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t
                  where version_code = p_version_code
				    and substr(period_id,1,4) = to_char(current_date,'yyyy'))
union all
select version_code,year, unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from result_tmp
 where result = 'NO'
   and year < to_char(current_date,'yyyy')
),
--缺少的会计期月份
year_less_tmp as (
select t1.version_code,(t1.year||lpad(t1.month,2,0))::numeric as period_id,
       t1.phase_date,t1.bg_code,t1.bg_name,t1.oversea_desc,t1.lv1_code,t1.lv1_name,t1.lv2_code,t1.lv2_name,t1.l1_name,t1.l2_name,t1.currency,t1.articulation_flag
  from year_all_tmp t1
  left join year_month_tmp t2
    on t1.version_code = t2.version_code
   and t1.year = t2.year
   and t1.month = t2.month
   and nvl(t1.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t1.bg_code            = t2.bg_code
   and t1.bg_name            = t2.bg_name
   and t1.oversea_desc       = t2.oversea_desc
   and t1.lv1_code           = t2.lv1_code
   and t1.lv1_name           = t2.lv1_name
   and t1.lv2_code           = t2.lv2_code
   and t1.lv2_name           = t2.lv2_name
   and t1.l1_name            = t2.l1_name
   and t1.l2_name            = t2.l2_name
   and t1.currency           = t2.currency
   and t1.articulation_flag  = t2.articulation_flag
 where t2.month is null 
),
--限制会计期，取>t表已存在最小会计期的数据,<=t表已存在最大会计期的数据
min_period_tmp as (
select version_code,min(period_id) as min_period_id,max(period_id) as max_period_id,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from year_month_tmp
 group by version_code,substr(period_id,1,4),phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
)
select  distinct
        t1.version_code
       ,t1.period_id
       ,t1.phase_date
       ,t1.bg_code
       ,t1.bg_name
       ,t1.oversea_desc
       ,t1.lv1_code
       ,t1.lv1_name
       ,t1.lv2_code
       ,t1.lv2_name
       ,t1.l1_name
       ,t1.l2_name
       ,t1.currency
       ,0 as equip_rev_cons_before_amt 
       ,0 as equip_cost_cons_before_amt 
       ,0 as plan_qty 
       ,0 as ship_qty 
       ,0 as spart_qty 
       ,0 as unit_cost 
       ,0 as unit_price 
       ,0 as rev_percent 
       ,0 as mgp_ratio 
       ,t1.articulation_flag
  from year_less_tmp t1
  left join min_period_tmp t2
    on t1.version_code       = t2.version_code
   and nvl(t1.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t1.bg_code            = t2.bg_code
   and t1.bg_name            = t2.bg_name
   and t1.oversea_desc       = t2.oversea_desc
   and t1.lv1_code           = t2.lv1_code
   and t1.lv1_name           = t2.lv1_name
   and t1.lv2_code           = t2.lv2_code
   and t1.lv2_name           = t2.lv2_name
   and t1.l1_name            = t2.l1_name
   and t1.l2_name            = t2.l2_name
   and t1.currency           = t2.currency
   and t1.articulation_flag  = t2.articulation_flag   
 where t1.period_id > t2.min_period_id
   and t1.period_id <= t2.max_period_id

union all

select distinct version_code,period_id,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency
	   ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
	   ,articulation_flag
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t 
 where version_code = p_version_code
;

	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'l2_info_his_t_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


  
     ----计算ytd的相应指标
     ---删除重跑his_ytd表中已有传入参数的数据
     delete fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t where version_code = p_version_code;	

	 
  -- 按照l2层级收敛	 
  insert into l2_temp_ytd(
	        version_code
	        ,period_id
			,phase_date
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l2_name
			,l1_name
			,equip_rev_cons_before_ytd  --设备收入本年累计（对价前）
			,equip_cost_cons_before_ytd --设备成本本年累计（对价前）
			,plan_ytd                   --发货量（SNOP本年累计）
			,ship_ytd                   --发货量（本年累计）
			,spart_ytd			        --收入量（本年累计）
			,currency
			,articulation_flag
  )		 
	select version_code,  -- 版本编码
	       period_id,--	会计期
		   phase_date,
		   bg_code,--	bg编码
		   bg_name,--	bg名称
		   oversea_desc,--区域描述
		   lv1_code,--	重量级团队lv1编码
		   lv1_name,--	重量级团队lv1描述
		   lv2_code,--	重量级团队lv2编码
		   lv2_name,--	重量级团队lv2名称
		   l2_name,--	l2名称
		   l1_name,--	l1名称
		   sum(equip_rev_cons_before_amt)over(partition by version_code
                                                           ,substr(period_id,1,4)
														   ,phase_date 
                                                           ,bg_code     
                                                           ,bg_name     
                                                           ,oversea_desc
                                                           ,lv1_code   
                                                           ,lv1_name
										                   ,lv2_code
                                                           ,lv2_name
														   ,l2_name
                                                           ,l1_name
										                   ,currency
                                                           ,articulation_flag														   
								                  order by period_id) as  equip_rev_cons_before_ytd, --设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code
                                                           ,substr(period_id,1,4)
														   ,phase_date 
                                                           ,bg_code     
                                                           ,bg_name     
                                                           ,oversea_desc
                                                           ,lv1_code   
                                                           ,lv1_name
										                   ,lv2_code
                                                           ,lv2_name
														   ,l2_name
                                                           ,l1_name
										                   ,currency
                                                           ,articulation_flag														   
								                  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
		   sum(plan_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as plan_ytd,                                   --发货量（SNOP本年累计）
		   sum(ship_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as ship_ytd,                                   --发货量（本年累计）
		   sum(spart_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as spart_ytd,                                  --收入量（本年累计）
		   currency,
		   articulation_flag
	  from l2_info_his_t_temp
         ;
		 
	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'l2_temp_ytd 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	-- 按照l1层级收敛
	-- 场景1对应的L1系数有2种：0或0.33333，其中0.33333的是射频模块的标识		 
	insert into l1_temp_ytd(
	        version_code
	        ,period_id
			,phase_date
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv2_code
			,l1_name
			,equip_rev_cons_before_ytd
			,plan_ytd
			,ship_ytd
			,spart_ytd			
			,currency
			,articulation_flag
	)		 
	 select distinct
	        version_code,  -- 版本编码
	        period_id,--	会计期
			phase_date,
			bg_code,--	bg编码
			bg_name,--	bg名称
			oversea_desc,--区域描述
			lv1_code,--	重量级团队lv1编码
			lv2_code,--	重量级团队lv2编码
			l1_name,--	l1名称
			sum (nvl(equip_rev_cons_before_amt,0))over(partition by version_code
                                                                    ,substr(period_id,1,4)
										                            ,phase_date 
                                                                    ,bg_code     
                                                                    ,bg_name     
                                                                    ,oversea_desc
                                                                    ,lv1_code   
										                            ,lv2_code  
                                                                    ,l1_name
										                            ,currency
                                                                    ,articulation_flag														   
								                           order by period_id) as equip_rev_cons_before_ytd,-- l1设备收入本年累计（对价前）
			sum(plan_qty)over(partition by version_code
                                           ,substr(period_id,1,4)
										   ,phase_date 
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
										   ,lv2_code
                                           ,l1_name
										   ,currency
                                           ,articulation_flag														   
								  order by period_id) as plan_ytd,--	发货量（SNOP本年累计）
			sum(ship_qty)over(partition by version_code
                                           ,substr(period_id,1,4)
										   ,phase_date 
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
										   ,lv2_code
                                           ,l1_name
										   ,currency
                                           ,articulation_flag														   
								  order by period_id) as ship_ytd,--	发货量（本年累计）
			sum(spart_qty)over(partition by version_code
                                            ,substr(period_id,1,4)
										    ,phase_date 
                                            ,bg_code     
                                            ,bg_name     
                                            ,oversea_desc
                                            ,lv1_code   
										    ,lv2_code
                                            ,l1_name
										    ,currency
                                            ,articulation_flag														   
								   order by period_id) as spart_ytd,--收入量（本年累计）
			currency,--币种
			articulation_flag
	   from l2_info_his_t_temp
          ;


	insert into all_temp_ytd
	      (
	        version_code,  -- 版本编码
	        period_id,	--会计期
			phase_date,
			bg_code,	--bg编码
			bg_name,	--bg名称
			oversea_desc,	--区域
			lv1_code,	--重量级团队lv1编码
			lv1_name,	--重量级团队lv1描述
			lv2_code,	--重量级团队lv2编码
			lv2_name,	--重量级团队lv2名称
			l1_name,	--l1名称
			l2_name,	--l2名称
			currency,	--币种
			equip_rev_cons_before_ytd,	
			equip_cost_cons_before_ytd,	
		    plan_ytd,	
			ship_ytd,	
			spart_ytd,	
			unit_cost_ytd,	 --单位成本（本年累计）
			unit_price_ytd,	 --单位价格（本年累计）
			rev_percent_ytd, --收入占比（本年累计）
			mgp_ratio_ytd, 	 --制毛率（本年累计）
			articulation_flag,   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind
           )
	--计算ytd收入占比、制毛率
	with all_temp_01 as (	 
	 select t1.version_code,  -- 版本编码
	        t1.period_id															,	--会计期
			t1.phase_date,
			t1.bg_code																,	--bg编码
			t1.bg_name																,	--bg名称
			t1.oversea_desc															,	--区域
			t1.lv1_code																,	--重量级团队lv1编码
			t1.lv1_name																,	--重量级团队lv1描述
			t1.lv2_code																,	--重量级团队lv2编码
			t1.lv2_name																,	--重量级团队lv2名称
			t2.l1_name																,	--l1名称
			t1.l2_name																,	--l2名称
			t1.currency																,	--币种
		    t1.equip_rev_cons_before_ytd											,	--设备收入本年累计(对价前)
			t1.equip_cost_cons_before_ytd											,	--设备成本本年累计(对价前)			
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.plan_ytd,0)*3) else t1.plan_ytd end) as plan_ytd	,	--发货量（SNOP本年累计）
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.ship_ytd,0)*3) else t1.ship_ytd end) as ship_ytd ,	--发货量（本年累计）
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.spart_ytd,0)*3) else t1.spart_ytd end) as spart_ytd ,	--收入量（本年累计）
			(case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when nvl(t2.equip_rev_cons_before_ytd,0) = 0 then -999999
				    else t1.equip_rev_cons_before_ytd / t2.equip_rev_cons_before_ytd end) as rev_percent_ytd,	--收入占比（本年累计） = l2对价前收入金额/l1对价前收入金额
			(case when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_ytd, 0 ) = 0 then 0
				    when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_ytd, 0 ) <> 0 then -999999
				    else 1 - t1.equip_cost_cons_before_ytd / t1.equip_rev_cons_before_ytd end) as mgp_ratio_ytd,	--制毛率（本年累计）   = 1 - l2对价前成本金额/l2对价前收入金额  
			t1.articulation_flag                                                    , 	--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			(case when t1.phase_date is not null and t1.plan_ytd = 0 then 'D' else '' end) as del_ind--phase_date有值且plan_qty为0的是要剔除的
	   from l2_temp_ytd t1
       left join l1_temp_ytd t2
		 on t1.version_code = t2.version_code
		and t1.period_id = t2.period_id--会计期
	    and t1.bg_code = t2.bg_code--bg编码
	    and t1.oversea_desc = t2.oversea_desc--区域描述
	    and t1.lv1_code = t2.lv1_code--	重量级团队lv1编码
	    and t1.lv2_code = t2.lv2_code--	重量级团队lv2编码
	    and t1.currency = t2.currency--币种
	    and t1.l1_name = t2.l1_name
	    and t1.articulation_flag = t2.articulation_flag
	    and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
	  where t1.l2_name is not null or t1.l2_name <> ''	 
	 
	) 
	-- 计算单位成本、单位价格	
	 select t1.version_code,  -- 版本编码
	        t1.period_id															,	--会计期
			t1.phase_date,
			t1.bg_code																,	--bg编码
			t1.bg_name																,	--bg名称
			t1.oversea_desc															,	--区域
			t1.lv1_code																,	--重量级团队lv1编码
			t1.lv1_name																,	--重量级团队lv1描述
			t1.lv2_code																,	--重量级团队lv2编码
			t1.lv2_name																,	--重量级团队lv2名称
			t1.l1_name																,	--l1名称
			t1.l2_name																,	--l2名称
			t1.currency																,	--币种			
			t1.equip_rev_cons_before_ytd,	
			t1.equip_cost_cons_before_ytd,	
		    t1.plan_ytd,	
			t1.ship_ytd,	
			t1.spart_ytd,	
			(case when nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
				    when nvl(t1.spart_ytd,0) = 0 then -999999
				    else t1.equip_cost_cons_before_ytd / t1.spart_ytd end) as unit_cost_ytd,	
			(case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when nvl(t1.spart_ytd,0) = 0 then -999999
				    else t1.equip_rev_cons_before_ytd / t1.spart_ytd end) as unit_price_ytd,
			t1.rev_percent_ytd, --收入占比（本年累计）
			t1.mgp_ratio_ytd, 	 --制毛率（本年累计）	
			t1.articulation_flag,   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind
       from all_temp_01 t1
        ;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'all_temp_ytd 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	
  --入his_ytd_t表
  insert into fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t 
          (
             version_code
            ,period_id
            ,phase_date
            ,bg_code
            ,bg_name
            ,oversea_desc
            ,lv1_code
            ,lv1_name
            ,lv2_code
            ,lv2_name
            ,l1_name
            ,l2_name
            ,currency
            ,equip_rev_cons_before_ytd
            ,equip_cost_cons_before_ytd
            ,plan_ytd
            ,ship_ytd
            ,spart_ytd
            ,unit_cost_ytd
            ,unit_price_ytd
            ,rev_percent_ytd
            ,mgp_ratio_ytd
            ,articulation_flag
            ,remark
            ,created_by
            ,creation_date
            ,last_updated_by
            ,last_update_date
            ,del_flag
			)			
	select  version_code
           ,period_id
           ,phase_date
           ,bg_code
           ,bg_name
           ,oversea_desc
           ,lv1_code
           ,lv1_name
           ,lv2_code
           ,lv2_name
           ,l1_name
           ,l2_name
           ,currency
           ,equip_rev_cons_before_ytd
           ,equip_cost_cons_before_ytd
           ,plan_ytd
           ,ship_ytd
           ,spart_ytd
           ,unit_cost_ytd
           ,unit_price_ytd
           ,rev_percent_ytd
           ,mgp_ratio_ytd
           ,articulation_flag
		   ,'' as remark
	  	   ,-1 as created_by
	  	   ,current_timestamp as creation_date
	  	   ,-1 as last_updated_by
	  	   ,current_timestamp as last_update_date
	  	   ,'N' as del_flag
	  from all_temp_ytd 
	;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '作业对象L2层级数据年累计表(多个版本)'||v_tbl_name2||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


	-- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t;
  
  
    --- 数据入ytd_t表
	  insert into fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t
	        (
               period_id
              ,phase_date
              ,bg_code
              ,bg_name
              ,oversea_desc
              ,lv1_code
              ,lv1_name
              ,lv2_code
              ,lv2_name
              ,l1_name
              ,l2_name
              ,currency
              ,equip_rev_cons_before_ytd
              ,equip_cost_cons_before_ytd
              ,plan_ytd
              ,ship_ytd
              ,spart_ytd
              ,unit_cost_ytd
              ,unit_price_ytd
              ,rev_percent_ytd
              ,mgp_ratio_ytd
              ,articulation_flag
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
		    )
	      select  period_id
			     ,phase_date
			     ,bg_code
			     ,bg_name
			     ,oversea_desc
			     ,lv1_code
			     ,lv1_name
			     ,lv2_code
			     ,lv2_name
			     ,l1_name
			     ,l2_name
			     ,currency
			     ,equip_rev_cons_before_ytd
			     ,equip_cost_cons_before_ytd
			     ,plan_ytd
			     ,ship_ytd
			     ,spart_ytd
			     ,unit_cost_ytd
			     ,unit_price_ytd
			     ,rev_percent_ytd
			     ,mgp_ratio_ytd
				 ,articulation_flag
			     ,'' as remark
	  	         ,-1 as created_by
	  	         ,current_timestamp as creation_date
	  	         ,-1 as last_updated_by
	  	         ,current_timestamp as last_update_date
	  	         ,'N' as del_flag
		    from fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t
		   where version_code = p_version_code  -- 取传入版本数据
		   ;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

   --写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '作业对象L2层级数据年累计表(一个版本)'||v_tbl_name||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


  else
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>6,
        p_log_cal_log_desc => 'his_t表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;


else

	-- 取来源表最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
      from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t 
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t)
     group by substr(version_code,1,6)
	; 


---把l2_his_t表中缺少的数据补0入临时表，为后面ytd做计算
     insert into l2_info_his_t_temp
	 (  version_code
       ,period_id
       ,phase_date
       ,bg_code
       ,bg_name
       ,oversea_desc
       ,lv1_code
       ,lv1_name
       ,lv2_code
       ,lv2_name
       ,l1_name
       ,l2_name
       ,currency
       ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
       ,articulation_flag
	 )

  ---找出缺少的月份数据
 with year_month_tmp as(
select distinct version_code,period_id,substr(period_id,1,4) as year, substr(period_id,5,2)::numeric as month,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
	   ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t 
 where version_code = v_max_version_code
   and phase_date is null
   and del_flag = 'N'
),
result_tmp as(
select version_code,year,month_cn,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
     , (case when year < to_char(current_date,'yyyy') and month_cn = 12 then 'OK' 
     	       when year = to_char(current_date,'yyyy') and month_cn = (to_char(current_date,'mm') - to_char(date_trunc('year',current_date),'mm')) then 'OK' 
     	       else 'NO'
     	  end) as result
  from (select version_code,year,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag, count(*) as month_cn 
          from year_month_tmp 
	     group by version_code,year,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag)
),
year_all_tmp as(
select version_code,year,month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from (
select version_code,year, unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from result_tmp
 where result = 'NO'
   and year = to_char(current_date,'yyyy')
)T
 where month <= (select max(substr(period_id,5))::numeric from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t
                  where version_code = v_max_version_code
				    and substr(period_id,1,4) = to_char(current_date,'yyyy'))
union all
select version_code,year, unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month,phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from result_tmp
 where result = 'NO'
   and year < to_char(current_date,'yyyy')
),
--缺少的会计期月份
year_less_tmp as (
select t1.version_code,(t1.year||lpad(t1.month,2,0))::numeric as period_id,
       t1.phase_date,t1.bg_code,t1.bg_name,t1.oversea_desc,t1.lv1_code,t1.lv1_name,t1.lv2_code,t1.lv2_name,t1.l1_name,t1.l2_name,t1.currency,t1.articulation_flag
  from year_all_tmp t1
  left join year_month_tmp t2
    on t1.version_code = t2.version_code
   and t1.year = t2.year
   and t1.month = t2.month
   and nvl(t1.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t1.bg_code            = t2.bg_code
   and t1.bg_name            = t2.bg_name
   and t1.oversea_desc       = t2.oversea_desc
   and t1.lv1_code           = t2.lv1_code
   and t1.lv1_name           = t2.lv1_name
   and t1.lv2_code           = t2.lv2_code
   and t1.lv2_name           = t2.lv2_name
   and t1.l1_name            = t2.l1_name
   and t1.l2_name            = t2.l2_name
   and t1.currency           = t2.currency
   and t1.articulation_flag  = t2.articulation_flag
 where t2.month is null 
),
--限制会计期，取>t表已存在最小会计期的数据,<=t表已存在最大会计期的数据
min_period_tmp as (
select version_code,min(period_id) as min_period_id,max(period_id) as max_period_id,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
  from year_month_tmp
 group by version_code,substr(period_id,1,4),phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency,articulation_flag
)
select  distinct
        t1.version_code
       ,t1.period_id
       ,t1.phase_date
       ,t1.bg_code
       ,t1.bg_name
       ,t1.oversea_desc
       ,t1.lv1_code
       ,t1.lv1_name
       ,t1.lv2_code
       ,t1.lv2_name
       ,t1.l1_name
       ,t1.l2_name
       ,t1.currency
       ,0 as equip_rev_cons_before_amt 
       ,0 as equip_cost_cons_before_amt 
       ,0 as plan_qty 
       ,0 as ship_qty 
       ,0 as spart_qty 
       ,0 as unit_cost 
       ,0 as unit_price 
       ,0 as rev_percent 
       ,0 as mgp_ratio 
       ,t1.articulation_flag
  from year_less_tmp t1
  left join min_period_tmp t2
    on t1.version_code       = t2.version_code
   and nvl(t1.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t1.bg_code            = t2.bg_code
   and t1.bg_name            = t2.bg_name
   and t1.oversea_desc       = t2.oversea_desc
   and t1.lv1_code           = t2.lv1_code
   and t1.lv1_name           = t2.lv1_name
   and t1.lv2_code           = t2.lv2_code
   and t1.lv2_name           = t2.lv2_name
   and t1.l1_name            = t2.l1_name
   and t1.l2_name            = t2.l2_name
   and t1.currency           = t2.currency
   and t1.articulation_flag  = t2.articulation_flag   
 where t1.period_id > t2.min_period_id
   and t1.period_id <= t2.max_period_id

union all

select distinct version_code,period_id,
       phase_date,bg_code,bg_name,oversea_desc,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name,currency
	   ,equip_rev_cons_before_amt 
       ,equip_cost_cons_before_amt 
       ,plan_qty 
       ,ship_qty 
       ,spart_qty 
       ,unit_cost 
       ,unit_price 
       ,rev_percent 
       ,mgp_ratio 
	   ,articulation_flag
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t 
 where version_code = v_max_version_code
;

	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'l2_info_his_t_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	

     ----计算ytd的相应指标
     ---删除重跑his_ytd表中最大版本的数据
     delete fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t where version_code = v_max_version_code;	

	 
  -- 按照l2层级收敛	 
  insert into l2_temp_ytd(
	        version_code
	        ,period_id
			,phase_date
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l2_name
			,l1_name
			,equip_rev_cons_before_ytd  --设备收入本年累计（对价前）
			,equip_cost_cons_before_ytd --设备成本本年累计（对价前）
			,plan_ytd                   --发货量（SNOP本年累计）
			,ship_ytd                   --发货量（本年累计）
			,spart_ytd			        --收入量（本年累计）
			,currency
			,articulation_flag
  )		 
	select version_code,  -- 版本编码
	       period_id,--	会计期
		   phase_date,
		   bg_code,--	bg编码
		   bg_name,--	bg名称
		   oversea_desc,--区域描述
		   lv1_code,--	重量级团队lv1编码
		   lv1_name,--	重量级团队lv1描述
		   lv2_code,--	重量级团队lv2编码
		   lv2_name,--	重量级团队lv2名称
		   l2_name,--	l2名称
		   l1_name,--	l1名称
		   sum(equip_rev_cons_before_amt)over(partition by version_code
                                                           ,substr(period_id,1,4)
														   ,phase_date 
                                                           ,bg_code     
                                                           ,bg_name     
                                                           ,oversea_desc
                                                           ,lv1_code   
                                                           ,lv1_name
										                   ,lv2_code
                                                           ,lv2_name
														   ,l2_name
                                                           ,l1_name
										                   ,currency
                                                           ,articulation_flag														   
								                  order by period_id) as  equip_rev_cons_before_ytd, --设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code
                                                           ,substr(period_id,1,4)
														   ,phase_date 
                                                           ,bg_code     
                                                           ,bg_name     
                                                           ,oversea_desc
                                                           ,lv1_code   
                                                           ,lv1_name
										                   ,lv2_code
                                                           ,lv2_name
														   ,l2_name
                                                           ,l1_name
										                   ,currency
                                                           ,articulation_flag														   
								                  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
		   sum(plan_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as plan_ytd,                                   --发货量（SNOP本年累计）
		   sum(ship_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as ship_ytd,                                   --发货量（本年累计）
		   sum(spart_qty)over(partition by version_code
                                          ,substr(period_id,1,4)
										  ,phase_date 
                                          ,bg_code     
                                          ,bg_name     
                                          ,oversea_desc
                                          ,lv1_code   
                                          ,lv1_name
										  ,lv2_code
                                          ,lv2_name
										  ,l2_name
                                          ,l1_name
										  ,currency
                                          ,articulation_flag														   
								 order by period_id) as spart_ytd,                                  --收入量（本年累计）
		   currency,
		   articulation_flag
	  from l2_info_his_t_temp
         ;
		 
	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'l2_temp_ytd 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	-- 按照l1层级收敛
	-- 场景1对应的L1系数有2种：0或0.33333，其中0.33333的是射频模块的标识		 
	insert into l1_temp_ytd(
	        version_code
	        ,period_id
			,phase_date
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv2_code
			,l1_name
			,equip_rev_cons_before_ytd
			,plan_ytd
			,ship_ytd
			,spart_ytd			
			,currency
			,articulation_flag
	)		 
	 select distinct
	        version_code,  -- 版本编码
	        period_id,--	会计期
			phase_date,
			bg_code,--	bg编码
			bg_name,--	bg名称
			oversea_desc,--区域描述
			lv1_code,--	重量级团队lv1编码
			lv2_code,--	重量级团队lv2编码
			l1_name,--	l1名称
			sum (nvl(equip_rev_cons_before_amt,0))over(partition by version_code
                                                                    ,substr(period_id,1,4)
										                            ,phase_date 
                                                                    ,bg_code     
                                                                    ,bg_name     
                                                                    ,oversea_desc
                                                                    ,lv1_code   
										                            ,lv2_code  
                                                                    ,l1_name
										                            ,currency
                                                                    ,articulation_flag														   
								                           order by period_id) as equip_rev_cons_before_ytd,-- l1设备收入本年累计（对价前）
			sum(plan_qty)over(partition by version_code
                                           ,substr(period_id,1,4)
										   ,phase_date 
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
										   ,lv2_code
                                           ,l1_name
										   ,currency
                                           ,articulation_flag														   
								  order by period_id) as plan_ytd,--	发货量（SNOP本年累计）
			sum(ship_qty)over(partition by version_code
                                           ,substr(period_id,1,4)
										   ,phase_date 
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
										   ,lv2_code
                                           ,l1_name
										   ,currency
                                           ,articulation_flag														   
								  order by period_id) as ship_ytd,--	发货量（本年累计）
			sum(spart_qty)over(partition by version_code
                                            ,substr(period_id,1,4)
										    ,phase_date 
                                            ,bg_code     
                                            ,bg_name     
                                            ,oversea_desc
                                            ,lv1_code   
										    ,lv2_code
                                            ,l1_name
										    ,currency
                                            ,articulation_flag														   
								   order by period_id) as spart_ytd,--收入量（本年累计）
			currency,--币种
			articulation_flag
	   from l2_info_his_t_temp 
          ;


	insert into all_temp_ytd
	      (
	        version_code,  -- 版本编码
	        period_id,	--会计期
			phase_date,
			bg_code,	--bg编码
			bg_name,	--bg名称
			oversea_desc,	--区域
			lv1_code,	--重量级团队lv1编码
			lv1_name,	--重量级团队lv1描述
			lv2_code,	--重量级团队lv2编码
			lv2_name,	--重量级团队lv2名称
			l1_name,	--l1名称
			l2_name,	--l2名称
			currency,	--币种
			equip_rev_cons_before_ytd,	
			equip_cost_cons_before_ytd,	
		    plan_ytd,	
			ship_ytd,	
			spart_ytd,	
			unit_cost_ytd,	 --单位成本（本年累计）
			unit_price_ytd,	 --单位价格（本年累计）
			rev_percent_ytd, --收入占比（本年累计）
			mgp_ratio_ytd, 	 --制毛率（本年累计）
			articulation_flag,   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind
           )
	--计算ytd收入占比、制毛率
	with all_temp_01 as (	 
	 select t1.version_code,  -- 版本编码
	        t1.period_id															,	--会计期
			t1.phase_date,
			t1.bg_code																,	--bg编码
			t1.bg_name																,	--bg名称
			t1.oversea_desc															,	--区域
			t1.lv1_code																,	--重量级团队lv1编码
			t1.lv1_name																,	--重量级团队lv1描述
			t1.lv2_code																,	--重量级团队lv2编码
			t1.lv2_name																,	--重量级团队lv2名称
			t2.l1_name																,	--l1名称
			t1.l2_name																,	--l2名称
			t1.currency																,	--币种
		    t1.equip_rev_cons_before_ytd											,	--设备收入本年累计(对价前)
			t1.equip_cost_cons_before_ytd											,	--设备成本本年累计(对价前)			
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.plan_ytd,0)*3) else t1.plan_ytd end) as plan_ytd	,	--发货量（SNOP本年累计）
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.ship_ytd,0)*3) else t1.ship_ytd end) as ship_ytd ,	--发货量（本年累计）
			(case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件', '其他') then (nvl(t2.spart_ytd,0)*3) else t1.spart_ytd end) as spart_ytd ,	--收入量（本年累计）
			(case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when nvl(t2.equip_rev_cons_before_ytd,0) = 0 then -999999
				    else t1.equip_rev_cons_before_ytd / t2.equip_rev_cons_before_ytd end) as rev_percent_ytd,	--收入占比（本年累计） = l2对价前收入金额/l1对价前收入金额
			(case when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_ytd, 0 ) = 0 then 0
				    when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_ytd, 0 ) <> 0 then -999999
				    else 1 - t1.equip_cost_cons_before_ytd / t1.equip_rev_cons_before_ytd end) as mgp_ratio_ytd,	--制毛率（本年累计）   = 1 - l2对价前成本金额/l2对价前收入金额  
			t1.articulation_flag                                                    , 	--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			(case when t1.phase_date is not null and t1.plan_ytd = 0 then 'D' else '' end) as del_ind--phase_date有值且plan_qty为0的是要剔除的
	   from l2_temp_ytd t1
       left join l1_temp_ytd t2
		 on t1.version_code = t2.version_code
		and t1.period_id = t2.period_id--会计期
	    and t1.bg_code = t2.bg_code--bg编码
	    and t1.oversea_desc = t2.oversea_desc--区域描述
	    and t1.lv1_code = t2.lv1_code--	重量级团队lv1编码
	    and t1.lv2_code = t2.lv2_code--	重量级团队lv2编码
	    and t1.currency = t2.currency--币种
	    and t1.l1_name = t2.l1_name
	    and t1.articulation_flag = t2.articulation_flag
	    and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
	  where t1.l2_name is not null or t1.l2_name <> ''	 
	 
	) 
	-- 计算单位成本、单位价格	
	 select t1.version_code,  -- 版本编码
	        t1.period_id															,	--会计期
			t1.phase_date,
			t1.bg_code																,	--bg编码
			t1.bg_name																,	--bg名称
			t1.oversea_desc															,	--区域
			t1.lv1_code																,	--重量级团队lv1编码
			t1.lv1_name																,	--重量级团队lv1描述
			t1.lv2_code																,	--重量级团队lv2编码
			t1.lv2_name																,	--重量级团队lv2名称
			t1.l1_name																,	--l1名称
			t1.l2_name																,	--l2名称
			t1.currency																,	--币种			
			t1.equip_rev_cons_before_ytd,	
			t1.equip_cost_cons_before_ytd,	
		    t1.plan_ytd,	
			t1.ship_ytd,	
			t1.spart_ytd,	
			(case when nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
				    when nvl(t1.spart_ytd,0) = 0 then -999999
				    else t1.equip_cost_cons_before_ytd / t1.spart_ytd end) as unit_cost_ytd,	
			(case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when nvl(t1.spart_ytd,0) = 0 then -999999
				    else t1.equip_rev_cons_before_ytd / t1.spart_ytd end) as unit_price_ytd,
			t1.rev_percent_ytd, --收入占比（本年累计）
			t1.mgp_ratio_ytd, 	 --制毛率（本年累计）	
			t1.articulation_flag,   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind
       from all_temp_01 t1
        ;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'all_temp_ytd 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	
  --入his_ytd_t表
  insert into fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t 
          (
             version_code
            ,period_id
            ,phase_date
            ,bg_code
            ,bg_name
            ,oversea_desc
            ,lv1_code
            ,lv1_name
            ,lv2_code
            ,lv2_name
            ,l1_name
            ,l2_name
            ,currency
            ,equip_rev_cons_before_ytd
            ,equip_cost_cons_before_ytd
            ,plan_ytd
            ,ship_ytd
            ,spart_ytd
            ,unit_cost_ytd
            ,unit_price_ytd
            ,rev_percent_ytd
            ,mgp_ratio_ytd
            ,articulation_flag
            ,remark
            ,created_by
            ,creation_date
            ,last_updated_by
            ,last_update_date
            ,del_flag
			)			
	select  version_code
           ,period_id
           ,phase_date
           ,bg_code
           ,bg_name
           ,oversea_desc
           ,lv1_code
           ,lv1_name
           ,lv2_code
           ,lv2_name
           ,l1_name
           ,l2_name
           ,currency
           ,equip_rev_cons_before_ytd
           ,equip_cost_cons_before_ytd
           ,plan_ytd
           ,ship_ytd
           ,spart_ytd
           ,unit_cost_ytd
           ,unit_price_ytd
           ,rev_percent_ytd
           ,mgp_ratio_ytd
           ,articulation_flag
		   ,'' as remark
	  	   ,-1 as created_by
	  	   ,current_timestamp as creation_date
	  	   ,-1 as last_updated_by
	  	   ,current_timestamp as last_update_date
	  	   ,'N' as del_flag
	  from all_temp_ytd 
	;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '作业对象L2层级数据年累计表(多个版本)'||v_tbl_name2||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


	-- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t;
  
  
    --- 数据入ytd_t表
	  insert into fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t
	        (
               period_id
              ,phase_date
              ,bg_code
              ,bg_name
              ,oversea_desc
              ,lv1_code
              ,lv1_name
              ,lv2_code
              ,lv2_name
              ,l1_name
              ,l2_name
              ,currency
              ,equip_rev_cons_before_ytd
              ,equip_cost_cons_before_ytd
              ,plan_ytd
              ,ship_ytd
              ,spart_ytd
              ,unit_cost_ytd
              ,unit_price_ytd
              ,rev_percent_ytd
              ,mgp_ratio_ytd
              ,articulation_flag
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
		    )
	      select  period_id
			     ,phase_date
			     ,bg_code
			     ,bg_name
			     ,oversea_desc
			     ,lv1_code
			     ,lv1_name
			     ,lv2_code
			     ,lv2_name
			     ,l1_name
			     ,l2_name
			     ,currency
			     ,equip_rev_cons_before_ytd
			     ,equip_cost_cons_before_ytd
			     ,plan_ytd
			     ,ship_ytd
			     ,spart_ytd
			     ,unit_cost_ytd
			     ,unit_price_ytd
			     ,rev_percent_ytd
			     ,mgp_ratio_ytd
				 ,articulation_flag
			     ,'' as remark
	  	         ,-1 as created_by
	  	         ,current_timestamp as creation_date
	  	         ,-1 as last_updated_by
	  	         ,current_timestamp as last_update_date
	  	         ,'N' as del_flag
		    from fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t
		   where version_code = v_max_version_code  -- 取最大的版本数据
		   ;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

   --写日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '作业对象L2层级数据年累计表(一个版本)'||v_tbl_name||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



  end if;

  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.p_dm_pf_capture_exception(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';
	
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l2_info_his_ytd_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l2_info_ytd_t;
	
	
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

