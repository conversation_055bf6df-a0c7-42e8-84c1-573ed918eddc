CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC(OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
	/*
创建时间：2025-07-21
创建人  ：周博孝
背景描述：将 DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC 表中的数据同步到目标表 DWK_ICTPS_PL_LV2_DIM_FCST_I
参数描述：X_SUCCESS_FLAG: 返回状态 SUCCESS / FAIL
事例    ：SELECT FIN_DM_OPT_FOP.F_DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC();
*/

DECLARE
    V_SP_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.F_DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC';
	V_TBL_NAME VARCHAR(100) := 'DWK_ICTPS_PL_LV2_DIM_FCST_I';
	V_DML_ROW_COUNT INTEGER DEFAULT 0;
	V_STEP_NUM   NUMERIC; --步骤号

BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';

	-- 写日志，开始
	V_STEP_NUM := 1;
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '同步表'||V_TBL_NAME||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

    -- 清理目标表中与源表中相同的数据（支持重跑）
    DELETE FROM FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I a
    WHERE  '20' || SUBSTRING(a.YEAR_CODE, '[0-9]+')||
	    CASE WHEN SUBSTRING(a.SCENARIO_CODE, '[0-9]+')::INT-15<10 THEN '0' || CAST(SUBSTRING(a.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
		    ELSE CAST(SUBSTRING(a.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
	    END  IN  (
                    SELECT DISTINCT '20' || SUBSTRING(B.YEAR_CODE, '[0-9]+')||
                        CASE WHEN SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15<10 THEN '0' || CAST(SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
                            ELSE CAST(SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
                            END as PERIOD_ID
                    FROM FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC b
                   );
	              

    -- 插入数据
    INSERT INTO FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I (
        STAT_PERIOD,
        CUBE_CODE,
        SCENARIO_CODE,
        INDEX_LEVEL_NO,
        CURRENCY_CODE,
        VIEW_CODE,
        VIEW_DIM_CODE,
        VIEW_LEVEL_NO,
        PRODUCT_CODE,
        CUSTOMER_DIM_CODE,
        DEPARTMENT_CODE,
        DEPARTMENT_DIM_CODE,
        CAPEXTYPE_CODE,
        MISCELLANEOUS1_DIM_CODE,
        MISCELLANEOUS2_LEVEL_NO,
        MISCELLANEOUS3_DIM_CODE,
        AMOUNT,
        PERIOD_CODE,
        MEASUREMENT_DIM_CODE,
        TRANSACTIONMODEL_DIM_CODE,
        GEOGRAPHY_CODE,
        REPORTCATEGORY_CODE,
        REPORTCATEGORY_LEVEL_NO,
        INDEXDETAIL_CODE,
        PARTITION_VALUE,
        ATTRIBUTE4,
        SCE_FLAG,
        YEAR_CODE,
        VERSION_CODE,
        MEASUREMENT_LEVEL_NO,
        TRANSACTIONMODEL_CODE,
        PRODUCT_DIM_CODE,
        PRODUCT_LEVEL_NO,
        GEOGRAPHY_DIM_CODE,
        GEOGRAPHY_LEVEL_NO,
        CUSTOMER_CODE,
        INDEXDETAIL_DIM_CODE,
        MISCELLANEOUS2_DIM_CODE,
        MISCELLANEOUS3_LEVEL_NO,
        TYPE_CODE,
        PARTITION_CODE,
        ATTRIBUTE5,
        APP_CODE,
        VERSION_DIM_CODE,
        VERSION_LEVEL_NO,
        INDEX_DIM_CODE,
        MEASUREMENT_CODE,
        DEPARTMENT_LEVEL_NO,
        REPORTCATEGORY_DIM_CODE,
        INDEXDETAIL_LEVEL_NO,
        CAPEXTYPE_LEVEL_NO,
        MISCELLANEOUS3_CODE,
        DIM_VERSION_CODE,
        DW_LAST_UPDATE_DATE,
        INDEX_CODE,
        TRANSACTIONMODEL_LEVEL_NO,
        CUSTOMER_LEVEL_NO,
        CAPEXTYPE_DIM_CODE,
        MISCELLANEOUS1_CODE,
        MISCELLANEOUS1_LEVEL_NO,
        MISCELLANEOUS2_CODE,
        ATTRIBUTE1,
        ATTRIBUTE2,
        ATTRIBUTE3,
        CREATE_DATE,
        LAST_UPDATE_DATE,
        FOP_LAST_UPDATE_DATE
    )
    SELECT
        STAT_PERIOD,
        CUBE_CODE,
        SCENARIO_CODE,
        INDEX_LEVEL_NO,
        CURRENCY_CODE,
        VIEW_CODE,
        VIEW_DIM_CODE,
        VIEW_LEVEL_NO,
        PRODUCT_CODE,
        CUSTOMER_DIM_CODE,
        DEPARTMENT_CODE,
        DEPARTMENT_DIM_CODE,
        CAPEXTYPE_CODE,
        MISCELLANEOUS1_DIM_CODE,
        MISCELLANEOUS2_LEVEL_NO,
        MISCELLANEOUS3_DIM_CODE,
        AMOUNT,
        PERIOD_CODE,
        MEASUREMENT_DIM_CODE,
        TRANSACTIONMODEL_DIM_CODE,
        GEOGRAPHY_CODE,
        REPORTCATEGORY_CODE,
        REPORTCATEGORY_LEVEL_NO,
        INDEXDETAIL_CODE,
        PARTITION_VALUE,
        ATTRIBUTE4,
        SCE_FLAG,
        YEAR_CODE,
        VERSION_CODE,
        MEASUREMENT_LEVEL_NO,
        TRANSACTIONMODEL_CODE,
        PRODUCT_DIM_CODE,
        PRODUCT_LEVEL_NO,
        GEOGRAPHY_DIM_CODE,
        GEOGRAPHY_LEVEL_NO,
        CUSTOMER_CODE,
        INDEXDETAIL_DIM_CODE,
        MISCELLANEOUS2_DIM_CODE,
        MISCELLANEOUS3_LEVEL_NO,
        TYPE_CODE,
        PARTITION_CODE,
        ATTRIBUTE5,
        APP_CODE,
        VERSION_DIM_CODE,
        VERSION_LEVEL_NO,
        INDEX_DIM_CODE,
        MEASUREMENT_CODE,
        DEPARTMENT_LEVEL_NO,
        REPORTCATEGORY_DIM_CODE,
        INDEXDETAIL_LEVEL_NO,
        CAPEXTYPE_LEVEL_NO,
        MISCELLANEOUS3_CODE,
        DIM_VERSION_CODE,
        DW_LAST_UPDATE_DATE,
        INDEX_CODE,
        TRANSACTIONMODEL_LEVEL_NO,
        CUSTOMER_LEVEL_NO,
        CAPEXTYPE_DIM_CODE,
        MISCELLANEOUS1_CODE,
        MISCELLANEOUS1_LEVEL_NO,
        MISCELLANEOUS2_CODE,
        ATTRIBUTE1,
        ATTRIBUTE2,
        ATTRIBUTE3,
        CREATE_DATE,
        LAST_UPDATE_DATE,
        FOP_LAST_UPDATE_DATE
    FROM DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC;

    V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '同步表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	ANALYSE FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I;

EXCEPTION
	WHEN OTHERS THEN
		-- 捕获异常并记录日志
		PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	    X_SUCCESS_FLAG := 'FAIL';

END;
$BODY$
LANGUAGE PLPGSQL VOLATILE
  COST 100;
