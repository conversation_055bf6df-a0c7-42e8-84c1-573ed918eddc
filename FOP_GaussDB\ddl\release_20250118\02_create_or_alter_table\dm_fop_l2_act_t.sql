-- ----------------------------
-- Table structure for dm_fop_l2_act_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_l2_act_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_l2_act_t" (
  "period_id" numeric,
  "target_period" varchar(100) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_name" varchar(100) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "unit_price_act" numeric(30,6),
  "unit_cost_act" numeric(30,6),
  "mgp_rate_before_act" numeric(30,6),
  "rev_percent_act" numeric(30,6),
  "carryover_amount_act" numeric(30,8),
  "equip_rev_before_act" numeric(30,8),
  "equip_cost_before_act" numeric(30,8),
  "ship_qty" numeric(30,8),
  "source_table" varchar(100) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."target_period" IS '目标时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."lv2_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."lv2_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."unit_price_act" IS '单位价格';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."unit_cost_act" IS '单位成本';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."mgp_rate_before_act" IS '对价前制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."rev_percent_act" IS '收入占比';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."carryover_amount_act" IS '结转量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."equip_rev_before_act" IS '历史对价前设备收入额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."equip_cost_before_act" IS '历史对价前设备成本额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."ship_qty" IS '发货量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."source_table" IS '来源表';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_l2_act_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_l2_act_t" IS 'L2历史表';

