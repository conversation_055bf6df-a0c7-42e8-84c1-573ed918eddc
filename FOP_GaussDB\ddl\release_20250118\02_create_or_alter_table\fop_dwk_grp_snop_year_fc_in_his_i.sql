-- ----------------------------
-- Table structure for fop_dwk_grp_snop_year_fc_in_his_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i" (
  "siteid" varchar(63) COLLATE "pg_catalog"."default",
  "item" varchar(250) COLLATE "pg_catalog"."default",
  "pgroup" varchar(250) COLLATE "pg_catalog"."default",
  "period" varchar(250) COLLATE "pg_catalog"."default",
  "region_org_id" varchar(625) COLLATE "pg_catalog"."default",
  "region_org_type" varchar(625) COLLATE "pg_catalog"."default",
  "src_sys_locationid" varchar(625) COLLATE "pg_catalog"."default",
  "month" numeric,
  "plan_quantity" numeric,
  "description" varchar(1250) COLLATE "pg_catalog"."default",
  "created_by" varchar(125) COLLATE "pg_catalog"."default",
  "creation_date" timestamp(0),
  "last_updated_by" varchar(125) COLLATE "pg_catalog"."default",
  "last_update_date" timestamp(0),
  "attribute1" varchar(1250) COLLATE "pg_catalog"."default",
  "attribute2" varchar(1250) COLLATE "pg_catalog"."default",
  "attribute3" varchar(1250) COLLATE "pg_catalog"."default",
  "attribute4" varchar(1250) COLLATE "pg_catalog"."default",
  "attribute5" varchar(1250) COLLATE "pg_catalog"."default",
  "planner_code" varchar(63) COLLATE "pg_catalog"."default",
  "plannerid" varchar(250) COLLATE "pg_catalog"."default",
  "fc_type" varchar(125) COLLATE "pg_catalog"."default",
  "product_group" varchar(625) COLLATE "pg_catalog"."default",
  "pmc_code" varchar(625) COLLATE "pg_catalog"."default",
  "sys_id" varchar(250) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(750) COLLATE "pg_catalog"."default",
  "attr4" varchar(750) COLLATE "pg_catalog"."default",
  "port_qty" numeric,
  "plan_unit_quantity" numeric,
  "coa_no" varchar(250) COLLATE "pg_catalog"."default",
  "prod_type_code" varchar(65) COLLATE "pg_catalog"."default",
  "prod_type" varchar(750) COLLATE "pg_catalog"."default",
  "prod_sous_type_code" varchar(65) COLLATE "pg_catalog"."default",
  "prod_sous_type" varchar(750) COLLATE "pg_catalog"."default",
  "unit" varchar(750) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "region_code" varchar(200) COLLATE "pg_catalog"."default",
  "region_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "region_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_region_code" varchar(200) COLLATE "pg_catalog"."default",
  "hrms_region_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_region_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "repoffice_code" varchar(200) COLLATE "pg_catalog"."default",
  "repoffice_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "repoffice_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_repoffice_code" varchar(200) COLLATE "pg_catalog"."default",
  "hrms_repoffice_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_repoffice_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "bd_bg_code" text COLLATE "pg_catalog"."default",
  "bd_bg_cn_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bg_en_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bu_code" text COLLATE "pg_catalog"."default",
  "bd_bu_cn_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bu_en_name" varchar(1250) COLLATE "pg_catalog"."default",
  "custom_attr_1" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_2" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_3" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_4" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_5" varchar(1000) COLLATE "pg_catalog"."default",
  "del_flag" varchar(2) COLLATE "pg_catalog"."default",
  "crt_cycle_id" numeric,
  "last_upd_cycle_id" numeric,
  "crt_job_instance_id" numeric,
  "upd_job_instance_id" numeric,
  "dw_last_update_date" timestamp(0)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."siteid" IS '计划SITE';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."item" IS '物料编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."pgroup" IS '计划产品族';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."period" IS '年度计划期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."region_org_id" IS '区域组织ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."region_org_type" IS '区域组织层级';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."src_sys_locationid" IS '源系统出发地ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."month" IS '月份';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plan_quantity" IS 'SNOP月计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."description" IS '编码描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."created_by" IS '创建人员';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."creation_date" IS '创建日期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."last_updated_by" IS '最新更新人员';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."last_update_date" IS '最新更新日期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attribute1" IS '预留字段1';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attribute2" IS '预留字段2';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attribute3" IS '预留字段3';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attribute4" IS '预留字段4';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attribute5" IS '预留字段5';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."planner_code" IS '主计划员代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plannerid" IS '主计划员ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."fc_type" IS '计划类型';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."product_group" IS '产品族';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."pmc_code" IS 'PMC部门';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."sys_id" IS '源系统ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."attr4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."port_qty" IS '计委包系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."plan_unit_quantity" IS '计划单元计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."coa_no" IS 'COA编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."prod_type_code" IS '产品大类编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."prod_type" IS '产品大类';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."prod_sous_type_code" IS '产品小类编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."prod_sous_type" IS '产品小类';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."unit" IS '单位';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bg_cn_name" IS 'BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv0_prod_rnd_team_code" IS '零级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv0_prod_rd_team_cn_name" IS '零级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv0_prod_rd_team_en_name" IS '零级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_rnd_team_code" IS '一级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_rd_team_cn_name" IS '一级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_rd_team_en_name" IS '一级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_rnd_team_code" IS '二级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_rd_team_cn_name" IS '二级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_rd_team_en_name" IS '二级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_rnd_team_code" IS '三级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_rd_team_cn_name" IS '三级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_rd_team_en_name" IS '三级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_list_code" IS '一级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_list_cn_name" IS '一级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv1_prod_list_en_name" IS '一级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_list_code" IS '二级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_list_cn_name" IS '二级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv2_prod_list_en_name" IS '二级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_list_code" IS '三级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_list_cn_name" IS '三级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."lst_lv3_prod_list_en_name" IS '三级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."region_code" IS '财经地区部编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."region_cn_name" IS '财经地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."region_en_name" IS '财经地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_region_code" IS 'HRMS地区部编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_region_cn_name" IS 'HRMS地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_region_en_name" IS 'HRMS地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."repoffice_code" IS '财经代表处编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."repoffice_cn_name" IS '财经代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."repoffice_en_name" IS '财经代表处英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_repoffice_code" IS 'HRMS代表处编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_repoffice_cn_name" IS 'HRMS代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."hrms_repoffice_en_name" IS 'HRMS代表处英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bg_code" IS '预算BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bg_cn_name" IS '预算BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bg_en_name" IS '预算BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bu_code" IS '预算BU编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bu_cn_name" IS '预算BU中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."bd_bu_en_name" IS '预算BU英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."custom_attr_1" IS '弹性域1';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."custom_attr_2" IS '弹性域2';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."custom_attr_3" IS '弹性域3';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."custom_attr_4" IS '弹性域4';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."custom_attr_5" IS '弹性域5';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."del_flag" IS '删除标记';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."crt_cycle_id" IS '数据创建批号ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."last_upd_cycle_id" IS '数据更新批号ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."crt_job_instance_id" IS '创建任务ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."upd_job_instance_id" IS '更新任务ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i"."dw_last_update_date" IS '最后更新时间';
COMMENT ON TABLE "fin_dm_opt_fop"."fop_dwk_grp_snop_year_fc_in_his_i" IS '业财联接_ICT产业SOP计划计划量_sop编码粒度_年度sop计划量';

