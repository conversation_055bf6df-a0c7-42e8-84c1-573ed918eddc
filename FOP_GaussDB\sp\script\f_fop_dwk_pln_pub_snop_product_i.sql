-- ----------------------------
-- Function structure for f_fop_dwk_pln_pub_snop_product_i
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_fop_dwk_pln_pub_snop_product_i"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_fop_dwk_pln_pub_snop_product_i"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-12-09
创建人  ：柳兴旺 l00521248
背景描述：产品线S&OP预测明细数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_fop_dwk_pln_pub_snop_product_i()

*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_fop_dwk_pln_pub_snop_product_i';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i';
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';       --1表示成功
	

	 --写日志,开始
	insert into fin_dm_opt_fop.dm_pf_log_t
		(log_id,
		 version_id,
		 sp_name,
		 para_list,
		 step_num,
		 cal_log_desc,
		 formula_sql_txt,
		 dml_row_count	,
		 result_status,
		 errbuf,
		 created_by,
		 creation_date)
	values
		(fin_dm_opt_fop.dm_pf_log_s.nextval,
		 null,
		 v_sp_name,
		 '',
		 1,                                             --第一步
		 '产品线S&OP预测明细数据'||v_tbl_name||'：开始运行',
		 null,
		 v_dml_row_count,
		 x_success_flag,
		 null,
		 1,
		 current_timestamp);


		---支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i 
		where phase_date in (select distinct phase_date from fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i_temp);    ---temp表的日期 

		---插入目标表数据
		insert into fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i            ----供应中心发货时点明细数据
		(
			phase_no,
			phase_date,
			month,
			snop_code,
			item_code,
			item_key,
			coa_no,
			prod_key,
			measure_code,
			bucket_id,
			area_id,
			area_key,
			area_type,
			bg_code,
			bg_cn_name,
			bg_en_name,
			lst_lv0_prod_rnd_team_code,
			lst_lv0_prod_rd_team_cn_name,
			lst_lv0_prod_rd_team_en_name,
			lst_lv1_prod_rnd_team_code,
			lst_lv1_prod_rd_team_cn_name,
			lst_lv1_prod_rd_team_en_name,
			lst_lv2_prod_rnd_team_code,
			lst_lv2_prod_rd_team_cn_name,
			lst_lv2_prod_rd_team_en_name,
			lst_lv3_prod_rnd_team_code,
			lst_lv3_prod_rd_team_cn_name,
			lst_lv3_prod_rd_team_en_name,
			site_code,
			site_key,
			header_id,
			line_id,
			bucket_desc,
			review_status,
			pgroup_code,
			end_date,
			port_qty,
			plan_type,
			plan_com_lv1,
			plan_com_lv2,
			plan_com_lv3,
			attr4,
			plan_unit_quantity,
			prod_type_code,
			prod_type,
			prod_sous_type_code,
			prod_sous_type,
			unit,
			lst_lv1_prod_list_code,
			lst_lv1_prod_list_cn_name,
			lst_lv1_prod_list_en_name,
			lst_lv2_prod_list_code,
			lst_lv2_prod_list_cn_name,
			lst_lv2_prod_list_en_name,
			lst_lv3_prod_list_code,
			lst_lv3_prod_list_cn_name,
			lst_lv3_prod_list_en_name,
			material_id,
			product_id,
			product_main_yn,
			quantity,
			ss_id,
			bd_bg_code,
			bd_bg_cn_name,
			bd_bg_en_name,
			bd_bu_code,
			bd_bu_cn_name,
			bd_bu_en_name,
			custom_attr_1,
			custom_attr_2,
			custom_attr_3,
			custom_attr_4,
			custom_attr_5,
			last_update_date_code,
			del_flag,
			crt_cycle_id,
			last_upd_cycle_id,
			crt_job_instance_id,
			upd_job_instance_id,
			dw_last_update_date,
			is_release_version_flag
		)
		select 
			phase_no,
			phase_date,
			month,
			snop_code,
			item_code,
			item_key,
			coa_no,
			prod_key,
			measure_code,
			bucket_id,
			area_id,
			area_key,
			area_type,
			bg_code,
			bg_cn_name,
			bg_en_name,
			lst_lv0_prod_rnd_team_code,
			lst_lv0_prod_rd_team_cn_name,
			lst_lv0_prod_rd_team_en_name,
			lst_lv1_prod_rnd_team_code,
			lst_lv1_prod_rd_team_cn_name,
			lst_lv1_prod_rd_team_en_name,
			lst_lv2_prod_rnd_team_code,
			lst_lv2_prod_rd_team_cn_name,
			lst_lv2_prod_rd_team_en_name,
			lst_lv3_prod_rnd_team_code,
			lst_lv3_prod_rd_team_cn_name,
			lst_lv3_prod_rd_team_en_name,
			site_code,
			site_key,
			header_id,
			line_id,
			bucket_desc,
			review_status,
			pgroup_code,
			end_date,
			port_qty,
			plan_type,
			plan_com_lv1,
			plan_com_lv2,
			plan_com_lv3,
			attr4,
			plan_unit_quantity,
			prod_type_code,
			prod_type,
			prod_sous_type_code,
			prod_sous_type,
			unit,
			lst_lv1_prod_list_code,
			lst_lv1_prod_list_cn_name,
			lst_lv1_prod_list_en_name,
			lst_lv2_prod_list_code,
			lst_lv2_prod_list_cn_name,
			lst_lv2_prod_list_en_name,
			lst_lv3_prod_list_code,
			lst_lv3_prod_list_cn_name,
			lst_lv3_prod_list_en_name,
			material_id,
			product_id,
			product_main_yn,
			quantity,
			ss_id,
			bd_bg_code,
			bd_bg_cn_name,
			bd_bg_en_name,
			bd_bu_code,
			bd_bu_cn_name,
			bd_bu_en_name,
			custom_attr_1,
			custom_attr_2,
			custom_attr_3,
			custom_attr_4,
			custom_attr_5,
			last_update_date_code,
			del_flag,
			crt_cycle_id,
			last_upd_cycle_id,
			crt_job_instance_id,
			upd_job_instance_id,
			dw_last_update_date,
			is_release_version_flag
	   from fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i_temp
  		  ;

	v_dml_row_count := sql%rowcount;          -- 收集数据量

	 -- 写结束日志
	insert into fin_dm_opt_fop.dm_pf_log_t
		(log_id,
		version_id,
		sp_name,
		para_list,
		step_num,
		cal_log_desc,
		formula_sql_txt,
		dml_row_count,
		result_status,
		errbuf,
		created_by,
		creation_date)
	values
		(
		fin_dm_opt_fop.dm_pf_log_s.nextval,
		null,
		v_sp_name,
		'',
		2,                                             --最后一步
		'产品线S&OP预测明细数据'||v_tbl_name||'：结束运行',
		null,
		v_dml_row_count,
		'1',
		null,
		1,
		current_timestamp);
		

exception
  	when others then

      perform fin_dm_opt_fop.p_dm_pf_capture_exception(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败
	
    --收集统计信息
    analyse fin_dm_opt_fop.fop_dwk_pln_pub_snop_product_i;	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

