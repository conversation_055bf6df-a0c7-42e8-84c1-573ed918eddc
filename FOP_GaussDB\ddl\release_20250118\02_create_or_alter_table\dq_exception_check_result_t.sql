-- ----------------------------
-- Table structure for dq_exception_check_result_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dq_exception_check_result_t";
CREATE TABLE "fin_dm_opt_fop"."dq_exception_check_result_t" (
  "exception_check_monitor_id" numeric NOT NULL,
  "period" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "c1" varchar(2000) COLLATE "pg_catalog"."default",
  "c2" varchar(2000) COLLATE "pg_catalog"."default",
  "c3" varchar(2000) COLLATE "pg_catalog"."default",
  "c4" varchar(2000) COLLATE "pg_catalog"."default",
  "c5" varchar(2000) COLLATE "pg_catalog"."default",
  "c6" varchar(2000) COLLATE "pg_catalog"."default",
  "c7" varchar(2000) COLLATE "pg_catalog"."default",
  "c8" varchar(2000) COLLATE "pg_catalog"."default",
  "c9" varchar(2000) COLLATE "pg_catalog"."default",
  "c10" varchar(2000) COLLATE "pg_catalog"."default",
  "c11" varchar(2000) COLLATE "pg_catalog"."default",
  "c12" varchar(2000) COLLATE "pg_catalog"."default",
  "c13" varchar(2000) COLLATE "pg_catalog"."default",
  "c14" varchar(2000) COLLATE "pg_catalog"."default",
  "c15" varchar(2000) COLLATE "pg_catalog"."default",
  "c16" varchar(2000) COLLATE "pg_catalog"."default",
  "c17" varchar(2000) COLLATE "pg_catalog"."default",
  "c18" varchar(2000) COLLATE "pg_catalog"."default",
  "c19" varchar(2000) COLLATE "pg_catalog"."default",
  "c20" varchar(2000) COLLATE "pg_catalog"."default",
  "c21" varchar(2000) COLLATE "pg_catalog"."default",
  "c22" varchar(2000) COLLATE "pg_catalog"."default",
  "c23" varchar(2000) COLLATE "pg_catalog"."default",
  "c24" varchar(2000) COLLATE "pg_catalog"."default",
  "c25" varchar(2000) COLLATE "pg_catalog"."default",
  "c26" varchar(2000) COLLATE "pg_catalog"."default",
  "c27" varchar(2000) COLLATE "pg_catalog"."default",
  "c28" varchar(2000) COLLATE "pg_catalog"."default",
  "c29" varchar(2000) COLLATE "pg_catalog"."default",
  "c30" varchar(2000) COLLATE "pg_catalog"."default",
  "c31" varchar(2000) COLLATE "pg_catalog"."default",
  "c32" varchar(2000) COLLATE "pg_catalog"."default",
  "c33" varchar(2000) COLLATE "pg_catalog"."default",
  "c34" varchar(2000) COLLATE "pg_catalog"."default",
  "c35" varchar(2000) COLLATE "pg_catalog"."default",
  "c36" varchar(2000) COLLATE "pg_catalog"."default",
  "c37" varchar(2000) COLLATE "pg_catalog"."default",
  "c38" varchar(2000) COLLATE "pg_catalog"."default",
  "c39" varchar(2000) COLLATE "pg_catalog"."default",
  "c40" varchar(2000) COLLATE "pg_catalog"."default",
  "c41" varchar(2000) COLLATE "pg_catalog"."default",
  "c42" varchar(2000) COLLATE "pg_catalog"."default",
  "c43" varchar(2000) COLLATE "pg_catalog"."default",
  "c44" varchar(2000) COLLATE "pg_catalog"."default",
  "c45" varchar(2000) COLLATE "pg_catalog"."default",
  "c46" varchar(2000) COLLATE "pg_catalog"."default",
  "c47" varchar(2000) COLLATE "pg_catalog"."default",
  "c48" varchar(2000) COLLATE "pg_catalog"."default",
  "c49" varchar(2000) COLLATE "pg_catalog"."default",
  "c50" varchar(2000) COLLATE "pg_catalog"."default",
  "c51" varchar(2000) COLLATE "pg_catalog"."default",
  "c52" varchar(2000) COLLATE "pg_catalog"."default",
  "c53" varchar(2000) COLLATE "pg_catalog"."default",
  "c54" varchar(2000) COLLATE "pg_catalog"."default",
  "c55" varchar(2000) COLLATE "pg_catalog"."default",
  "c56" varchar(2000) COLLATE "pg_catalog"."default",
  "c57" varchar(2000) COLLATE "pg_catalog"."default",
  "c58" varchar(2000) COLLATE "pg_catalog"."default",
  "c59" varchar(2000) COLLATE "pg_catalog"."default",
  "c60" varchar(2000) COLLATE "pg_catalog"."default",
  "c61" varchar(2000) COLLATE "pg_catalog"."default",
  "c62" varchar(2000) COLLATE "pg_catalog"."default",
  "c63" varchar(2000) COLLATE "pg_catalog"."default",
  "c64" varchar(2000) COLLATE "pg_catalog"."default",
  "c65" varchar(2000) COLLATE "pg_catalog"."default",
  "c66" varchar(2000) COLLATE "pg_catalog"."default",
  "c67" varchar(2000) COLLATE "pg_catalog"."default",
  "c68" varchar(2000) COLLATE "pg_catalog"."default",
  "c69" varchar(2000) COLLATE "pg_catalog"."default",
  "c70" varchar(2000) COLLATE "pg_catalog"."default",
  "c71" varchar(2000) COLLATE "pg_catalog"."default",
  "c72" varchar(2000) COLLATE "pg_catalog"."default",
  "c73" varchar(2000) COLLATE "pg_catalog"."default",
  "c74" varchar(2000) COLLATE "pg_catalog"."default",
  "c75" varchar(2000) COLLATE "pg_catalog"."default",
  "c76" varchar(2000) COLLATE "pg_catalog"."default",
  "c77" varchar(2000) COLLATE "pg_catalog"."default",
  "c78" varchar(2000) COLLATE "pg_catalog"."default",
  "c79" varchar(2000) COLLATE "pg_catalog"."default",
  "c80" varchar(2000) COLLATE "pg_catalog"."default",
  "c81" varchar(2000) COLLATE "pg_catalog"."default",
  "c82" varchar(2000) COLLATE "pg_catalog"."default",
  "c83" varchar(2000) COLLATE "pg_catalog"."default",
  "c84" varchar(2000) COLLATE "pg_catalog"."default",
  "c85" varchar(2000) COLLATE "pg_catalog"."default",
  "c86" varchar(2000) COLLATE "pg_catalog"."default",
  "c87" varchar(2000) COLLATE "pg_catalog"."default",
  "c88" varchar(2000) COLLATE "pg_catalog"."default",
  "c89" varchar(2000) COLLATE "pg_catalog"."default",
  "c90" varchar(2000) COLLATE "pg_catalog"."default",
  "c91" varchar(2000) COLLATE "pg_catalog"."default",
  "c92" varchar(2000) COLLATE "pg_catalog"."default",
  "c93" varchar(2000) COLLATE "pg_catalog"."default",
  "c94" varchar(2000) COLLATE "pg_catalog"."default",
  "c95" varchar(2000) COLLATE "pg_catalog"."default",
  "c96" varchar(2000) COLLATE "pg_catalog"."default",
  "c97" varchar(2000) COLLATE "pg_catalog"."default",
  "c98" varchar(2000) COLLATE "pg_catalog"."default",
  "c99" varchar(2000) COLLATE "pg_catalog"."default",
  "c100" varchar(2000) COLLATE "pg_catalog"."default",
  "row_number" numeric,
  "exception_check_cause" varchar(1000) COLLATE "pg_catalog"."default",
  "original_status" numeric,
  "real_status" numeric,
  "unique_id" varchar(16) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."exception_check_monitor_id" IS 'MONITOR ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."period" IS '会计期，以会计期做分期表';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c1" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c2" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c3" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c4" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c5" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c6" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c7" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c8" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c9" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c10" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c11" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c12" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c13" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c14" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c15" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c16" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c17" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c18" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c19" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c20" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c21" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c22" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c23" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c24" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c25" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c26" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c27" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c28" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c29" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c30" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c31" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c32" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c33" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c34" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c35" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c36" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c37" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c38" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c39" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c40" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c41" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c42" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c43" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c44" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c45" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c46" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c47" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c48" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c49" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c50" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c51" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c52" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c53" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c54" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c55" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c56" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c57" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c58" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c59" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c60" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c61" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c62" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c63" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c64" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c65" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c66" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c67" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c68" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c69" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c70" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c71" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c72" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c73" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c74" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c75" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c76" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c77" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c78" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c79" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c80" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c81" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c82" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c83" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c84" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c85" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c86" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c87" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c88" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c89" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c90" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c91" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c92" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c93" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c94" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c95" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c96" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c97" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c98" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c99" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."c100" IS '动态存储规则校验结果数据';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."row_number" IS '行号';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."original_status" IS '原始检查结果状态，1代表正常(绿灯)，2代表疑似异常(黄灯)和3代表确认异常(红灯)';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_result_t"."real_status" IS '实际处理后的状态，1代表正常(绿灯)，2代表疑似异常(黄灯)和3代表确认异常(红灯)';

