-- ----------------------------
-- Table structure for fop_dwk_snop_year_fc_in_his_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwk_snop_year_fc_in_his_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwk_snop_year_fc_in_his_i" (
  "siteid" varchar(50) COLLATE "pg_catalog"."default",
  "item" varchar(200) COLLATE "pg_catalog"."default",
  "pgroup" varchar(200) COLLATE "pg_catalog"."default",
  "period" varchar(200) COLLATE "pg_catalog"."default",
  "region_org_id" varchar(500) COLLATE "pg_catalog"."default",
  "region_org_type" varchar(500) COLLATE "pg_catalog"."default",
  "src_sys_locationid" varchar(500) COLLATE "pg_catalog"."default",
  "month" numeric,
  "plan_quantity" numeric,
  "description" varchar(1000) COLLATE "pg_catalog"."default",
  "created_by" varchar(100) COLLATE "pg_catalog"."default",
  "creation_date" timestamp(0),
  "last_updated_by" varchar(100) COLLATE "pg_catalog"."default",
  "last_update_date" timestamp(0),
  "attribute1" varchar(1000) COLLATE "pg_catalog"."default",
  "attribute2" varchar(1000) COLLATE "pg_catalog"."default",
  "attribute3" varchar(1000) COLLATE "pg_catalog"."default",
  "attribute4" varchar(1000) COLLATE "pg_catalog"."default",
  "attribute5" varchar(1000) COLLATE "pg_catalog"."default",
  "planner_code" varchar(50) COLLATE "pg_catalog"."default",
  "plannerid" varchar(200) COLLATE "pg_catalog"."default",
  "fc_type" varchar(100) COLLATE "pg_catalog"."default",
  "product_group" varchar(500) COLLATE "pg_catalog"."default",
  "pmc_code" varchar(500) COLLATE "pg_catalog"."default",
  "sys_id" varchar(200) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(600) COLLATE "pg_catalog"."default",
  "attr4" varchar(600) COLLATE "pg_catalog"."default",
  "port_qty" numeric,
  "plan_unit_quantity" numeric,
  "coa_no" varchar(200) COLLATE "pg_catalog"."default",
  "prod_type_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_type" varchar(600) COLLATE "pg_catalog"."default",
  "prod_sous_type_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_sous_type" varchar(600) COLLATE "pg_catalog"."default",
  "unit" varchar(600) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "region_code" varchar(200) COLLATE "pg_catalog"."default",
  "region_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "region_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_region_code" varchar(50) COLLATE "pg_catalog"."default",
  "hrms_region_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "hrms_region_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "repoffice_code" varchar(200) COLLATE "pg_catalog"."default",
  "repoffice_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "repoffice_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "hrms_repoffice_code" varchar(50) COLLATE "pg_catalog"."default",
  "hrms_repoffice_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "hrms_repoffice_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "bd_bg_code" varchar(4000) COLLATE "pg_catalog"."default",
  "bd_bg_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bg_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bu_code" varchar(4000) COLLATE "pg_catalog"."default",
  "bd_bu_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "bd_bu_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_1" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_2" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_3" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_4" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_5" varchar(1000) COLLATE "pg_catalog"."default",
  "del_flag" varchar(1) COLLATE "pg_catalog"."default",
  "crt_cycle_id" numeric,
  "last_upd_cycle_id" numeric,
  "crt_job_instance_id" numeric,
  "upd_job_instance_id" numeric,
  "dw_last_update_date" timestamp(0)
)
;

