DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWR_DIM_PRODUCTDIMENSION_D;
CREATE TABLE FIN_DM_OPT_FOP.DWR_DIM_PRODUCTDIMENSION_D (
    ROW_ID BIGSERIAL PRIMARY KEY,
	DIMENSION_KEY NUMERIC,
	<PERSON>IM<PERSON><PERSON>ON_CODE CHARACTER VARYING(500),
	DIMENSION_CN_NAME CHARACTER VARYING(2000),
	DIMENSION_EN_NAME CHARACTER VARYING(2000),
	DIMENSION_DESC CHARACTER VARYING(2000),
	DIMENSION_LEVEL CHARACTER VARYING(1000),
	DIMENSION_TYPE CHARACTER VARYING(500),
	DIMENSION_STATUS CHARACTER VARYING(1000),
	PRODUCT_DIMENSION_CODE CHARACTER VARYING(500),
	PRODUCT_DIMENSION_CN_NAME CHARACTER VARYING(2000),
	PRODUCT_DIMENSION_EN_NAME CHARACTER VARYING(2000),
	INDUSTRY_CATG_CODE CHARACTER VARYING(100),
	INDUSTRY_CATG_CN_NAME CHARACTER VARYING(1000),
	INDUSTRY_CATG_EN_NAME CHARACTER VARYING(1000),
	INDUSTRY_DIMENSION_CODE CHARACTER VARYING(600),
	INDUSTRY_DIMENSION_CN_NAME CHARACTER VARYING(3000),
	INDUSTRY_DIMENSION_EN_NAME CHARACTER VARYING(3000),
	DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
	DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(2000),
	DIMENSION_SUBCATEGORY_EN_NAME CHARACTER VARYING(2000),
	DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
	DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(2000),
	DIMENSION_SUB_DETAIL_EN_NAME CHARACTER VARYING(2000),
	DEL_FLAG CHARACTER VARYING(1),
	SS_ID NUMERIC,
	SCD_ACTIVE_IND NUMERIC,
	SCD_ACTIVE_BEGIN_DATE TIMESTAMP(0) WITHOUT TIME ZONE,
	SCD_ACTIVE_END_DATE TIMESTAMP(0) WITHOUT TIME ZONE,
	CRT_CYCLE_ID NUMERIC,
	LAST_UPD_CYCLE_ID NUMERIC,
	CRT_JOB_INSTANCE_ID NUMERIC,
	UPD_JOB_INSTANCE_ID NUMERIC,
	DW_LAST_UPDATE_DATE TIMESTAMP(0) WITHOUT TIME ZONE,
	PRODUCT_DIMENSION_GROUP CHARACTER VARYING(2000),
	PRODUCT_DIMENSION_GROUP_EN_NAME CHARACTER VARYING(2000),
	PRODUCT_DIMENSION_GROUP_CODE CHARACTER VARYING(2000),
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_KEY IS '量纲KEY';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_CODE IS '量纲编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_CN_NAME IS '量纲中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_EN_NAME IS '量纲英文名称　';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_DESC IS '量纲描述　';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_LEVEL IS '量纲层级';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_TYPE IS '量纲类型';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_STATUS IS '量纲状态';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_CODE IS '产品量纲编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_CN_NAME IS '产品量纲中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_EN_NAME IS '产品量纲英文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_CATG_CODE IS '量纲归属产业编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_CATG_CN_NAME IS '量纲归属产业中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_CATG_EN_NAME IS '量纲归属产业英文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_DIMENSION_CODE IS '产业量纲编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_DIMENSION_CN_NAME IS '产业量纲中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.INDUSTRY_DIMENSION_EN_NAME IS '产业量纲英文名称　';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUBCATEGORY_CODE IS '量纲子类编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUBCATEGORY_CN_NAME IS '量纲子类中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUBCATEGORY_EN_NAME IS '量纲子类英文名称　';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUB_DETAIL_CODE IS '量纲子类明细编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUB_DETAIL_CN_NAME IS '量纲子类明细中文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DIMENSION_SUB_DETAIL_EN_NAME IS '量纲子类明细英文名称　';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DEL_FLAG IS '数据审计字段，表明该行记录删除';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.SS_ID IS '源系统ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.SCD_ACTIVE_IND IS '缓慢变化有效标识';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.SCD_ACTIVE_BEGIN_DATE IS '缓慢变化开始时间';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.SCD_ACTIVE_END_DATE IS '缓慢变化结束时间';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.CRT_CYCLE_ID IS '数据创建批号ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.LAST_UPD_CYCLE_ID IS '数据更新批号ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.CRT_JOB_INSTANCE_ID IS '数据被创建时的任务ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.UPD_JOB_INSTANCE_ID IS '数据被更新时的任务ID';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.DW_LAST_UPDATE_DATE IS 'DW最后更新日期';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_GROUP IS '量纲分组';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_GROUP_EN_NAME IS '量纲分组英文名称';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.PRODUCT_DIMENSION_GROUP_CODE IS '量纲分组编码';
COMMENT ON COLUMN DWR_DIM_PRODUCTDIMENSION_D.LAST_UPDATE_DATE IS 'FOP最后更新日期';
