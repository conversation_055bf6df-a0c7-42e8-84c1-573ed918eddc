-- ----------------------------
-- Function structure for f_dm_fop_spart_lv1_info_ytd_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_lv1_info_ytd_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_lv1_info_ytd_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-06-01
创建人  ：鲁广武  lwx1186472
背景描述：重量级团队LV1层级数据年累计表（只保留一个版本的数据）(提供给知识表示)
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_lv1_info_ytd_t()										   		  
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_lv1_info_ytd_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t';
	v_tbl_name2 varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t';	
	v_max_version_code varchar(50);  -- 宽表的最大版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;
	
	
begin
	x_success_flag := '1';           --1表示成功	


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '重量级团队LV1层级数据年累计表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的版本编码:'||p_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    	  


--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
  ---判断p_version_code在his_t表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t where version_code = p_version_code)
   then     

     ----计算ytd的相应指标
     ---删除重跑his_ytd表中已有传入参数的数据
	 delete from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t where version_code = p_version_code;

	  
   --计算ytd指标入目标表  
	  insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t
      (version_code                   --版本编码
       ,period_id                     --会计期
       ,bg_code                       --BG编码
       ,bg_name                       --BG名称
       ,oversea_desc                  --区域
       ,lv1_code                      --重量级团队LV1编码
       ,lv1_name                      --重量级团队LV1描述
       ,currency                      --币种
       ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
       ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
       ,mgp_ratio_ytd                 --制毛率（本年累计）
       ,remark                        --备注
       ,created_by                    --创建人
       ,creation_date                 --创建时间
       ,last_updated_by               --修改人
       ,last_update_date              --修改时间
       ,del_flag                      --是否删除
	   )
with ytd_tmp as ( 
	select 
	       version_code                          
          ,period_id							
          ,bg_code                               
          ,bg_name                               
          ,oversea_desc                          
          ,lv1_code                              
          ,lv1_name 
          ,currency			  
          ,sum(nvl(equip_rev_cons_after_amt,0))over(partition by version_code
                                                                 ,substr(period_id,1,4)			
                                                                 ,bg_code     
                                                                 ,bg_name     
                                                                 ,oversea_desc
                                                                 ,lv1_code   
                                                                 ,lv1_name
                                                                 ,currency 
                                                        order by period_id) as equip_rev_cons_after_ytd              ---设备收入额（对价后）
          ,sum(nvl(equip_cost_cons_after_amt,0))over(partition by version_code
                                                                  ,substr(period_id,1,4)		
                                                                  ,bg_code     
                                                                  ,bg_name     
                                                                  ,oversea_desc
                                                                  ,lv1_code   
                                                                  ,lv1_name
                                                                  ,currency 
                                                         order by period_id) as equip_cost_cons_after_ytd             ---设备成本额（对价后）
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t 
	where version_code = p_version_code
)	  
	select 
	       version_code                          
          ,period_id							
          ,bg_code                               
          ,bg_name                               
          ,oversea_desc                          
          ,lv1_code                              
          ,lv1_name 
          ,currency			  
          ,equip_rev_cons_after_ytd              ---设备收入额（对价后）
          ,equip_cost_cons_after_ytd             ---设备成本额（对价后）
		  ,case when equip_rev_cons_after_ytd = 0 and equip_cost_cons_after_ytd = 0 then 0
		  	    when equip_rev_cons_after_ytd = 0 then -999999
                else 1-equip_cost_cons_after_ytd/equip_rev_cons_after_ytd end as mgp_ratio_ytd   ---制毛率	  
		  ,'' as remark							  ---备注
		  ,-1 as created_by                       ---创建人
		  ,current_timestamp as creation_date     ---创建时间
		  ,-1 as last_updated_by                  ---修改人
		  ,current_timestamp as last_update_date  ---修改时间
		  ,'N' as del_flag                         ---是否删除	
	 from ytd_tmp
      ;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '重量级团队LV1层级数据年累计历史表(多个版本)'||v_tbl_name2||',传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;	  


     ---删除重跑目标表中的数据，只留一个版本的数据
	 truncate table fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t;

  -- 目标表取his_t表传入参数的数据
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t 
     (
       period_id                     --会计期
      ,bg_code                       --BG编码
      ,bg_name                       --BG名称
      ,oversea_desc                  --区域
      ,lv1_code                      --重量级团队LV1编码
      ,lv1_name                      --重量级团队LV1描述
      ,currency                      --币种
      ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
      ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
      ,mgp_ratio_ytd                 --制毛率（本年累计）
	  ,remark                        --备注
	  ,created_by                    --创建人
	  ,creation_date                 --创建时间
	  ,last_updated_by               --修改人
	  ,last_update_date              --修改时间
	  ,del_flag                      --是否删除
     )	
    select 
           period_id                     --会计期
          ,bg_code                       --BG编码
          ,bg_name                       --BG名称
          ,oversea_desc                  --区域
          ,lv1_code                      --重量级团队LV1编码
          ,lv1_name                      --重量级团队LV1描述
          ,currency                      --币种
          ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
          ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
          ,mgp_ratio_ytd                 --制毛率（本年累计）
		  ,'' as remark							  ---备注
		  ,-1 as created_by                       ---创建人
		  ,current_timestamp as creation_date     ---创建时间
		  ,-1 as last_updated_by                  ---修改人
		  ,current_timestamp as last_update_date  ---修改时间
		  ,'N' as del_flag                         ---是否删除		
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t
    where version_code = p_version_code      --his_ytd_t表传入的版本号	  
    ;	

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '重量级团队LV1层级数据年累计表(一个版本)'||v_tbl_name||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  else
		
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => '宽表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;



--当p_version_code无值时，取最大的版本号
else 

    --取his_t表最大版本号
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code 
	  from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t)
     group by substr(version_code,1,6);  -- 取his_t表最大版本号		


     ----计算ytd的相应指标
     ---删除重跑his_ytd表中已有最大版本的数据
	 delete from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t where version_code = v_max_version_code;

	  
   --计算ytd指标入目标表  
	  insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t
      (version_code                   --版本编码
       ,period_id                     --会计期
       ,bg_code                       --BG编码
       ,bg_name                       --BG名称
       ,oversea_desc                  --区域
       ,lv1_code                      --重量级团队LV1编码
       ,lv1_name                      --重量级团队LV1描述
       ,currency                      --币种
       ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
       ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
       ,mgp_ratio_ytd                 --制毛率（本年累计）
       ,remark                        --备注
       ,created_by                    --创建人
       ,creation_date                 --创建时间
       ,last_updated_by               --修改人
       ,last_update_date              --修改时间
       ,del_flag                      --是否删除
	   )
with ytd_tmp as ( 
	select 
	       version_code                          
          ,period_id							
          ,bg_code                               
          ,bg_name                               
          ,oversea_desc                          
          ,lv1_code                              
          ,lv1_name 
          ,currency			  
          ,sum(nvl(equip_rev_cons_after_amt,0))over(partition by version_code
                                                                 ,substr(period_id,1,4)			
                                                                 ,bg_code     
                                                                 ,bg_name     
                                                                 ,oversea_desc
                                                                 ,lv1_code   
                                                                 ,lv1_name
                                                                 ,currency 
                                                        order by period_id) as equip_rev_cons_after_ytd              ---设备收入额（对价后）
          ,sum(nvl(equip_cost_cons_after_amt,0))over(partition by version_code
                                                                  ,substr(period_id,1,4)		
                                                                  ,bg_code     
                                                                  ,bg_name     
                                                                  ,oversea_desc
                                                                  ,lv1_code   
                                                                  ,lv1_name
                                                                  ,currency 
                                                         order by period_id) as equip_cost_cons_after_ytd             ---设备成本额（对价后）
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t 
	where version_code = v_max_version_code
)	  
	select 
	       version_code                          
          ,period_id							
          ,bg_code                               
          ,bg_name                               
          ,oversea_desc                          
          ,lv1_code                              
          ,lv1_name 
          ,currency			  
          ,equip_rev_cons_after_ytd              ---设备收入额（对价后）
          ,equip_cost_cons_after_ytd             ---设备成本额（对价后）
		  ,case when equip_rev_cons_after_ytd = 0 and equip_cost_cons_after_ytd = 0 then 0
		  	    when equip_rev_cons_after_ytd = 0 then -999999
                else 1-equip_cost_cons_after_ytd/equip_rev_cons_after_ytd end as mgp_ratio_ytd   ---制毛率	  
		  ,'' as remark							  ---备注
		  ,-1 as created_by                       ---创建人
		  ,current_timestamp as creation_date     ---创建时间
		  ,-1 as last_updated_by                  ---修改人
		  ,current_timestamp as last_update_date  ---修改时间
		  ,'N' as del_flag                         ---是否删除	
	 from ytd_tmp
      ;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '重量级团队LV1层级数据年累计历史表(多个版本)'||v_tbl_name2||',最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


     ---删除重跑目标表中的数据，只留一个版本的数据
	 truncate table fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t;

  -- 目标表取his_t表传入参数的数据
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t 
     (
       period_id                     --会计期
      ,bg_code                       --BG编码
      ,bg_name                       --BG名称
      ,oversea_desc                  --区域
      ,lv1_code                      --重量级团队LV1编码
      ,lv1_name                      --重量级团队LV1描述
      ,currency                      --币种
      ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
      ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
      ,mgp_ratio_ytd                 --制毛率（本年累计）
	  ,remark                        --备注
	  ,created_by                    --创建人
	  ,creation_date                 --创建时间
	  ,last_updated_by               --修改人
	  ,last_update_date              --修改时间
	  ,del_flag                      --是否删除
     )	
    select 
           period_id                     --会计期
          ,bg_code                       --BG编码
          ,bg_name                       --BG名称
          ,oversea_desc                  --区域
          ,lv1_code                      --重量级团队LV1编码
          ,lv1_name                      --重量级团队LV1描述
          ,currency                      --币种
          ,equip_rev_cons_after_ytd      --设备收入本年累计（对价后）
          ,equip_cost_cons_after_ytd     --设备成本本年累计（对价后）
          ,mgp_ratio_ytd                 --制毛率（本年累计）
		  ,'' as remark							  ---备注
		  ,-1 as created_by                       ---创建人
		  ,current_timestamp as creation_date     ---创建时间
		  ,-1 as last_updated_by                  ---修改人
		  ,current_timestamp as last_update_date  ---修改时间
		  ,'N' as del_flag                         ---是否删除		
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t
    where version_code = v_max_version_code      --his_ytd_t表最大版本号	  
    ;	

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '重量级团队LV1层级数据年累计表(一个版本)'||v_tbl_name||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

end if
   ;


exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	       --2001表示失败
	
    --收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_lv1_info_his_ytd_t;
    analyse fin_dm_opt_fop.dm_fop_spart_lv1_info_ytd_t;		
	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

