#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2010-2022. All rights reserved.
set -ex
#获取jenkins的$BUILD_NUMBER，获取时间戳，获取build随机数

    if [ -e /proc/sys/kernel/random/uuid ] && [ -r /proc/sys/kernel/random/uuid ];then
        #build=`cat /proc/sys/kernel/random/uuid| cksum | cut -f1 -d" "`
		build="$(cat /proc/sys/kernel/random/uuid| cksum | cut -f1 -d" ")"
    else
        build=${RANDOM}
    fi
    datetime="$(date +%Y%m%d%H%M%S)"
    BUILD_NUMBER="${datetime}.${build}"

#微服务名称
SERVICE_NAME=""
#包所在的项目路径
PACKAGE_PATH=""
#包名称
PACKAGE_NAME=""
#默认profilesActive

# 项目名称
PROJECT_NAME="optfcstprofitsdb"


echo "Release is ${isRelease}"
#判断当前构建是否为版本构建，以及定义构建变量(包版本,包服务名称,包编译存放路径,包类型,包编译名称,包打包名称)
if [ "${isRelease}"x = "false"x ];then
    SERVICE_VERSION='1.0.0-SNAPSHOT'
	#版本号+时间戳+build随机数写入buildInfo.properties
	echo "buildVersion=${SERVICE_VERSION}.$BUILD_NUMBER">buildInfo.properties
	#sed -i 's/VERSION/'${SERVICE_VERSION}.${BUILD_NUMBER}'/g' appspec.yml
	#压缩包名称
	PACKAGE_TAR_PATH="${SERVICE_NAME}_${SERVICE_VERSION}.${BUILD_NUMBER}"
	#执行工程编译
	workdir=$(cd $(dirname $0); pwd)
	cd $workdir
  mkdir target

  # 打包临时文件目录
  mkdir -p DeployScript/ddl

  # 数据库目录是否存在
  if [ ! -d "${database}" ]; then
    echo "error: no directory ${database}"
    exit 1
  fi

  # 全量部署
  cp ${database}/master.xml DeployScript/
  cat ${database}/master.xml | grep -oP '<include file="\K[^"]+' | while read -r line;
  do
    source_path=$(echo "${line}" | sed 's/\\/\//g')
    dir_path=$(dirname ${source_path})

    cp -r ${database}/${dir_path} DeployScript/${dir_path}

  done
  cd $workdir/DeployScript
  ls

  cd $workdir
  cd DeployScript
  zip -r ${PROJECT_NAME}_${database}_${version}.zip *
  cd $workdir
  cp DeployScript/${PROJECT_NAME}_${database}_${version}.zip target

  ls

elif [ "${isRelease}"x = "true"x ];then
    SERVICE_VERSION=${ENV_RELEASE_VERSION}
	#版本号+时间戳+build随机数写入buildInfo.properties
	echo "buildVersion=${SERVICE_VERSION}">buildInfo.properties
	#sed -i 's/VERSION/'${SERVICE_VERSION}'/g' appspec.yml
	#压缩包名称
	PACKAGE_TAR_PATH="${SERVICE_NAME}_${SERVICE_VERSION}"
	#执行工程编译
	workdir=$(cd $(dirname $0); pwd)
	cd $workdir
  mkdir target

  # 打包临时文件目录
  mkdir -p DeployScript/ddl

  # 数据库目录是否存在
  if [ ! -d "${database}" ]; then
    echo "error: no directory ${database}"
    exit 1
  fi

  # 全量部署
  cp ${database}/master.xml DeployScript/
  cat ${database}/master.xml | grep -oP '<include file="\K[^"]+' | while read -r line;
  do
    source_path=$(echo "${line}" | sed 's/\\/\//g')
    dir_path=$(dirname ${source_path})

    cp -r ${database}/${dir_path} DeployScript/${dir_path}

  done
  cd $workdir/DeployScript
  ls

  cd $workdir
  cd DeployScript
  zip -r release_${PROJECT_NAME}_${database}_${version}.zip *
  cd $workdir
  cp DeployScript/release_${PROJECT_NAME}_${database}_${version}.zip target

  ls

fi