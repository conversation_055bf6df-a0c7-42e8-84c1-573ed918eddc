-- ----------------------------
-- Table structure for dm_ps_ai_forecast_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_ps_ai_forecast_t";
CREATE TABLE "fin_dm_opt_fop"."dm_ps_ai_forecast_t" (
  "period_id" numeric(50,0),
  "phase_date" varchar(60) COLLATE "pg_catalog"."default",
  "ver_lv1_code" varchar(50) COLLATE "pg_catalog"."default",
  "ver_lv1_cn_name" varchar(100) COLLATE "pg_catalog"."default",
  "ver_lv1_en_name" varchar(100) COLLATE "pg_catalog"."default",
  "dste_scenario_lv1_code" varchar(50) COLLATE "pg_catalog"."default",
  "dste_scenario_lv1_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "dste_scenario_lv1_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "report_item_code" varchar(50) COLLATE "pg_catalog"."default",
  "report_item_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "report_item_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "report_item_fcst" numeric(38,10),
  "report_item_fcst_conf" numeric(38,10),
  "report_item_fcst_upper" numeric(38,10),
  "report_item_fcst_lower" numeric(38,10),
  "lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "target_period" varchar(100) COLLATE "pg_catalog"."default",
  "fcst_type" varchar(50) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "is_release_version_flag" varchar(10) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."period_id" IS '预测会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."phase_date" IS 'S&OP期次';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."ver_lv1_code" IS '版本编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."ver_lv1_cn_name" IS '版本中文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."ver_lv1_en_name" IS '版本英文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."dste_scenario_lv1_code" IS '经营活动L1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."dste_scenario_lv1_cn_name" IS '经营活动L1中文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."dste_scenario_lv1_en_name" IS '经营活动L1英文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_code" IS '报表项编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_cn_name" IS '报表项中文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_en_name" IS '报表项英文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_fcst" IS '报表项的预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_fcst_conf" IS '报表项的预测置信度';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_fcst_upper" IS '报表项的预测上界';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."report_item_fcst_lower" IS '报表项的预测下界';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv1_prod_rnd_team_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv1_prod_rd_team_cn_name" IS '重量级团队LV1中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv2_prod_rnd_team_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv2_prod_rd_team_cn_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."lv2_prod_rd_team_en_name" IS '重量级团队LV2英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."bg_cn_name" IS 'BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."target_period" IS '预测时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."fcst_type" IS '预测方法';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."is_release_version_flag" IS '是否发布版本（Y、N、空值）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_ps_ai_forecast_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_ps_ai_forecast_t" IS 'ICT产业损益AI预测数_重量级团队LV2层级';

