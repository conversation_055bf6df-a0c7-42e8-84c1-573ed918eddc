-- ----------------------------
-- View structure for dm_ps_ai_forecast_v
-- ----------------------------
DROP VIEW IF EXISTS "fin_dm_opt_fop"."dm_ps_ai_forecast_v";
CREATE VIEW "fin_dm_opt_fop"."dm_ps_ai_forecast_v" AS  SELECT dm_ps_ai_forecast_t.period_id, dm_ps_ai_forecast_t.phase_date, 
    dm_ps_ai_forecast_t.ver_lv1_code, dm_ps_ai_forecast_t.ver_lv1_cn_name, 
    dm_ps_ai_forecast_t.ver_lv1_en_name, 
    dm_ps_ai_forecast_t.dste_scenario_lv1_code, 
    dm_ps_ai_forecast_t.dste_scenario_lv1_cn_name, 
    dm_ps_ai_forecast_t.dste_scenario_lv1_en_name, 
    dm_ps_ai_forecast_t.report_item_code, 
    dm_ps_ai_forecast_t.report_item_cn_name, 
    dm_ps_ai_forecast_t.report_item_en_name, 
    dm_ps_ai_forecast_t.report_item_fcst, 
    dm_ps_ai_forecast_t.report_item_fcst_conf, 
    dm_ps_ai_forecast_t.report_item_fcst_upper, 
    dm_ps_ai_forecast_t.report_item_fcst_lower, 
    dm_ps_ai_forecast_t.lv1_prod_rnd_team_code, 
    dm_ps_ai_forecast_t.lv1_prod_rd_team_cn_name, 
    dm_ps_ai_forecast_t.lv1_prod_rd_team_en_name, 
    dm_ps_ai_forecast_t.lv2_prod_rnd_team_code, 
    dm_ps_ai_forecast_t.lv2_prod_rd_team_cn_name, 
    dm_ps_ai_forecast_t.lv2_prod_rd_team_en_name, dm_ps_ai_forecast_t.bg_code, 
    dm_ps_ai_forecast_t.bg_cn_name, dm_ps_ai_forecast_t.bg_en_name, 
    dm_ps_ai_forecast_t.oversea_desc, dm_ps_ai_forecast_t.target_period, 
    dm_ps_ai_forecast_t.fcst_type, dm_ps_ai_forecast_t.currency, 
    dm_ps_ai_forecast_t.is_release_version_flag, dm_ps_ai_forecast_t.remark, 
    dm_ps_ai_forecast_t.created_by, dm_ps_ai_forecast_t.creation_date, 
    dm_ps_ai_forecast_t.last_updated_by, dm_ps_ai_forecast_t.last_update_date, 
    dm_ps_ai_forecast_t.del_flag
   FROM dm_ps_ai_forecast_t;

