-- ----------------------------
-- Function structure for f_dm_fop_spart_l1_tgt_period_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l1_tgt_period_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l1_tgt_period_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-06-01
创建人  ：鲁广武  lwx1186472
背景描述：作业对象L1层级目标时点表; 
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l1_tgt_period_t()
          2023-08-02 lwx1186472    202309版本增加字段'target_period',增加计算'年度'、'半年度'、'季度' 	 									   		  
*/ 		

								   
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l1_tgt_period_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t';
	v_tbl_name2 varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t';	
	v_version_code varchar(50);  -- 版本编码
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';
  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L1层级目标时点表：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  
  -- 创建 l1_info_his_tmp 临时表
    drop table if exists l1_info_his_tmp;
	create temporary table l1_info_his_tmp (
	version_code character varying(100),
	period_id numeric,
	target_period character varying(100),
	bg_code character varying(100),
	bg_name character varying(400),
	oversea_desc character varying(100),
	lv1_code character varying(100),
	lv1_name character varying(600),
	lv2_code character varying(100),
	lv2_name character varying(600),
	l1_name character varying(100),
	currency character varying(50),
	ship_qty numeric(38,10),
	spart_qty numeric(38,10),
	equip_rev_cons_before_amt numeric(38,10),
	equip_cost_cons_before_amt numeric(38,10),
	equip_rev_cons_after_amt numeric(38,10),
	equip_cost_cons_after_amt numeric(38,10),
	articulation_flag character varying(50),
	H1_H2 character varying(50),
	Q1_Q4 character varying(50)
)on commit preserve rows distribute by hash(period_id,lv2_code,l1_name)
;

  -- 创建 qty_amt_ytd_tmp 临时表
    drop table if exists qty_amt_ytd_tmp;
	create temporary table qty_amt_ytd_tmp (
	version_code character varying(100),
	period_id numeric,	
	bg_code character varying(100),
	bg_name character varying(400),
	oversea_desc character varying(100),
	lv1_code character varying(100),
	lv1_name character varying(600),
	lv2_code character varying(100),
	lv2_name character varying(600),
	l1_name character varying(100),
	currency character varying(50),
	ship_qty numeric(38,10),
	spart_qty numeric(38,10),
	equip_rev_cons_before_amt numeric(38,10),
	equip_cost_cons_before_amt numeric(38,10),
	equip_rev_cons_after_amt numeric(38,10),
	equip_cost_cons_after_amt numeric(38,10),
	articulation_flag character varying(50),
	target_period character varying(100)
)on commit preserve rows distribute by hash(period_id,lv2_code,l1_name)
;

  -- 创建 unit_cost_price_tmp 临时表
    drop table if exists unit_cost_price_tmp;
	create temporary table unit_cost_price_tmp (
	version_code character varying(100),
	period_id numeric,	
	bg_code character varying(100),
	bg_name character varying(400),
	oversea_desc character varying(100),
	lv1_code character varying(100),
	lv1_name character varying(600),
	lv2_code character varying(100),
	lv2_name character varying(600),
	l1_name character varying(100),
	currency character varying(50),
	unit_cost numeric(38,10),
	unit_price numeric(38,10),
	articulation_flag character varying(50),
	target_period character varying(100)
)on commit preserve rows distribute by hash(period_id,lv2_code,l1_name)
;

--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
   
  ---判断p_version_code在his_t表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t where version_code = p_version_code)
   then   
  
     ----计算ytd的相应指标
     ---删除重跑his_ytd表中已有传入参数的数据
     delete fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t where version_code = p_version_code;

--入l1_info_his_tmp表
insert into l1_info_his_tmp 
          (
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,
    		spart_qty,
    		equip_rev_cons_before_amt,
    		equip_cost_cons_before_amt,
    		equip_rev_cons_after_amt,
    		equip_cost_cons_after_amt,
    		articulation_flag,--勾稽方法标签
            H1_H2,	     
			Q1_Q4	
)
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,
    		spart_qty,
    		equip_rev_cons_before_amt,
    		equip_cost_cons_before_amt,
    		equip_rev_cons_after_amt,
    		equip_cost_cons_after_amt,
    		articulation_flag,--勾稽方法标签
            case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'06' then 'H1'
	             when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'12' then 'H2'
	         end as H1_H2,	      
	        case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'03' then 'Q1'
	             when period_id between substr(period_id,1,4)||'04' and substr(period_id,1,4)||'06' then 'Q2'
			     when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'09' then 'Q3'
			     when period_id between substr(period_id,1,4)||'10' and substr(period_id,1,4)||'12' then 'Q4'
	         end as Q1_Q4				
       from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
      where	version_code = p_version_code
;

--入qty_amt_ytd_tmp 
insert into qty_amt_ytd_tmp
           (
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,--发货量（本年累计）
    		spart_qty,--收入量（本年累计）
    		equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			target_period
)
	-- 计算金额、数量类ytd(直取)
	
--月度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
			                      order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			period_id||'YTD' as target_period
       from l1_info_his_tmp
union all
--年度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4) as target_period
       from l1_info_his_tmp
union all
--半年度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4)||H1_H2 as target_period
       from l1_info_his_tmp
union all
--季度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4)||Q1_Q4 as target_period
       from l1_info_his_tmp	
;

--入unit_cost_price_tmp,计算均本均价
insert into unit_cost_price_tmp 
          (
	         version_code,  -- 版本编码
	         period_id,--会计期
			 bg_code,--bg编码
			 bg_name,--bg名称
			 oversea_desc,--区域
			 lv1_code,--重量级团队lv1编码
			 lv1_name,--重量级团队lv1描述
			 lv2_code,--重量级团队lv2编码
			 lv2_name,--重量级团队lv2名称
			 l1_name,--l1名称
			 currency,--币种
			 unit_cost,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			 unit_price,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			 articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			 target_period
)
	 select t1.version_code,  -- 版本编码
	        t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_cost,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_rev_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_price,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period
	  from qty_amt_ytd_tmp t1
;

	  
  --ytd指标数据入目标表   
  insert into fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t 
             (
                version_code
               ,period_id
               ,bg_code
               ,bg_name
               ,oversea_desc
               ,lv1_code
               ,lv1_name
               ,lv2_code
               ,lv2_name
               ,l1_name
               ,currency
               ,ship_qty
               ,spart_qty
               ,equip_rev_cons_before_amt
               ,equip_cost_cons_before_amt
               ,equip_rev_cons_after_amt
               ,equip_cost_cons_after_amt
               ,mgp_ratio
               ,mca_adjust_ratio
               ,mgp_adjust_ratio
               ,carryover_ratio
               ,unit_cost
               ,unit_price
               ,articulation_flag
			   ,target_period
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			  )
			  
	-- 计算场景一的 mca调整率、制毛调整率_ytd（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）
--半年度、季度拆分
with SCENO1_H_Q_tmp as (
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   equip_rev_cons_before_amt,
		   equip_cost_cons_before_amt,
		   equip_rev_cons_after_amt,
		   equip_cost_cons_after_amt,
           case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'06' then 'H1'
	            when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'12' then 'H2'
	        end as H1_H2,	      
	       case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'03' then 'Q1'
	            when period_id between substr(period_id,1,4)||'04' and substr(period_id,1,4)||'06' then 'Q2'
			    when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'09' then 'Q3'
			    when period_id between substr(period_id,1,4)||'10' and substr(period_id,1,4)||'12' then 'Q4'
	        end as Q1_Q4		   
	  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t   
	 where version_code = p_version_code
	   and articulation_flag = 'SCENO1'
),	
	adjust_rate_sceno_ytd_tmp as (
	--月度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   period_id||'YTD' as target_period					  
	  from SCENO1_H_Q_tmp
union all	  
	--年度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4) as target_period					  
	  from SCENO1_H_Q_tmp	
union all	  
	--半年度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4)||H1_H2 as target_period					  
	  from SCENO1_H_Q_tmp	  
union all	  
	--季度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4)||Q1_Q4 as target_period					  
	  from SCENO1_H_Q_tmp	  	  
	),			   
	--计算制毛率、mca调整率、结转率、单位成本、单位价格（ytd）
  mgp_adjust_rate_ytd_tmp as (
	 select t1.version_code,  -- 版本编码
	        t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			t1.ship_qty,--发货量（本年累计）
			t1.spart_qty,--收入量（本年累计）
			t1.equip_rev_cons_before_amt,--设备收入本年累计（对价前）
			t1.equip_cost_cons_before_amt,--设备成本本年累计（对价前）
			t1.equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
			t1.equip_cost_cons_after_amt,--设备成本本年累计（对价后）
			(case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 0
				    when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				    when nvl(t1.equip_rev_cons_after_amt,0) <> 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0
					  then 1 - t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
				    else null
			 end) as mgp_ratio,--制毛率(本年累计) = 1 - l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1')
			      then (case when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t2.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t2.equip_rev_cons_after_amt / t2.equip_rev_cons_before_amt
				          end)
			      when t1.articulation_flag in('SCENO2')
			      then (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t1.equip_rev_cons_after_amt / t1.equip_rev_cons_before_amt
				          end)
				    else null
			 end) as mca_adjust_ratio,--mca调整率(本年累计) = 1 – 对价后收入/对价前收入
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_amt,0) <> 0 then t2.equip_cost_cons_before_amt  / t2.equip_rev_cons_before_amt
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt  / t1.equip_rev_cons_before_amt
					         end)
				     else null
			  end) as mgp_ratio_before,--l1对价前成本金额/l1对价前收入金额
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_amt,0) <> 0 then t2.equip_cost_cons_after_amt  / t2.equip_rev_cons_after_amt
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_amt,0) <> 0 then t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
						       end)
				   else null
				   end) as mgp_ratio_after,--l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 and t1.spart_qty = 0 then null
				    when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 then -999999
				    when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_qty / t1.ship_qty
				    else null
			 end) as carryover_ratio,--结转率(本年累计) = l1收入数量/l1发货数量
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period
	  from qty_amt_ytd_tmp t1
	  left join adjust_rate_sceno_ytd_tmp t2
	    on t1.version_code  = t2.version_code
	   and t1.period_id     = t2.period_id
	   and t1.bg_code       = t2.bg_code
	   and t1.oversea_desc  = t2.oversea_desc
	   and t1.lv1_code      = t2.lv1_code
	   and t1.currency      = t2.currency
	   and t1.target_period = t2.target_period
	)
----计算制毛调整率（本年累计）
     select t1.version_code,
            t1.period_id,
			t1.bg_code,
			t1.bg_name,
			t1.oversea_desc,
			t1.lv1_code,
			t1.lv1_name,
			t1.lv2_code,
			t1.lv2_name,
			t1.l1_name,
			t1.currency,
			t1.ship_qty,
			t1.spart_qty,
			t1.equip_rev_cons_before_amt,
			t1.equip_cost_cons_before_amt,
			t1.equip_rev_cons_after_amt,
			t1.equip_cost_cons_after_amt,
			t1.mgp_ratio,
			t1.mca_adjust_ratio,
			(case when t1.mgp_ratio_before <> -999999 and t1.mgp_ratio_after <> -999999 then t1.mgp_ratio_before - t1.mgp_ratio_after
				  when t1.mgp_ratio_before = -999999  and t1.mgp_ratio_after = -999999  then 0
				  when t1.mgp_ratio_before = -999999   or t1.mgp_ratio_after = -999999  then -999999
			      end) as mgp_adjust_ratio,--制毛调整率
			t1.carryover_ratio,
			t2.unit_cost,
			t2.unit_price,
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period,
			'' as remark,--备注
			'-1' as created_by,--创建人
			current_timestamp as creation_date,--创建时间
			'-1' as last_updated_by,--修改人
			current_timestamp as last_update_date,--修改时间
			'N' as del_flag --是否删除
       from mgp_adjust_rate_ytd_tmp t1
       join unit_cost_price_tmp t2
         on t1.version_code      = t2.version_code    
	    and t1.period_id         = t2.period_id
		and t1.bg_code           = t2.bg_code
		and t1.bg_name           = t2.bg_name
		and t1.oversea_desc      = t2.oversea_desc
		and t1.lv1_code          = t2.lv1_code
		and t1.lv1_name          = t2.lv1_name
		and t1.lv2_code          = t2.lv2_code
		and t1.lv2_name          = t2.lv2_name
		and t1.l1_name           = t2.l1_name
		and t1.currency          = t2.currency
		and t1.articulation_flag = t2.articulation_flag
		and t1.target_period     = t2.target_period		 
	;	

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '作业对象L1层级目标时点历史表'||v_tbl_name2||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t;
   
  --入目标表
    insert into fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t(
			 period_id
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l1_name
			,currency
			,ship_qty
			,spart_qty
			,equip_rev_cons_before_amt
			,equip_cost_cons_before_amt
			,equip_rev_cons_after_amt
			,equip_cost_cons_after_amt
			,mgp_ratio
			,mca_adjust_ratio
			,mgp_adjust_ratio
			,carryover_ratio
			,unit_cost
			,unit_price
			,articulation_flag
			,target_period
			,remark
			,created_by
			,creation_date
			,last_updated_by
			,last_update_date
			,del_flag
			)
		select  period_id
			   ,bg_code
			   ,bg_name
			   ,oversea_desc
			   ,lv1_code
			   ,lv1_name
			   ,lv2_code
			   ,lv2_name
			   ,l1_name
			   ,currency
			   ,ship_qty
			   ,spart_qty
			   ,equip_rev_cons_before_amt
			   ,equip_cost_cons_before_amt
			   ,equip_rev_cons_after_amt
			   ,equip_cost_cons_after_amt
			   ,mgp_ratio
			   ,mca_adjust_ratio
			   ,mgp_adjust_ratio
			   ,carryover_ratio
			   ,unit_cost
			   ,unit_price
			   ,articulation_flag
			   ,target_period
			   ,remark
			   ,created_by
			   ,creation_date
			   ,last_updated_by
			   ,last_update_date
			   ,del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t
		 where version_code = p_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量

  -- 写结束日志		
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '作业对象L1层级目标时点表'||v_tbl_name||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  else
		
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => 'his_t表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;

		
else 
  
	-- 取来源表的最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
      from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t 
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t)
     group by substr(version_code,1,6)
	;    
	  -- 判断来源表中是否有最大版本，如有，需要删除his_ytd目标表中最大版本数据
	  delete fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t where version_code = v_max_version_code;

--入l1_info_his_tmp表
insert into l1_info_his_tmp 
          (
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,
    		spart_qty,
    		equip_rev_cons_before_amt,
    		equip_cost_cons_before_amt,
    		equip_rev_cons_after_amt,
    		equip_cost_cons_after_amt,
    		articulation_flag,--勾稽方法标签
            H1_H2,	     
			Q1_Q4	
)
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,
    		spart_qty,
    		equip_rev_cons_before_amt,
    		equip_cost_cons_before_amt,
    		equip_rev_cons_after_amt,
    		equip_cost_cons_after_amt,
    		articulation_flag,--勾稽方法标签
            case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'06' then 'H1'
	             when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'12' then 'H2'
	         end as H1_H2,	      
	        case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'03' then 'Q1'
	             when period_id between substr(period_id,1,4)||'04' and substr(period_id,1,4)||'06' then 'Q2'
			     when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'09' then 'Q3'
			     when period_id between substr(period_id,1,4)||'10' and substr(period_id,1,4)||'12' then 'Q4'
	         end as Q1_Q4				
       from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
      where	version_code = v_max_version_code
;

--入qty_amt_ytd_tmp 
insert into qty_amt_ytd_tmp
           (
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		ship_qty,--发货量（本年累计）
    		spart_qty,--收入量（本年累计）
    		equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			target_period
)
	-- 计算金额、数量类ytd(直取)
	
--月度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
			                      order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			period_id||'YTD' as target_period
       from l1_info_his_tmp
union all
--年度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4) as target_period
       from l1_info_his_tmp
union all
--半年度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4)||H1_H2 as target_period
       from l1_info_his_tmp
union all
--季度YTD
     select distinct
	        version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as ship_qty,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as spart_qty,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
						          order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_rev_cons_after_amt,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name,oversea_desc
                                           ,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,currency,articulation_flag
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
    		articulation_flag,  --勾稽方法标签
			substr(period_id,1,4)||Q1_Q4 as target_period
       from l1_info_his_tmp	
;

--入unit_cost_price_tmp,计算均本均价
insert into unit_cost_price_tmp 
          (
	         version_code,  -- 版本编码
	         period_id,--会计期
			 bg_code,--bg编码
			 bg_name,--bg名称
			 oversea_desc,--区域
			 lv1_code,--重量级团队lv1编码
			 lv1_name,--重量级团队lv1描述
			 lv2_code,--重量级团队lv2编码
			 lv2_name,--重量级团队lv2名称
			 l1_name,--l1名称
			 currency,--币种
			 unit_cost,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			 unit_price,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			 articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			 target_period
)
	 select t1.version_code,  -- 版本编码
	        t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_cost,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_rev_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_price,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period
	  from qty_amt_ytd_tmp t1
;
	  
  --ytd指标数据入目标表   
  insert into fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t 
             (
                version_code
               ,period_id
               ,bg_code
               ,bg_name
               ,oversea_desc
               ,lv1_code
               ,lv1_name
               ,lv2_code
               ,lv2_name
               ,l1_name
               ,currency
               ,ship_qty
               ,spart_qty
               ,equip_rev_cons_before_amt
               ,equip_cost_cons_before_amt
               ,equip_rev_cons_after_amt
               ,equip_cost_cons_after_amt
               ,mgp_ratio
               ,mca_adjust_ratio
               ,mgp_adjust_ratio
               ,carryover_ratio
               ,unit_cost
               ,unit_price
               ,articulation_flag
			   ,target_period
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			  )

	-- 计算场景一的 mca调整率、制毛调整率_ytd（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）
--半年度、季度拆分
with SCENO1_H_Q_tmp as (
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   equip_rev_cons_before_amt,
		   equip_cost_cons_before_amt,
		   equip_rev_cons_after_amt,
		   equip_cost_cons_after_amt,
           case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'06' then 'H1'
	            when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'12' then 'H2'
	        end as H1_H2,	      
	       case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'03' then 'Q1'
	            when period_id between substr(period_id,1,4)||'04' and substr(period_id,1,4)||'06' then 'Q2'
			    when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'09' then 'Q3'
			    when period_id between substr(period_id,1,4)||'10' and substr(period_id,1,4)||'12' then 'Q4'
	        end as Q1_Q4		   
	  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t   
	 where version_code = v_max_version_code
	   and articulation_flag = 'SCENO1'
),	
	adjust_rate_sceno_ytd_tmp as (
	--月度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   period_id||'YTD' as target_period					  
	  from SCENO1_H_Q_tmp
union all	  
	--年度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4) as target_period					  
	  from SCENO1_H_Q_tmp	
union all	  
	--半年度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4)||H1_H2 as target_period					  
	  from SCENO1_H_Q_tmp	  
union all	  
	--季度YTD
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_rev_cons_before_amt,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_before_amt,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency 
								  order by period_id) as equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,bg_name     
                                           ,oversea_desc,lv1_code,lv1_name,currency
								  order by period_id) as equip_cost_cons_after_amt,--设备成本本年累计（对价后）
		   substr(period_id,1,4)||Q1_Q4 as target_period					  
	  from SCENO1_H_Q_tmp	 
	),			   
	--计算制毛率、mca调整率、结转率、单位成本、单位价格（ytd）
  mgp_adjust_rate_ytd_tmp as (
	select t1.version_code,  -- 版本编码
	       t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			t1.ship_qty,--发货量（本年累计）
			t1.spart_qty,--收入量（本年累计）
			t1.equip_rev_cons_before_amt,--设备收入本年累计（对价前）
			t1.equip_cost_cons_before_amt,--设备成本本年累计（对价前）
			t1.equip_rev_cons_after_amt,--	设备收入本年累计（对价后）
			t1.equip_cost_cons_after_amt,--设备成本本年累计（对价后）
			(case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 0
				    when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				    when nvl(t1.equip_rev_cons_after_amt,0) <> 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0
					  then 1 - t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
				    else null
			 end) as mgp_ratio,--制毛率(本年累计) = 1 - l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1')
			      then (case when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t2.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t2.equip_rev_cons_after_amt / t2.equip_rev_cons_before_amt
				          end)
			      when t1.articulation_flag in('SCENO2')
			      then (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t1.equip_rev_cons_after_amt / t1.equip_rev_cons_before_amt
				          end)
				    else null
			 end) as mca_adjust_ratio,--mca调整率(本年累计) = 1 – 对价后收入/对价前收入
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_amt,0) <> 0 then t2.equip_cost_cons_before_amt  / t2.equip_rev_cons_before_amt
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt  / t1.equip_rev_cons_before_amt
					         end)
				     else null
			  end) as mgp_ratio_before,--l1对价前成本金额/l1对价前收入金额
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_amt,0) <> 0 then t2.equip_cost_cons_after_amt  / t2.equip_rev_cons_after_amt
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_amt,0) <> 0 then t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
						       end)
				   else null
				   end) as mgp_ratio_after,--l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 and t1.spart_qty = 0 then null
				    when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 then -999999
				    when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_qty / t1.ship_qty
				    else null
			 end) as carryover_ratio,--结转率(本年累计) = l1收入数量/l1发货数量
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period
	  from qty_amt_ytd_tmp t1
	  left join adjust_rate_sceno_ytd_tmp t2
	    on t1.version_code  = t2.version_code
	   and t1.period_id     = t2.period_id
	   and t1.bg_code       = t2.bg_code
	   and t1.oversea_desc  = t2.oversea_desc
	   and t1.lv1_code      = t2.lv1_code
	   and t1.currency      = t2.currency
	   and t1.target_period = t2.target_period
	)
----计算制毛调整率（本年累计）
     select t1.version_code,
            t1.period_id,
			t1.bg_code,
			t1.bg_name,
			t1.oversea_desc,
			t1.lv1_code,
			t1.lv1_name,
			t1.lv2_code,
			t1.lv2_name,
			t1.l1_name,
			t1.currency,
			t1.ship_qty,
			t1.spart_qty,
			t1.equip_rev_cons_before_amt,
			t1.equip_cost_cons_before_amt,
			t1.equip_rev_cons_after_amt,
			t1.equip_cost_cons_after_amt,
			t1.mgp_ratio,
			t1.mca_adjust_ratio,
			(case when t1.mgp_ratio_before <> -999999 and t1.mgp_ratio_after <> -999999 then t1.mgp_ratio_before - t1.mgp_ratio_after
				  when t1.mgp_ratio_before = -999999  and t1.mgp_ratio_after = -999999  then 0
				  when t1.mgp_ratio_before = -999999   or t1.mgp_ratio_after = -999999  then -999999
			      end) as mgp_adjust_ratio,--制毛调整率
			t1.carryover_ratio,
			t2.unit_cost,
			t2.unit_price,
			t1.articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			t1.target_period,
			'' as remark,--备注
			'-1' as created_by,--创建人
			current_timestamp as creation_date,--创建时间
			'-1' as last_updated_by,--修改人
			current_timestamp as last_update_date,--修改时间
			'N' as del_flag --是否删除
       from mgp_adjust_rate_ytd_tmp t1
       join unit_cost_price_tmp t2
         on t1.version_code      = t2.version_code    
	    and t1.period_id         = t2.period_id
		and t1.bg_code           = t2.bg_code
		and t1.bg_name           = t2.bg_name
		and t1.oversea_desc      = t2.oversea_desc
		and t1.lv1_code          = t2.lv1_code
		and t1.lv1_name          = t2.lv1_name
		and t1.lv2_code          = t2.lv2_code
		and t1.lv2_name          = t2.lv2_name
		and t1.l1_name           = t2.l1_name
		and t1.currency          = t2.currency
		and t1.articulation_flag = t2.articulation_flag
		and t1.target_period     = t2.target_period		 
	;		

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '作业对象L1层级目标时点历史表'||v_tbl_name2||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t;
  
  --入目标表
    insert into fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t(
			 period_id
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l1_name
			,currency
			,ship_qty
			,spart_qty
			,equip_rev_cons_before_amt
			,equip_cost_cons_before_amt
			,equip_rev_cons_after_amt
			,equip_cost_cons_after_amt
			,mgp_ratio
			,mca_adjust_ratio
			,mgp_adjust_ratio
			,carryover_ratio
			,unit_cost
			,unit_price
			,articulation_flag
			,target_period
			,remark
			,created_by
			,creation_date
			,last_updated_by
			,last_update_date
			,del_flag
			)
		select  period_id
			   ,bg_code
			   ,bg_name
			   ,oversea_desc
			   ,lv1_code
			   ,lv1_name
			   ,lv2_code
			   ,lv2_name
			   ,l1_name
			   ,currency
			   ,ship_qty
			   ,spart_qty
			   ,equip_rev_cons_before_amt
			   ,equip_cost_cons_before_amt
			   ,equip_rev_cons_after_amt
			   ,equip_cost_cons_after_amt
			   ,mgp_ratio
			   ,mca_adjust_ratio
			   ,mgp_adjust_ratio
			   ,carryover_ratio
			   ,unit_cost
			   ,unit_price
			   ,articulation_flag
			   ,target_period
			   ,remark
			   ,created_by
			   ,creation_date
			   ,last_updated_by
			   ,last_update_date
			   ,del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t
		 where version_code = v_max_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量
	
   --写结束日志	
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '作业对象L1层级目标时点表'||v_tbl_name||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

		
  end if;
  

--处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_his_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l1_tgt_period_t;	
	
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

