-- ----------------------------
-- Function structure for f_dm_fop_spart_detail_l1_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_detail_l1_info_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_detail_l1_info_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*
创建时间：2022-10-12
创建人  ：qwx1110218
背景描述：SPART明细L1标签表: 对目标产业的数据根据重量级团队LV1、LV2、LV3信息匹配出Spart对应的L1名称
参数描述：参数一(p_version_code)：版本编码，参数格式：202207_V1
		      参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_detail_l1_info_t(202207);
修改记录：20230203 qwx1110218 版本编码是SUM层表已经存在的，如果传入版本参数，则根据版本参数取值，否则取最大版本数据
          20230601 qwx1110218 新增“BG英文名称”字段
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_detail_l1_info_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t';
	v_version_code varchar(50) default null;  -- 传入版本
	v_source_max_version_code varchar(50);  -- 来源表的最大版本
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月日_V1...VN
	v_dml_row_count  number default 0 ;

begin
	
	set enable_force_vector_engine to on;
	
	x_success_flag := '1';

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'SPART明细L1标签表'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 创建 ict_fcst_holistic_tmp 临时表
  drop table if exists ict_fcst_holistic_tmp;
	create temporary table ict_fcst_holistic_tmp(
	       lv1_code varchar(100), lv1_name varchar(500), industry_type varchar(100)
	)on commit preserve rows distribute by replication 
	;
  
  -- 创建 spart_all_tmp1 临时表
  drop table if exists spart_all_tmp1;
	create temporary table spart_all_tmp1(
	       version_code                   varchar(100)       -- 版本编码
	     , period_id                      numeric            -- 会计期
       , spart_code                     varchar(150)       -- 物料编码
       , spart_desc                     varchar(2000)      -- 物料描述
       , spart_qty                      numeric            -- Part物料数量
       , ship_qty                       numeric            -- Part发货数量
       , snop_quantity                  numeric            -- SNOP预测计划单元计划量
       , snop_plan_quantity             numeric            -- SNOP预算计划单元计划量
       , rmb_revenue                    numeric(38,10)     -- Spart收入金额_人民币
       , usd_revenue                    numeric(38,10)     -- Spart收入金额_美金
       , rmb_cost                       varchar(5000)      -- Bpart成本金额_人民币
       , usd_cost                       varchar(5000)      -- Bpart成本金额_美金
       , equip_rev_rmb_amt              numeric(38,10)     -- 设备收入人民币金额
       , equip_rev_usd_amt              numeric(38,10)     -- 设备收入美元金额
       , equip_cost_rmb_amt             numeric(38,10)     -- 设备成本人民币金额
       , equip_cost_usd_amt             numeric(38,10)     -- 设备成本美元金额
       , prod_key                       numeric            -- 产品KEY
       , prod_code                      varchar(50)        -- 产品编码
       , prod_cn_name                   varchar(600)       -- 产品名称
       , bg_code                        varchar(50)        -- BG编码
       , bg_name                        varchar(200)       -- BG中文名称
       , bg_en_name                     varchar(200)       -- BG英文名称
       , lv0_prod_rnd_team_code         varchar(50)        -- 重量级团队LV0编码
       , lv0_prod_rd_team_cn_name       varchar(600)       -- 重量级团队LV0中文描述
       , lv0_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV0英文描述
       , lv1_prod_rnd_team_code         varchar(50)        -- 重量级团队LV1编码
       , lv1_prod_rd_team_cn_name       varchar(600)       -- 重量级团队LV1中文描述
       , lv1_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV1英文描述
       , lv2_prod_rnd_team_code         varchar(50)        -- 重量级团队LV2编码
       , lv2_prod_rd_team_cn_name       varchar(600)       -- 重量级团队LV2中文描述
       , lv2_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV2英文描述
       , lv3_prod_rnd_team_code         varchar(50)        -- 重量级团队LV3编码
       , lv3_prod_rd_team_cn_name       varchar(600)       -- 重量级团队LV3中文描述
       , lv3_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV3英文描述
       , plan_com_lv1                   varchar(600)       -- 一级计委包
       , plan_com_lv2                   varchar(600)       -- 二级计委包
       , plan_com_lv3                   varchar(600)       -- 三级计委包
       , busi_lv4                       varchar(600)       -- 四级业务包
       , geo_pc_key                     numeric            -- 区域责任中心KEY
       , oversea_flag                   varchar(20)        -- 海外标志
       , phase_date                     varchar(60)        -- 期次分区字段
       , plan_unit_quantity             numeric            -- 计划单元计划量
       , unit                           varchar(600)       -- 单位
       , source_table                   varchar(100)       -- 来源表
	)on commit preserve rows distribute by hash(period_id, spart_code, prod_key)
	;
	
	
	-- 创建 source_max_version_tmp 临时表
	drop table if exists source_max_version_tmp;
	create temporary table source_max_version_tmp(source_max_version_code varchar(50)) on commit preserve rows distribute by replication;
	-- 取来源表中当年的最大版本
	insert into source_max_version_tmp(source_max_version_code)
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as source_max_version_code
    from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t)
   group by substr(version_code,1,6)
	union all
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as source_max_version_code
    from fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t)
   group by substr(version_code,1,6)
	union all
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as source_max_version_code
    from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t)
   group by substr(version_code,1,6)
	union all
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as source_max_version_code
    from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t where substr(period,3,4) = to_char(current_date,'yyyy')) -- 取系统当年期次的最大版本
   group by substr(version_code,1,6)
	union all
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as source_max_version_code
    from fin_dm_opt_fop.dm_fop_ict_pl_sum_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_ict_pl_sum_t)
   group by substr(version_code,1,6)
	;
	
	-- 取来源表中最大版本（正常情况下来源表的版本是一致的）
	select substr(source_max_version_code,1,6)||'_V'||max(substr(source_max_version_code,9)::numeric) as source_max_version_code into v_source_max_version_code
    from source_max_version_tmp 
   where substr(source_max_version_code,1,6) in(select max(substr(source_max_version_code,1,6)) from source_max_version_tmp)
   group by substr(source_max_version_code,1,6)
	;
  
  -- 创建 in_version_code_tmp 临时表
  drop table if exists in_version_code_tmp;
  create temporary table in_version_code_tmp(in_version_code varchar(50), source_table varchar(100)) on commit preserve rows distribute by replication;

  -- 产业临时表
  insert into ict_fcst_holistic_tmp(lv1_code, lv1_name, industry_type)
  select distinct lv1_code, lv1_name, industry_type
      from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t -- ICT业务预测全景图维表
     where del_flag = 'N'
       and upper(status) = 'SUBMIT'
  ;
  
  v_dml_row_count := sql%rowcount;  -- 收集数据量
  
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'ict_fcst_holistic_tmp 临时表数据量：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 如果传入版本无值，则取最大版本数据
  if(p_version_code is null or p_version_code = '') then 	
    delete fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t where version_code = v_source_max_version_code;  -- 删除目标表中已存在的来源表的最大版本
	  -- SPART明细：5张集成表数据的合并，通过关联产业临时表，只取需要的产业
    insert into spart_all_tmp1(
         version_code                    -- 版本编码
	     , period_id                       -- 会计期
       , spart_code                      -- 物料编码
       , spart_desc                      -- 物料描述
       , spart_qty                       -- Part物料数量
       , ship_qty                        -- Part发货数量
       , snop_quantity                   -- SNOP预测月计划量
       , snop_plan_quantity              -- SNOP预算月计划量
       , rmb_revenue                     -- Spart收入金额_人民币
       , usd_revenue                     -- Spart收入金额_美金
       , rmb_cost                        -- Bpart成本金额_人民币
       , usd_cost                        -- Bpart成本金额_美金
       , equip_rev_rmb_amt               -- 设备收入人民币金额
       , equip_rev_usd_amt               -- 设备收入美元金额
       , equip_cost_rmb_amt              -- 设备成本人民币金额
       , equip_cost_usd_amt              -- 设备成本美元金额
       , prod_key                        -- 产品KEY
       , prod_code                       -- 产品编码
       , prod_cn_name                    -- 产品名称
       , bg_code                         -- BG编码
       , bg_name                         -- BG名称
       , bg_en_name                      -- BG英文名
       , lv0_prod_rnd_team_code          -- 重量级团队LV0编码
       , lv0_prod_rd_team_cn_name        -- 重量级团队LV0中文描述
       , lv0_prod_rd_team_en_name        -- 重量级团队LV0英文描述
       , lv1_prod_rnd_team_code          -- 重量级团队LV1编码
       , lv1_prod_rd_team_cn_name        -- 重量级团队LV1中文描述
       , lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
       , lv2_prod_rnd_team_code          -- 重量级团队LV2编码
       , lv2_prod_rd_team_cn_name        -- 重量级团队LV2中文描述
       , lv2_prod_rd_team_en_name        -- 重量级团队LV2英文描述
       , lv3_prod_rnd_team_code          -- 重量级团队LV3编码
       , lv3_prod_rd_team_cn_name        -- 重量级团队LV3中文描述
       , lv3_prod_rd_team_en_name        -- 重量级团队LV3英文描述
       , plan_com_lv1                    -- 一级计委包
       , plan_com_lv2                    -- 二级计委包
       , plan_com_lv3                    -- 三级计委包
       , busi_lv4                        -- 四级业务包
       , geo_pc_key                      -- 区域责任中心KEY
       , oversea_flag                    -- 海外标志
       , phase_date                      -- 期次分区字段
       , plan_unit_quantity              -- 预算、预测计划单元计划量
       , unit                            -- 单位
       , source_table                    -- 来源表
       --, industry_type                   -- 产业类型（TGT 目标产业、OTHR 其它产业）
	  )
    select t1.version_code                      -- 版本编码
         , t1.period_id                          -- 会计期
         , t1.spart_code                              -- Part编码
         , t1.spart_desc                              -- Part描述
         , t1.spart_qty                               -- Part物料数量
         , null::numeric as ship_qty
         , null::numeric as snop_quantity
         , null::numeric as snop_plan_quantity
         , t1.rmb_revenue                             -- Spart收入金额_人民币
         , t1.usd_revenue                             -- Spart收入金额_美金
         , t1.rmb_cost                                -- Bpart成本金额_人民币
         , t1.usd_cost                                -- Bpart成本金额_美金
         , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
         , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
         , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
         , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
         , t1.prod_key                                -- 产品KEY
         , t1.prod_code                               -- 产品编码
         , t1.prod_cn_name                            -- 产品中文描述
         , t1.bg_code                                 -- BG编码
         , t1.bg_name                                 -- BG中文名
         , t1.bg_en_name                              -- BG英文名
         , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
         , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
         , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
         , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
         , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
         , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
         , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
         , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
         , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
         , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
         , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
         , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
         , '' as plan_com_lv1                         -- 一级计委包
         , '' as plan_com_lv2                         -- 二级计委包
         , '' as plan_com_lv3                         -- 三级计委包
         , '' as busi_lv4                             -- 四级业务包
         , t1.geo_pc_key                              -- 区域责任中心KEY
         , t1.oversea_flag                            -- 海外标志
         , '' as phase_date
         , null::numeric as plan_unit_quantity
         , '' as unit
         , 'fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t' as source_table
         --, t2.industry_type
      from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t t1 -- 收入时点S-Part量本价汇总表
      /*join ict_fcst_holistic_tmp t2  -- 产业临时表
        on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
     where t1.version_code = v_source_max_version_code  -- 取最大版本数据
       and t1.del_flag = 'N'
    union all
    select t1.version_code                    -- 版本编码
         , t1.period_id                    -- 会计期
         , t1.spart_code                   -- Part编码
         , t1.spart_desc                   -- Part描述
         , null::numeric as spart_qty       -- Part发货数量
         , t1.ship_qty
         , null::numeric as snop_quantity
         , null::numeric as snop_plan_quantity
         , null::numeric as rmb_revenue	           -- Spart收入金额_人民币
         , null::numeric as usd_revenue	           -- Spart收入金额_美金
         --, null::numeric as rmb_cost	               -- Bpart成本金额_人民币
         --, null::numeric as usd_cost	               -- Bpart成本金额_美金
         , '' as rmb_cost	               -- Bpart成本金额_人民币
         , '' as usd_cost	               -- Bpart成本金额_美金
         , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
         , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
         , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
         , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
         , t1.prod_key                     -- 产品KEY
         , t1.prod_code                    -- 产品编码
         , t1.prod_cn_name                 -- 产品名称
         , t1.bg_code                      -- BG编码
         , t1.bg_name                      -- BG中文名称
         , t1.bg_en_name                   -- BG英文名称
         , t1.lv0_prod_rnd_team_code       -- 重量级团队LV0编码
         , t1.lv0_prod_rd_team_cn_name     -- 重量级团队LV0中文描述
         , t1.lv0_prod_rd_team_en_name     -- 重量级团队LV0英文描述
         , t1.lv1_prod_rnd_team_code       -- 重量级团队LV1编码
         , t1.lv1_prod_rd_team_cn_name     -- 重量级团队LV1中文描述
         , t1.lv1_prod_rd_team_en_name     -- 重量级团队LV1英文描述
         , t1.lv2_prod_rnd_team_code       -- 重量级团队LV2编码
         , t1.lv2_prod_rd_team_cn_name     -- 重量级团队LV2中文描述
         , t1.lv2_prod_rd_team_en_name     -- 重量级团队LV2英文描述
         , t1.lv3_prod_rnd_team_code       -- 重量级团队LV3编码
         , t1.lv3_prod_rd_team_cn_name     -- 重量级团队LV3中文描述
         , t1.lv3_prod_rd_team_en_name     -- 重量级团队LV3英文描述
         , '' as plan_com_lv1                         -- 一级计委包
         , '' as plan_com_lv2                         -- 二级计委包
         , '' as plan_com_lv3                         -- 三级计委包
         , '' as busi_lv4                             -- 四级业务包
         , t1.geo_pc_key                   -- 区域责任中心KEY
         , t1.oversea_flag                 -- 海外标志
         , '' as phase_date
         , null::numeric as plan_unit_quantity
         , '' as unit
         , 'fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t' as source_table
         --, t2.industry_type
      from fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t t1  -- 供应中心发货时点汇总表
      /*join ict_fcst_holistic_tmp t2  -- 产业临时表
        on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
     where t1.version_code = v_source_max_version_code  -- 取最大版本数据  
       and t1.del_flag = 'N'
    union all
    select t1.version_code                    -- 版本编码
         , t1.month::int4 as period_id        -- 会计期
         , t1.item_code as spart_code
         , '' as spart_desc
         , null::numeric as spart_qty       -- Part物料数量
         , null::numeric as ship_qty
         --, t1.plan_unit_quantity as snop_quantity  -- 【20221116】业务确认用 plan_unit_quantity，因为无线的 quantity、plan_unit_quantity 数量有差异。
         , t1.quantity as snop_quantity    -- 20230423 update by qwx1110218
         , null::numeric as snop_plan_quantity
         , null::numeric as rmb_revenue	             -- Spart收入金额_人民币
         , null::numeric as usd_revenue	             -- Spart收入金额_美金
         --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
         --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
         , '' as rmb_cost	                 -- Bpart成本金额_人民币
         , '' as usd_cost	                 -- Bpart成本金额_美金
         , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
         , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
         , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
         , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
         , null::numeric as prod_key
         --, t1.coa_no as prod_code
         , t1.prod_code   -- 20230423 update by qwx1110218 5月版修改字段名
         , '' as prod_cn_name
         , t1.bg_code
         , t1.bg_cn_name as bg_name
         , t1.bg_en_name                   -- BG英文名称
         , t1.lst_lv0_prod_rnd_team_code   as lv0_prod_rnd_team_code	  -- 重量级团队LV0编码
         , t1.lst_lv0_prod_rd_team_cn_name as lv0_prod_rd_team_cn_name	-- 重量级团队LV0中文描述
         , t1.lst_lv0_prod_rd_team_en_name as lv0_prod_rd_team_en_name	-- 重量级团队LV0英文描述
         , t1.lst_lv1_prod_rnd_team_code   as lv1_prod_rnd_team_code	  -- 重量级团队LV1编码
         , t1.lst_lv1_prod_rd_team_cn_name as lv1_prod_rd_team_cn_name	-- 重量级团队LV1中文描述
         , t1.lst_lv1_prod_rd_team_en_name as lv1_prod_rd_team_en_name	-- 重量级团队LV1英文描述
         , t1.lst_lv2_prod_rnd_team_code   as lv2_prod_rnd_team_code	  -- 重量级团队LV2编码
         , t1.lst_lv2_prod_rd_team_cn_name as lv2_prod_rd_team_cn_name	-- 重量级团队LV2中文描述
         , t1.lst_lv2_prod_rd_team_en_name as lv2_prod_rd_team_en_name	-- 重量级团队LV2英文描述
         , t1.lst_lv3_prod_rnd_team_code   as lv3_prod_rnd_team_code	  -- 重量级团队LV3编码
         , t1.lst_lv3_prod_rd_team_cn_name as lv3_prod_rd_team_cn_name	-- 重量级团队LV3中文描述
         , t1.lst_lv3_prod_rd_team_en_name as lv3_prod_rd_team_en_name	-- 重量级团队LV3英文描述
         , t1.plan_com_lv1                         -- 一级计委包
         , t1.plan_com_lv2                         -- 二级计委包
         , t1.plan_com_lv3                         -- 三级计委包
         , t1.busi_lv4                             -- 四级业务包
         , null::numeric as geo_pc_key	         -- 区域责任中心KEY
         , (case when oversea_desc = '海外' then 'Y' 
                 when oversea_desc in ('国内', '中国区') then 'N' 
                 when oversea_desc = '全球' then 'G' -- S&OP预测的全球
                 else oversea_desc
            end) as oversea_flag	         -- 海外标志（Y 海外、N 国内、G 全球、null 空），说明：S&OP预测SUM表的“全球”与“国内海外”上游集成表不一样，所以它的全球标识处理为G
         , t1.phase_date
         , t1.plan_unit_quantity
         , t1.unit
         , 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t' as source_table
         --, t2.industry_type
      from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t t1  -- 产品线S&OP预测汇总表
      /*join ict_fcst_holistic_tmp t2  -- 产业临时表
        on t1.lst_lv1_prod_rnd_team_code = t2.lv1_code*/
     where t1.version_code = v_source_max_version_code  -- 取最大版本数据 
       and t1.del_flag = 'N'
       and t1.oversea_desc in('全球','国内','海外','中国区') 
    union all
    select t1.version_code                    -- 版本编码
         , t1.month::int4 as period_id     -- 会计期
         , t1.item as spart_code                  -- 物料编码
         , t1.description as spart_desc           -- 物料描述
         , null::numeric as spart_qty       -- Part物料数量
         , null::numeric as ship_qty
         , null::numeric as snop_quantity
         , t1.plan_quantity      as snop_plan_quantity        -- SNOP预算月计划量
         , null::numeric as rmb_revenue	             -- Spart收入金额_人民币
         , null::numeric as usd_revenue	             -- Spart收入金额_美金
         --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
         --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
         , '' as rmb_cost	                 -- Bpart成本金额_人民币
         , '' as usd_cost	                 -- Bpart成本金额_美金
         , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
         , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
         , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
         , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
         , null::numeric as prod_key
         , t1.coa_no as prod_code
         , '' as prod_cn_name
         , t1.bg_code                             -- BG编码
         , t1.bg_cn_name                          -- BG中文名
         , t1.bg_en_name                          -- BG英文名称
         , t1.lst_lv0_prod_rnd_team_code    as lv0_prod_rnd_team_code	       -- 重量级团队LV0编码
         , t1.lst_lv0_prod_rd_team_cn_name  as lv0_prod_rd_team_cn_name      -- 重量级团队LV0中文描述
         , t1.lst_lv0_prod_rd_team_en_name  as lv0_prod_rd_team_en_name      -- 重量级团队LV0英文描述
         , t1.lst_lv1_prod_rnd_team_code    as lv1_prod_rnd_team_code	       -- 重量级团队LV1编码
         , t1.lst_lv1_prod_rd_team_cn_name  as lv1_prod_rd_team_cn_name      -- 重量级团队LV1中文描述
         , t1.lst_lv1_prod_rd_team_en_name  as lv1_prod_rd_team_en_name      -- 重量级团队LV1英文描述
         , t1.lst_lv2_prod_rnd_team_code    as lv2_prod_rnd_team_code	       -- 重量级团队LV1编码
         , t1.lst_lv2_prod_rd_team_cn_name  as lv2_prod_rd_team_cn_name      -- 重量级团队LV2中文描述
         , t1.lst_lv2_prod_rd_team_en_name  as lv2_prod_rd_team_en_name      -- 重量级团队LV2中文描述
         , t1.lst_lv3_prod_rnd_team_code    as lv3_prod_rnd_team_code	       -- 重量级团队LV3编码
         , t1.lst_lv3_prod_rd_team_cn_name  as lv3_prod_rd_team_cn_name      -- 重量级团队LV3中文描述
         , t1.lst_lv3_prod_rd_team_en_name  as lv3_prod_rd_team_en_name      -- 重量级团队LV3英文描述
         , t1.plan_com_lv1                         -- 一级计委包
         , t1.plan_com_lv2                         -- 二级计委包
         , t1.plan_com_lv3                         -- 三级计委包
         , t1.busi_lv4                             -- 四级业务包
         , null::numeric as geo_pc_key	         -- 区域责任中心KEY
         , '' as oversea_flag	         -- 海外标志
         , period as phase_date
         , null::numeric as plan_unit_quantity
         , '' as unit
         , 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t' as source_table
         --, t2.industry_type
      from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t t1  -- 产品线年度S&OP预算汇总表
      /*join ict_fcst_holistic_tmp t2  -- 产业临时表
        on t1.lst_lv1_prod_rnd_team_code = t2.lv1_code*/
     where t1.version_code = v_source_max_version_code  -- 取最大版本数据 
       and t1.del_flag = 'N'
    union all
    select t1.version_code                    -- 版本编码
         , t1.period_id                       -- 会计期
         , '' as spart_code                   -- Part编码
         , '' as spart_desc                   -- Part描述
         , null::numeric as spart_qty       -- Part发货数量
         , null::numeric as ship_qty
         , null::numeric as snop_quantity
         , null::numeric as snop_plan_quantity
         , null::numeric as rmb_revenue
         , null::numeric as usd_revenue
         --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
         --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
         , '' as rmb_cost	                 -- Bpart成本金额_人民币
         , '' as usd_cost	                 -- Bpart成本金额_美金
         , (case when t1.report_item_l1_code in ('GRP_PL_91000','PS_PL_91000') and t1.report_item_l2_code in ('GRP_PL_91050','PS_PL_91050') then t1.rmb_fact_ex_rate_ptd_amt end) as equip_rev_rmb_amt   -- 设备收入人民币金额   GRP_PL_91000	净销售收入;GRP_PL_91050	设备收入
         , (case when t1.report_item_l1_code in ('GRP_PL_91000','PS_PL_91000') and t1.report_item_l2_code in ('GRP_PL_91050','PS_PL_91050') then t1.usd_fact_ex_rate_ptd_amt end) as equip_rev_usd_amt   -- 设备收入美元金额     GRP_PL_91000	净销售收入;GRP_PL_91050	设备收入
         , (case when t1.report_item_l1_code in ('GRP_PL_91200','PS_PL_91200') and t1.report_item_l2_code in ('GRP_PL_91250','PS_PL_91250') then t1.rmb_fact_ex_rate_ptd_amt end) as equip_cost_rmb_amt  -- 设备成本人民币金额   GRP_PL_91200	销售成本;GRP_PL_91250	设备成本
         , (case when t1.report_item_l1_code in ('GRP_PL_91200','PS_PL_91200') and t1.report_item_l2_code in ('GRP_PL_91250','PS_PL_91250') then t1.usd_fact_ex_rate_ptd_amt end) as equip_cost_usd_amt  -- 设备成本美元金额     GRP_PL_91200	销售成本;GRP_PL_91250	设备成本
         , t1.prod_key                        -- 产品KEY
         , t1.prod_code                       -- 产品编码
         , t1.prod_cn_name                    -- 产品名称
         , t1.bg_code                         -- BG编码
         , t1.bg_name                         -- BG中文描述
         , t1.bg_en_name                      -- BG英文名称
         , t1.lv0_prod_rnd_team_code          -- 重量级团队LV0编码
         , t1.lv0_prod_rd_team_cn_name        -- 重量级团队LV0中文描述
         , t1.lv0_prod_rd_team_en_name        -- 重量级团队LV0英文描述
         , t1.lv1_prod_rnd_team_code          -- 重量级团队LV1编码
         , t1.lv1_prod_rd_team_cn_name        -- 重量级团队LV1中文描述
         , t1.lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
         , t1.lv2_prod_rnd_team_code          -- 重量级团队LV2编码
         , t1.lv2_prod_rd_team_cn_name        -- 重量级团队LV2中文描述
         , t1.lv2_prod_rd_team_en_name        -- 重量级团队LV2中文描述
         , t1.lv3_prod_rnd_team_code          -- 重量级团队LV3编码
         , t1.lv3_prod_rd_team_cn_name        -- 重量级团队LV3中文描述
         , t1.lv3_prod_rd_team_en_name        -- 重量级团队LV3英文描述
         , '' as plan_com_lv1                         -- 一级计委包
         , '' as plan_com_lv2                         -- 二级计委包
         , '' as plan_com_lv3                         -- 三级计委包
         , '' as busi_lv4                             -- 四级业务包
         , t1.geo_pc_key                      -- 区域责任中心KEY
         , t1.oversea_flag                    -- 海外标志
         , '' as phase_date
         , null::numeric as plan_unit_quantity
         , '' as unit
         , 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t' as source_table
         --, t2.industry_type
      from fin_dm_opt_fop.dm_fop_ict_pl_sum_t t1  -- ICT损益汇总表
      /*join ict_fcst_holistic_tmp t2  -- 产业临时表
        on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
     where t1.version_code = v_source_max_version_code  -- 取最大版本数据
       and t1.del_flag = 'N'
    ;
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量
    
    -- 开始记录日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '最大版本：'||v_source_max_version_code||'，spart_all_tmp1 临时表数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    
  else
    -- 根据传入版本取来源表对应版本的数据
    insert into in_version_code_tmp(in_version_code, source_table)
    select distinct version_code, 'dm_fop_prod_prod_unit_sum_kms_t' as source_table from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t where version_code = p_version_code union all
    select distinct version_code, 'dm_fop_cnbg_spart_ship_sum_t' as source_table from fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t where version_code = p_version_code union all
    select distinct version_code, 'dm_fop_snop_forecasts_sum_t' as source_table from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t where version_code = p_version_code union all
    select distinct version_code, 'dm_fop_snop_year_budget_sum_t' as source_table from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t where version_code = p_version_code union all
    select distinct version_code, 'dm_fop_ict_pl_sum_t' as source_table from fin_dm_opt_fop.dm_fop_ict_pl_sum_t where version_code = p_version_code
    ;
    
    if exists(select distinct in_version_code from in_version_code_tmp) then
      select distinct in_version_code into v_version_code from in_version_code_tmp;
      -- 如果来源表中有传入版本数据，则删除目标表中对应的版本数据
      delete fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t where version_code = v_version_code;
      
      -- 取传入版本编码的数据
      -- SPART明细：5张集成表数据的合并，通过关联产业临时表，只取需要的产业
      insert into spart_all_tmp1(
           version_code                    -- 版本编码
	       , period_id                       -- 会计期
         , spart_code                      -- 物料编码
         , spart_desc                      -- 物料描述
         , spart_qty                       -- Part物料数量
         , ship_qty                        -- Part发货数量
         , snop_quantity                   -- SNOP预测计划单元计划量
         , snop_plan_quantity              -- SNOP预算计划单元计划量
         , rmb_revenue                     -- Spart收入金额_人民币
         , usd_revenue                     -- Spart收入金额_美金
         , rmb_cost                        -- Bpart成本金额_人民币
         , usd_cost                        -- Bpart成本金额_美金
         , equip_rev_rmb_amt               -- 设备收入人民币金额
         , equip_rev_usd_amt               -- 设备收入美元金额
         , equip_cost_rmb_amt              -- 设备成本人民币金额
         , equip_cost_usd_amt              -- 设备成本美元金额
         , prod_key                        -- 产品KEY
         , prod_code                       -- 产品编码
         , prod_cn_name                    -- 产品名称
         , bg_code                         -- BG编码
         , bg_name                         -- BG中文名称
         , bg_en_name                      -- BG英文名称
         , lv0_prod_rnd_team_code          -- 重量级团队LV0编码
         , lv0_prod_rd_team_cn_name        -- 重量级团队LV0中文描述
         , lv0_prod_rd_team_en_name        -- 重量级团队LV0英文描述
         , lv1_prod_rnd_team_code          -- 重量级团队LV1编码
         , lv1_prod_rd_team_cn_name        -- 重量级团队LV1中文描述
         , lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
         , lv2_prod_rnd_team_code          -- 重量级团队LV2编码
         , lv2_prod_rd_team_cn_name        -- 重量级团队LV2中文描述
         , lv2_prod_rd_team_en_name        -- 重量级团队LV2英文描述
         , lv3_prod_rnd_team_code          -- 重量级团队LV3编码
         , lv3_prod_rd_team_cn_name        -- 重量级团队LV3中文描述
         , lv3_prod_rd_team_en_name        -- 重量级团队LV3英文描述
         , plan_com_lv1                    -- 一级计委包
         , plan_com_lv2                    -- 二级计委包
         , plan_com_lv3                    -- 三级计委包
         , busi_lv4                        -- 四级业务包
         , geo_pc_key                      -- 区域责任中心KEY
         , oversea_flag                    -- 海外标志
         , phase_date                      -- 期次分区字段
         , plan_unit_quantity              -- 计划单元计划量
         , unit                            -- 单位
         , source_table                    -- 来源表
         --, industry_type                   -- 产业类型（TGT 目标产业、OTHR 其它产业）
	    )
      select t1.version_code                    -- 版本编码
           , t1.period_id                          -- 会计期
           , t1.spart_code                              -- Part编码
           , t1.spart_desc                              -- Part描述
           , t1.spart_qty                               -- Part物料数量
           , null::numeric as ship_qty
           , null::numeric as snop_quantity
           , null::numeric as snop_plan_quantity
           , t1.rmb_revenue                             -- Spart收入金额_人民币
           , t1.usd_revenue                             -- Spart收入金额_美金
           , t1.rmb_cost                                -- Bpart成本金额_人民币
           , t1.usd_cost                                -- Bpart成本金额_美金
           , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
           , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
           , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
           , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
           , t1.prod_key                                -- 产品KEY
           , t1.prod_code                               -- 产品编码
           , t1.prod_cn_name                            -- 产品中文描述
           , t1.bg_code                                 -- BG编码
           , t1.bg_name                                 -- BG中文名称
           , t1.bg_en_name                              -- BG英文名称
           , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
           , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
           , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
           , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
           , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
           , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
           , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
           , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
           , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
           , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
           , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
           , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
           , '' as plan_com_lv1                         -- 一级计委包
           , '' as plan_com_lv2                         -- 二级计委包
           , '' as plan_com_lv3                         -- 三级计委包
           , '' as busi_lv4                             -- 四级业务包
           , t1.geo_pc_key                              -- 区域责任中心KEY
           , t1.oversea_flag                            -- 海外标志
           , '' as phase_date
           , null::numeric as plan_unit_quantity
           , '' as unit
           , 'fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t' as source_table
           --, t2.industry_type
        from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_kms_t t1 -- 收入时点S-Part量本价汇总表
        /*join ict_fcst_holistic_tmp t2  -- 产业临时表
          on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
       where t1.version_code = v_version_code
         and t1.del_flag = 'N'
      union all
      select t1.version_code                    -- 版本编码
           , t1.period_id                    -- 会计期
           , t1.spart_code                   -- Part编码
           , t1.spart_desc                   -- Part描述
           , null::numeric as spart_qty       -- Part发货数量
           , t1.ship_qty
           , null::numeric as snop_quantity
           , null::numeric as snop_plan_quantity
           , null::numeric as rmb_revenue	           -- Spart收入金额_人民币
           , null::numeric as usd_revenue	           -- Spart收入金额_美金
           --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
         --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
         , '' as rmb_cost	                 -- Bpart成本金额_人民币
         , '' as usd_cost	                 -- Bpart成本金额_美金
           , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
           , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
           , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
           , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
           , t1.prod_key                     -- 产品KEY
           , t1.prod_code                    -- 产品编码
           , t1.prod_cn_name                 -- 产品名称
           , t1.bg_code                      -- BG编码
           , t1.bg_name                      -- BG中文名称
           , t1.bg_en_name                   -- BG英文名称
           , t1.lv0_prod_rnd_team_code       -- 重量级团队LV0编码
           , t1.lv0_prod_rd_team_cn_name     -- 重量级团队LV0中文描述
           , t1.lv0_prod_rd_team_en_name     -- 重量级团队LV0英文描述
           , t1.lv1_prod_rnd_team_code       -- 重量级团队LV1编码
           , t1.lv1_prod_rd_team_cn_name     -- 重量级团队LV1中文描述
           , t1.lv1_prod_rd_team_en_name     -- 重量级团队LV1英文描述
           , t1.lv2_prod_rnd_team_code       -- 重量级团队LV2编码
           , t1.lv2_prod_rd_team_cn_name     -- 重量级团队LV2中文描述
           , t1.lv2_prod_rd_team_en_name     -- 重量级团队LV2英文描述
           , t1.lv3_prod_rnd_team_code       -- 重量级团队LV3编码
           , t1.lv3_prod_rd_team_cn_name     -- 重量级团队LV3中文描述
           , t1.lv3_prod_rd_team_en_name     -- 重量级团队LV3英文描述
           , '' as plan_com_lv1                         -- 一级计委包
           , '' as plan_com_lv2                         -- 二级计委包
           , '' as plan_com_lv3                         -- 三级计委包
           , '' as busi_lv4                             -- 四级业务包
           , t1.geo_pc_key                   -- 区域责任中心KEY
           , t1.oversea_flag                 -- 海外标志
           , '' as phase_date
           , null::numeric as plan_unit_quantity
           , '' as unit
           , 'fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t' as source_table
           --, t2.industry_type
        from fin_dm_opt_fop.dm_fop_cnbg_spart_ship_sum_t t1  -- 供应中心发货时点汇总表
        /*join ict_fcst_holistic_tmp t2  -- 产业临时表
          on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
       where t1.version_code = v_version_code
         and t1.del_flag = 'N'
      union all
      select t1.version_code                    -- 版本编码
           , t1.month::int4 as period_id        -- 会计期
           , t1.item_code as spart_code
           , '' as spart_desc
           , null::numeric as spart_qty       -- Part物料数量
           , null::numeric as ship_qty
           --, t1.plan_unit_quantity as snop_quantity  -- 【20221116】业务确认用 plan_unit_quantity，因为无线的 quantity、plan_unit_quantity 数量有差异。
           , t1.quantity as snop_quantity    -- 20230423 update by qwx1110218
           , null::numeric as snop_plan_quantity
           , null::numeric as rmb_revenue	             -- Spart收入金额_人民币
           , null::numeric as usd_revenue	             -- Spart收入金额_美金
           --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
           --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
           , '' as rmb_cost	                 -- Bpart成本金额_人民币
           , '' as usd_cost	                 -- Bpart成本金额_美金
           , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
           , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
           , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
           , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
           , null::numeric as prod_key
           --, t1.coa_no as prod_code
           , t1.prod_code  -- 20230423 update by qwx1110218	 5月版修改字段名
           , '' as prod_cn_name
           , t1.bg_code
           , t1.bg_cn_name as bg_name
           , t1.bg_en_name
           , t1.lst_lv0_prod_rnd_team_code   as lv0_prod_rnd_team_code	  -- 重量级团队LV0编码
           , t1.lst_lv0_prod_rd_team_cn_name as lv0_prod_rd_team_cn_name	-- 重量级团队LV0中文描述
           , t1.lst_lv0_prod_rd_team_en_name as lv0_prod_rd_team_en_name	-- 重量级团队LV0英文描述
           , t1.lst_lv1_prod_rnd_team_code   as lv1_prod_rnd_team_code	  -- 重量级团队LV1编码
           , t1.lst_lv1_prod_rd_team_cn_name as lv1_prod_rd_team_cn_name	-- 重量级团队LV1中文描述
           , t1.lst_lv1_prod_rd_team_en_name as lv1_prod_rd_team_en_name	-- 重量级团队LV1英文描述
           , t1.lst_lv2_prod_rnd_team_code   as lv2_prod_rnd_team_code	  -- 重量级团队LV2编码
           , t1.lst_lv2_prod_rd_team_cn_name as lv2_prod_rd_team_cn_name	-- 重量级团队LV2中文描述
           , t1.lst_lv2_prod_rd_team_en_name as lv2_prod_rd_team_en_name	-- 重量级团队LV2英文描述
           , t1.lst_lv3_prod_rnd_team_code   as lv3_prod_rnd_team_code	  -- 重量级团队LV3编码
           , t1.lst_lv3_prod_rd_team_cn_name as lv3_prod_rd_team_cn_name	-- 重量级团队LV3中文描述
           , t1.lst_lv3_prod_rd_team_en_name as lv3_prod_rd_team_en_name	-- 重量级团队LV3英文描述
           , t1.plan_com_lv1                         -- 一级计委包
           , t1.plan_com_lv2                         -- 二级计委包
           , t1.plan_com_lv3                         -- 三级计委包
           , t1.busi_lv4                             -- 四级业务包
           , null::numeric as geo_pc_key	         -- 区域责任中心KEY
           , (case when oversea_desc = '海外' then 'Y' 
                   when oversea_desc in ('国内','中国区') then 'N' 
                   when oversea_desc = '全球' then 'G' -- S&OP预测的全球
                   else oversea_desc
              end) as oversea_flag	         -- 海外标志（Y 海外、N 国内、G 全球、null 空），说明：S&OP预测SUM表的“全球”与“国内海外”上游集成表不一样，所以它的全球标识处理为G
           , t1.phase_date
           , t1.plan_unit_quantity
           , t1.unit
           , 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t' as source_table
           --, t2.industry_type
        from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t t1  -- 产品线S&OP预测汇总表
        /*join ict_fcst_holistic_tmp t2  -- 产业临时表
          on t1.lst_lv1_prod_rnd_team_code = t2.lv1_code*/
       where t1.version_code = v_version_code
         and t1.del_flag = 'N'
      union all
      select t1.version_code                    -- 版本编码
           , t1.month::int4 as period_id     -- 会计期
           , t1.item as spart_code                  -- 物料编码
           , t1.description as spart_desc           -- 物料描述
           , null::numeric as spart_qty       -- Part物料数量
           , null::numeric as ship_qty
           , null::numeric as snop_quantity
           , t1.plan_quantity      as snop_plan_quantity        -- SNOP预算月计划量
           , null::numeric as rmb_revenue	             -- Spart收入金额_人民币
           , null::numeric as usd_revenue	             -- Spart收入金额_美金
           --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
           --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
           , '' as rmb_cost	                 -- Bpart成本金额_人民币
           , '' as usd_cost	                 -- Bpart成本金额_美金
           , null::numeric as equip_rev_rmb_amt        -- 设备收入人民币金额
           , null::numeric as equip_rev_usd_amt        -- 设备收入美元金额
           , null::numeric as equip_cost_rmb_amt          -- 设备成本人民币金额
           , null::numeric as equip_cost_usd_amt          -- 设备成本美元金额
           , null::numeric as prod_key
           , t1.coa_no as prod_code
           , '' as prod_cn_name
           , t1.bg_code                             -- BG编码
           , t1.bg_cn_name                          -- BG中文名
           , t1.bg_en_name                          -- BG英文文名
           , t1.lst_lv0_prod_rnd_team_code    as lv0_prod_rnd_team_code	       -- 重量级团队LV0编码
           , t1.lst_lv0_prod_rd_team_cn_name  as lv0_prod_rd_team_cn_name      -- 重量级团队LV0中文描述
           , t1.lst_lv0_prod_rd_team_en_name  as lv0_prod_rd_team_en_name      -- 重量级团队LV0英文描述
           , t1.lst_lv1_prod_rnd_team_code    as lv1_prod_rnd_team_code	       -- 重量级团队LV1编码
           , t1.lst_lv1_prod_rd_team_cn_name  as lv1_prod_rd_team_cn_name      -- 重量级团队LV1中文描述
           , t1.lst_lv1_prod_rd_team_en_name  as lv1_prod_rd_team_en_name      -- 重量级团队LV1英文描述
           , t1.lst_lv2_prod_rnd_team_code    as lv2_prod_rnd_team_code	       -- 重量级团队LV1编码
           , t1.lst_lv2_prod_rd_team_cn_name  as lv2_prod_rd_team_cn_name      -- 重量级团队LV2中文描述
           , t1.lst_lv2_prod_rd_team_en_name  as lv2_prod_rd_team_en_name      -- 重量级团队LV2中文描述
           , t1.lst_lv3_prod_rnd_team_code    as lv3_prod_rnd_team_code	       -- 重量级团队LV3编码
           , t1.lst_lv3_prod_rd_team_cn_name  as lv3_prod_rd_team_cn_name      -- 重量级团队LV3中文描述
           , t1.lst_lv3_prod_rd_team_en_name  as lv3_prod_rd_team_en_name      -- 重量级团队LV3英文描述
           , t1.plan_com_lv1                         -- 一级计委包
           , t1.plan_com_lv2                         -- 二级计委包
           , t1.plan_com_lv3                         -- 三级计委包
           , t1.busi_lv4                             -- 四级业务包
           , null::numeric as geo_pc_key	         -- 区域责任中心KEY
           , '' as oversea_flag	         -- 海外标志
           , period as phase_date
           , null::numeric as plan_unit_quantity
           , '' as unit
           , 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t' as source_table
           --, t2.industry_type
        from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t t1  -- 产品线年度S&OP预算汇总表
        /*join ict_fcst_holistic_tmp t2  -- 产业临时表
          on t1.lst_lv1_prod_rnd_team_code = t2.lv1_code*/
       where t1.version_code = v_version_code
         and t1.del_flag = 'N'
      union all
      select t1.version_code                    -- 版本编码
           , t1.period_id                       -- 会计期
           , '' as spart_code                   -- Part编码
           , '' as spart_desc                   -- Part描述
           , null::numeric as spart_qty       -- Part发货数量
           , null::numeric as ship_qty
           , null::numeric as snop_quantity
           , null::numeric as snop_plan_quantity
           , null::numeric as rmb_revenue
           , null::numeric as usd_revenue
           --, null::numeric as rmb_cost	                 -- Bpart成本金额_人民币
           --, null::numeric as usd_cost	                 -- Bpart成本金额_美金
           , '' as rmb_cost	                 -- Bpart成本金额_人民币
           , '' as usd_cost	                 -- Bpart成本金额_美金
           , (case when t1.report_item_l1_code in ('GRP_PL_91000','PS_PL_91000')  and t1.report_item_l2_code in ('GRP_PL_91050','PS_PL_91050') then t1.rmb_fact_ex_rate_ptd_amt end) as equip_rev_rmb_amt   -- 设备收入人民币金额   GRP_PL_91000	净销售收入;GRP_PL_91050	设备收入
           , (case when t1.report_item_l1_code in ('GRP_PL_91000','PS_PL_91000')  and t1.report_item_l2_code in ('GRP_PL_91050','PS_PL_91050') then t1.usd_fact_ex_rate_ptd_amt end) as equip_rev_usd_amt   -- 设备收入美元金额     GRP_PL_91000	净销售收入;GRP_PL_91050	设备收入
           , (case when t1.report_item_l1_code in ('GRP_PL_91200','PS_PL_91200') and t1.report_item_l2_code in ('GRP_PL_91250','PS_PL_91250') then t1.rmb_fact_ex_rate_ptd_amt end) as equip_cost_rmb_amt  -- 设备成本人民币金额   GRP_PL_91200	销售成本;GRP_PL_91250	设备成本
           , (case when t1.report_item_l1_code in ('GRP_PL_91200','PS_PL_91200') and t1.report_item_l2_code in ('GRP_PL_91250','PS_PL_91250') then t1.usd_fact_ex_rate_ptd_amt end) as equip_cost_usd_amt  -- 设备成本美元金额     GRP_PL_91200	销售成本;GRP_PL_91250	设备成本
           , t1.prod_key                        -- 产品KEY
           , t1.prod_code                       -- 产品编码
           , t1.prod_cn_name                    -- 产品名称
           , t1.bg_code                         -- BG编码
           , t1.bg_name                         -- BG中文描述
           , t1.bg_en_name                      -- BG英文名称
           , t1.lv0_prod_rnd_team_code          -- 重量级团队LV0编码
           , t1.lv0_prod_rd_team_cn_name        -- 重量级团队LV0中文描述
           , t1.lv0_prod_rd_team_en_name        -- 重量级团队LV0英文描述
           , t1.lv1_prod_rnd_team_code          -- 重量级团队LV1编码
           , t1.lv1_prod_rd_team_cn_name        -- 重量级团队LV1中文描述
           , t1.lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
           , t1.lv2_prod_rnd_team_code          -- 重量级团队LV2编码
           , t1.lv2_prod_rd_team_cn_name        -- 重量级团队LV2中文描述
           , t1.lv2_prod_rd_team_en_name        -- 重量级团队LV2中文描述
           , t1.lv3_prod_rnd_team_code          -- 重量级团队LV3编码
           , t1.lv3_prod_rd_team_cn_name        -- 重量级团队LV3中文描述
           , t1.lv3_prod_rd_team_en_name        -- 重量级团队LV3英文描述
           , '' as plan_com_lv1                         -- 一级计委包
           , '' as plan_com_lv2                         -- 二级计委包
           , '' as plan_com_lv3                         -- 三级计委包
           , '' as busi_lv4                             -- 四级业务包
           , t1.geo_pc_key                      -- 区域责任中心KEY
           , t1.oversea_flag                    -- 海外标志
           , '' as phase_date
           , null::numeric as plan_unit_quantity
           , '' as unit
           , 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t' as source_table
           --, t2.industry_type
        from fin_dm_opt_fop.dm_fop_ict_pl_sum_t t1  -- ICT损益汇总表
        /*join ict_fcst_holistic_tmp t2  -- 产业临时表
          on t1.lv1_prod_rnd_team_code = t2.lv1_code*/
       where t1.version_code = v_version_code
         and t1.del_flag = 'N'
      ;
      
      v_dml_row_count := sql%rowcount;  -- 收集数据量
      
      -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => '传入版本：'||v_version_code||'，spart_all_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
    else
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '传入版本：'||v_version_code||'，来源表中没有此版本，请重新传入版本！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	    x_success_flag := '2001';
	    return; 
    end if;
  end if;
  
  -- 数据入到临时表
  with spart_all_tmp as(
	select t1.version_code                        -- 版本编码
	     , t1.period_id                           -- 会计期
       , t1.spart_code                          -- 物料编码
       , t1.spart_desc                          -- 物料描述
       , t1.spart_qty                           -- Part物料数量
       , t1.ship_qty                            -- Part发货数量
       , t1.snop_quantity                       -- SNOP预测计划单元计划量
       , t1.snop_plan_quantity                  -- SNOP预算计划单元计划量
       , t1.rmb_revenue                         -- Spart收入金额_人民币
       , t1.usd_revenue                         -- Spart收入金额_美金
       , t1.rmb_cost                            -- Bpart成本金额_人民币
       , t1.usd_cost                            -- Bpart成本金额_美金
       , t1.equip_rev_rmb_amt                   -- 设备收入人民币金额
       , t1.equip_rev_usd_amt                   -- 设备收入美元金额
       , t1.equip_cost_rmb_amt                  -- 设备成本人民币金额
       , t1.equip_cost_usd_amt                  -- 设备成本美元金额
       , t1.prod_key                            -- 产品KEY
       , t1.prod_code                           -- 产品编码
       , t1.prod_cn_name                        -- 产品名称
       , t1.bg_code                             -- BG编码
       , t1.bg_name                             -- BG中文名称
       , t1.bg_en_name                          -- BG英文名称
       , t1.lv0_prod_rnd_team_code              -- 重量级团队LV0编码
       , t1.lv0_prod_rd_team_cn_name            -- 重量级团队LV0中文描述
       , t1.lv0_prod_rd_team_en_name            -- 重量级团队LV0英文描述
       , t1.lv1_prod_rnd_team_code              -- 重量级团队LV1编码
       , t1.lv1_prod_rd_team_cn_name            -- 重量级团队LV1中文描述
       , t1.lv1_prod_rd_team_en_name            -- 重量级团队LV1英文描述
       , t1.lv2_prod_rnd_team_code              -- 重量级团队LV2编码
       , t1.lv2_prod_rd_team_cn_name            -- 重量级团队LV2中文描述
       , t1.lv2_prod_rd_team_en_name            -- 重量级团队LV2英文描述
       , t1.lv3_prod_rnd_team_code              -- 重量级团队LV3编码
       , t1.lv3_prod_rd_team_cn_name            -- 重量级团队LV3中文描述
       , t1.lv3_prod_rd_team_en_name            -- 重量级团队LV3英文描述
       , t1.plan_com_lv1                        -- 一级计委包
       , t1.plan_com_lv2                        -- 二级计委包
       , t1.plan_com_lv3                        -- 三级计委包
       , t1.busi_lv4                            -- 四级业务包
       , t1.geo_pc_key                          -- 区域责任中心KEY
       , t1.oversea_flag                        -- 海外标志
       , t1.phase_date                          -- 期次分区字段
       , t1.plan_unit_quantity                  -- 计划单元计划量
       , t1.unit                                -- 单位
       , t1.source_table                        -- 来源表
       , t2.industry_type
	  from spart_all_tmp1 t1
	  join ict_fcst_holistic_tmp t2  -- 产业临时表
      on t1.lv1_prod_rnd_team_code = t2.lv1_code
  ),
  -- 通过关联ICT业务预测全景图维表取L1名称，有2种逻辑：
  -- 第1种逻辑：通过LV1、LV2、LV3关联取L1名称
  spart_l1_tmp1 as(
  select t1.version_code                    -- 版本编码
       , t1.period_id                          -- 会计期
       , t1.spart_code                              -- Part编码
       , t1.spart_desc                              -- Part描述
       , t1.spart_qty                               -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue                             -- Spart收入金额_人民币
       , t1.usd_revenue                             -- Spart收入金额_美金
       , t1.rmb_cost                                -- Bpart成本金额_人民币
       , t1.usd_cost                                -- Bpart成本金额_美金
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key                                -- 产品KEY
       , t1.prod_code                               -- 产品编码
       , t1.prod_cn_name                            -- 产品中文描述
       , t1.bg_code                                 -- BG编码
       , t1.bg_name                                 -- BG中文名称
       , t1.bg_en_name                              -- BG英文名称
       , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
       , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
       , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
       , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
       , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
       , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
       , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
       , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
       , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
       , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
       , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
       , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
       , t1.plan_com_lv1                         -- 一级计委包
       , t1.plan_com_lv2                         -- 二级计委包
       , t1.plan_com_lv3                         -- 三级计委包
       , t1.busi_lv4                             -- 四级业务包
       , t1.geo_pc_key                              -- 区域责任中心KEY
       , t1.oversea_flag                            -- 海外标志
       , t2.l1_name                                 -- L1名称
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
    from spart_all_tmp t1  -- 5张来源表数据合并的临时表
    left join fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t t2  -- ICT业务预测全景图维表
      on t1.lv1_prod_rnd_team_code = t2.lv1_code
     and t1.lv2_prod_rnd_team_code = t2.lv2_code
     and t1.lv3_prod_rnd_team_code = t2.lv3_code
     and t2.del_flag = 'N'
     and upper(t2.status) = 'SUBMIT'
  ),
  spart_l1_tmp2 as(
  select t1.version_code                    -- 版本编码
       , t1.period_id                          -- 会计期
       , t1.spart_code                              -- Part编码
       , t1.spart_desc                              -- Part描述
       , t1.spart_qty                               -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue                             -- Spart收入金额_人民币
       , t1.usd_revenue                             -- Spart收入金额_美金
       , t1.rmb_cost                                -- Bpart成本金额_人民币
       , t1.usd_cost                                -- Bpart成本金额_美金
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key                                -- 产品KEY
       , t1.prod_code                               -- 产品编码
       , t1.prod_cn_name                            -- 产品中文描述
       , t1.bg_code                                 -- BG编码
       , t1.bg_name                                 -- BG中文名称
       , t1.bg_en_name                              -- BG英文名称
       , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
       , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
       , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
       , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
       , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
       , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
       , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
       , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
       , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
       , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
       , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
       , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
       , t1.plan_com_lv1                         -- 一级计委包
       , t1.plan_com_lv2                         -- 二级计委包
       , t1.plan_com_lv3                         -- 三级计委包
       , t1.busi_lv4                             -- 四级业务包
       , t1.geo_pc_key                              -- 区域责任中心KEY
       , t1.oversea_flag                            -- 海外标志
       , t2.l1_name                                 -- L1名称
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
    from spart_l1_tmp1 t1
    left join (select distinct lv1_code, lv2_code, l1_name, articulation_flag from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t 
                where del_flag = 'N' and upper(status) = 'SUBMIT'and lv3_name is null
              ) t2  -- ICT业务预测全景图维表
      on t1.lv1_prod_rnd_team_code = t2.lv1_code
     and t1.lv2_prod_rnd_team_code = t2.lv2_code
   where t1.l1_name is null  -- 取排除已匹配L1的数据
  union all
  select t1.version_code                    -- 版本编码
       , t1.period_id                          -- 会计期
       , t1.spart_code                              -- Part编码
       , t1.spart_desc                              -- Part描述
       , t1.spart_qty                               -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue                             -- Spart收入金额_人民币
       , t1.usd_revenue                             -- Spart收入金额_美金
       , t1.rmb_cost                                -- Bpart成本金额_人民币
       , t1.usd_cost                                -- Bpart成本金额_美金
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key                                -- 产品KEY
       , t1.prod_code                               -- 产品编码
       , t1.prod_cn_name                            -- 产品中文描述
       , t1.bg_code                                 -- BG编码
       , t1.bg_name                                 -- BG中文名称
       , t1.bg_en_name                              -- BG英文名称
       , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
       , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
       , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
       , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
       , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
       , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
       , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
       , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
       , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
       , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
       , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
       , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
       , t1.plan_com_lv1                         -- 一级计委包
       , t1.plan_com_lv2                         -- 二级计委包
       , t1.plan_com_lv3                         -- 三级计委包
       , t1.busi_lv4                             -- 四级业务包
       , t1.geo_pc_key                              -- 区域责任中心KEY
       , t1.oversea_flag                            -- 海外标志
       , t1.l1_name                                 -- L1名称
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t2.industry_type
    from spart_l1_tmp1 t1
    left join ict_fcst_holistic_tmp t2
      on t1.lv1_prod_rnd_team_code = t2.lv1_code
   where t1.l1_name is not null  -- 取第1种逻辑匹配的L1数据
  ),
  -- 20230423 add by qwx1110218 L1宽表新增“勾稽方法标签”字段
  spart_l1_tmp3 as(
  select t1.version_code                    -- 版本编码
       , t1.period_id                          -- 会计期
       , t1.spart_code                              -- Part编码
       , t1.spart_desc                              -- Part描述
       , t1.spart_qty                               -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue                             -- Spart收入金额_人民币
       , t1.usd_revenue                             -- Spart收入金额_美金
       , t1.rmb_cost                                -- Bpart成本金额_人民币
       , t1.usd_cost                                -- Bpart成本金额_美金
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key                                -- 产品KEY
       , t1.prod_code                               -- 产品编码
       , t1.prod_cn_name                            -- 产品中文描述
       , t1.bg_code                                 -- BG编码
       , t1.bg_name                                 -- BG中文名称
       , t1.bg_en_name                              -- BG英文名称
       , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
       , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
       , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
       , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
       , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
       , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
       , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
       , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
       , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
       , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
       , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
       , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
       , t1.plan_com_lv1                         -- 一级计委包
       , t1.plan_com_lv2                         -- 二级计委包
       , t1.plan_com_lv3                         -- 三级计委包
       , t1.busi_lv4                             -- 四级业务包
       , t1.geo_pc_key                              -- 区域责任中心KEY
       , t1.oversea_flag                            -- 海外标志
       , t1.l1_name                                 -- L1名称
       , (case when t1.industry_type = 'OTHR' then 'SCENO3' else t3.articulation_flag end) as articulation_flag  -- -- 勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
    from spart_l1_tmp2 t1
    left join (select distinct lv1_code, l1_name, articulation_flag from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t 
                where del_flag = 'N' and upper(status) = 'SUBMIT' 
              ) t3  -- 关联产业维表取“勾稽方法标签”
      on ((t1.lv1_prod_rnd_team_code = t3.lv1_code and t3.l1_name is null) or (t1.lv1_prod_rnd_team_code = t3.lv1_code and t1.l1_name = t3.l1_name and t3.l1_name is not null))
  )
  -- 数据入到目标表
  insert into fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t(
           version_code                            -- 版本编码
         , period_id                               -- 会计期
         , spart_code                              -- Part编码
         , spart_desc                              -- Part描述
         , spart_qty                               -- Part物料数量
         , ship_qty
         , snop_quantity
         , snop_plan_quantity
         , rmb_revenue                             -- Spart收入金额_人民币
         , usd_revenue                             -- Spart收入金额_美金
         , rmb_cost                                -- Bpart成本金额_人民币
         , usd_cost                                -- Bpart成本金额_美金
         , equip_rev_rmb_amt        -- 设备收入人民币金额
         , equip_rev_usd_amt        -- 设备收入美元金额
         , equip_cost_rmb_amt          -- 设备成本人民币金额
         , equip_cost_usd_amt          -- 设备成本美元金额
         , prod_key                                -- 产品KEY
         , prod_code                               -- 产品编码
         , prod_cn_name                            -- 产品中文描述
         , bg_code                                 -- BG编码
         , bg_name                                 -- BG中文名称
         , bg_en_name                              -- BG英文名称
         , lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
         , lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
         , lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
         , lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
         , lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
         , lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
         , lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
         , lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
         , lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
         , lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
         , lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
         , lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
         , plan_com_lv1                            -- 一级计委包
         , plan_com_lv2                            -- 二级计委包
         , plan_com_lv3                            -- 三级计委包
         , busi_lv4                                -- 四级业务包
         , geo_pc_key                              -- 区域责任中心KEY
         , oversea_flag                            -- 海外标志
         , l1_name                                 -- L1名称
         , articulation_flag                       -- 勾稽方法标签
         , phase_date
         , plan_unit_quantity
         , unit
         , source_table
         , industry_type
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select /*+set global(enable_force_vector_engine on)*/t1.version_code                       -- 版本编码
         , t1.period_id                          -- 会计期
         , t1.spart_code                              -- Part编码
         , t1.spart_desc                              -- Part描述
         , t1.spart_qty                               -- Part物料数量
         , t1.ship_qty
         , t1.snop_quantity
         , t1.snop_plan_quantity
         , t1.rmb_revenue                             -- Spart收入金额_人民币
         , t1.usd_revenue                             -- Spart收入金额_美金
         , t1.rmb_cost                                -- Bpart成本金额_人民币
         , t1.usd_cost                                -- Bpart成本金额_美金
         , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
         , t1.equip_rev_usd_amt        -- 设备收入美元金额
         , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
         , t1.equip_cost_usd_amt          -- 设备成本美元金额
         , t1.prod_key                                -- 产品KEY
         , t1.prod_code                               -- 产品编码
         , t1.prod_cn_name                            -- 产品中文描述
         , t1.bg_code                                 -- BG编码
         , t1.bg_name                                 -- BG中文名称
         , t1.bg_en_name                              -- BG英文名称
         , t1.lv0_prod_rnd_team_code                  -- 重量级团队LV0编码
         , t1.lv0_prod_rd_team_cn_name                -- 重量级团队LV0中文描述
         , t1.lv0_prod_rd_team_en_name                -- 重量级团队LV0英文描述
         , t1.lv1_prod_rnd_team_code                  -- 重量级团队LV1编码
         , t1.lv1_prod_rd_team_cn_name                -- 重量级团队LV1中文描述
         , t1.lv1_prod_rd_team_en_name                -- 重量级团队LV1英文描述
         , t1.lv2_prod_rnd_team_code                  -- 重量级团队LV2编码
         , t1.lv2_prod_rd_team_cn_name                -- 重量级团队LV2中文描述
         , t1.lv2_prod_rd_team_en_name                -- 重量级团队LV2英文描述
         , t1.lv3_prod_rnd_team_code                  -- 重量级团队LV3编码
         , t1.lv3_prod_rd_team_cn_name                -- 重量级团队LV3中文描述
         , t1.lv3_prod_rd_team_en_name                -- 重量级团队LV3英文描述
         , t1.plan_com_lv1                         -- 一级计委包
         , t1.plan_com_lv2                         -- 二级计委包
         , t1.plan_com_lv3                         -- 三级计委包
         , t1.busi_lv4                             -- 四级业务包
         , t1.geo_pc_key                              -- 区域责任中心KEY
         , t1.oversea_flag                            -- 海外标志
         , t1.l1_name                                 -- L1名称
         , t1.articulation_flag                       -- 勾稽方法标签
         , t1.phase_date
         , t1.plan_unit_quantity
         , t1.unit
         , t1.source_table
         , t1.industry_type
         , '' as remark
         , -1::int8 as created_by
         , CURRENT_TIMESTAMP as creation_date
         , -1::int8 as last_updated_by
         , CURRENT_TIMESTAMP as last_update_date
         , 'N' as del_flag
      from spart_l1_tmp3 t1
    ;
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

exception
  	when others then
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_row_count => null,
        p_log_errbuf => sqlstate  --错误编码
      ) ;
      
	x_success_flag := '2001';

	-- 收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

