-- ----------------------------
-- Table structure for dm_fop_label_modify_info_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_label_modify_info_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_label_modify_info_t" (
  "id" varchar(50) COLLATE "pg_catalog"."default",
  "page_module" varchar(200) COLLATE "pg_catalog"."default",
  "creation_date" timestamp(6) DEFAULT now(),
  "last_update_date" timestamp(6),
  "created_by" varchar(100) COLLATE "pg_catalog"."default",
  "last_updated_by" varchar(100) COLLATE "pg_catalog"."default",
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "file_source_key" varchar(200) COLLATE "pg_catalog"."default",
  "file_name" varchar(200) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "del_flag" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'N'::character varying
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."id" IS 'UID';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."page_module" IS '模块';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."last_update_date" IS '最后更新时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."last_updated_by" IS '最后更新人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."status" IS '状态';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."file_source_key" IS 'file_source_key';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."file_name" IS '文件名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."remark" IS '备注   ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_label_modify_info_t"."del_flag" IS '是否删除 ';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_label_modify_info_t" IS '标签的修改记录表';

