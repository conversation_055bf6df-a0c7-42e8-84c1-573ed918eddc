DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I;
CREATE TABLE FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I (
    ROW_ID BIGSERIAL PRIMARY KEY,
    LV0_PROD_LIST_CODE NVARCHAR2(65),
    PROD_KEY NUMERIC,
    REGION_CN_NAME NVARCHAR2(200),
    REGION_EN_NAME NVARCHAR2(200),
    OFFICE_CN_NAME NVARCHAR2(200),
    PRODUCT_DIMENSION_CN_NAME NVARCHAR2(2000),
    DIMENSION_SUBCATEGORY_CN_NAME NVARCHAR2(2000),
    NEW_CUST_FLAG NVARCHAR2(65),
    COMMON_USD_PERC NUMERIC,
    NEW_PROD_AMOUNT_USD NUMERIC,
    TIME_WINDOW_CODE NVARCHAR2(125),
    START_PERIOD_ID NUMERIC,
    END_CUST_KEY NUMERIC,
    ACCOUNT_DEPT_CUST_KEY NUMERIC,
    CUST_ACCOUNT_NUM NVARCHAR2(50),
    CUST_EN_NAME NVARCHAR2(600),
    GRD_NA_LEVEL_EN_NAME NVARCHAR2(1024),
    PRT_NA_LEVEL_CN_NAME NVARCHAR2(1024),
    DOMESTIC_OR_OVERSEA_CODE NVARCHAR2(150),
    DOMESTIC_OR_OVERSEA_CNAME NVARCHAR2(3000),
    LV3_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV3_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV1_INDUSTRY_CATG_CODE NVARCHAR2(50),
    LV1_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV0_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    SOFTWARE_MARK NVARCHAR2(500),
    IS_SEA NVARCHAR2(5),
    REGION_CODE NVARCHAR2(50),
    OFFICE_EN_NAME NVARCHAR2(200),
    PRODUCT_DIMENSION_GROUP_CODE NVARCHAR2(2000),
    PRODUCT_DIMENSION_GROUP NVARCHAR2(2000),
    DIMENSION_GROUP_CODE_L1 NVARCHAR2(500),
    DIMENSION_GROUP_L1_CN_NAME NVARCHAR2(1000),
    DIMENSION_GROUP_L2_CN_NAME NVARCHAR2(1000),
    DIMENSION_GROUP_L2_EN_NAME NVARCHAR2(1000),
    DIMENSION_SUBCATEGORY_CODE NVARCHAR2(500),
    DIMENSION_SUB_DETAIL_CODE NVARCHAR2(500),
    FREQUENCY_BAND NVARCHAR2(1250),
    COMMON_RMB NUMERIC,
    OTHER_AMOUNT_RMB NUMERIC,
    OTHER_AMOUNT_USD NUMERIC,
    LV0_PROD_LIST_EN_NAME NVARCHAR2(200),
    LV0_PROD_LIST_CN_NAME NVARCHAR2(200),
    LV3_INDUSTRY_CATG_CODE NVARCHAR2(50),
    LV2_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV0_INDUSTRY_CATG_CODE NVARCHAR2(50),
    PROD_CODE NVARCHAR2(190),
    BASELINE_TAG NVARCHAR2(50),
    REPOFFICE_CN_NAME NVARCHAR2(200),
    COUNTRY_ID NUMERIC,
    PRODUCT_DIMENSION_CODE NVARCHAR2(500),
    DIMENSION_GROUP_L1_EN_NAME NVARCHAR2(1000),
    DIMENSION_SUB_DETAIL_EN_NAME NVARCHAR2(2000),
    PROD_AMOUNT_RMB NUMERIC,
    USD_PERC NUMERIC,
    COMMON_RMB_PERC NUMERIC,
    NEW_PROD_AMOUNT_RMB NUMERIC,
    END_PERIOD_ID NUMERIC,
    PRT_NA_FLAG NVARCHAR2(1024),
    PERIOD_ID NUMERIC,
    DATA_SOURCE NVARCHAR2(250),
    DATA_FLAG NVARCHAR2(2000),
    LV2_INDUSTRY_CATG_CODE NVARCHAR2(50),
    LV2_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV0_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    REPOFFICE_CODE NVARCHAR2(50),
    OFFICE_CODE NVARCHAR2(65),
    DIMENSION_KEY NUMERIC,
    PRODUCT_DIMENSION_GROUP_EN_NAME NVARCHAR2(2000),
    DIMENSION_GROUP_CODE_L2 NVARCHAR2(100),
    DIMENSION_SUB_DETAIL_CN_NAME NVARCHAR2(2000),
    PROD_AMOUNT_USD NUMERIC,
    COMMON_USD NUMERIC,
    RMB_PERC NUMERIC,
    GRD_NA_FLAG NVARCHAR2(1024),
    GRD_NA_LEVEL_CODE NVARCHAR2(1024),
    PRT_NA_LEVEL_EN_NAME NVARCHAR2(1024),
    DOMESTIC_OR_OVERSEA_ENAME NVARCHAR2(3000),
    LV1_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    REPOFFICE_EN_NAME NVARCHAR2(200),
    PRODUCT_DIMENSION_EN_NAME NVARCHAR2(2000),
    DIMENSION_SUBCATEGORY_EN_NAME NVARCHAR2(2000),
    NEW_CUST_CATEGORY_CODE NVARCHAR2(65),
    NEW_CUST_CATEGORY_CN_NAME NVARCHAR2(625),
    NEW_CUST_CATEGORY_EN_NAME NVARCHAR2(625),
    PROD_UNIT_QTY NUMERIC,
    IS_ALLOC_EXCEPTION NVARCHAR2(15),
    ENTERPRISE_CUST_KEY NUMERIC,
    CUST_NL_NAME NVARCHAR2(600),
    GRD_NA_LEVEL_CN_NAME NVARCHAR2(1024),
    PRT_NA_LEVEL_CODE NVARCHAR2(1024),
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON TABLE DWK_ICT_PS_PROD_ALLOC_DTL_I IS 'ICT产业产品量纲收入接口表';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_PROD_LIST_CODE IS '产品零级目录代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PROD_KEY IS '产品KEY';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REGION_CN_NAME IS '地区部中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REGION_EN_NAME IS '地区部英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.OFFICE_CN_NAME IS '办事处中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_CN_NAME IS '产品量纲中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUBCATEGORY_CN_NAME IS '产品量纲子类中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_CUST_FLAG IS '系统部分类：TOP_CUST_CATEGORY_CODE,ACCT_COMBINATION_CODE,DOMTC_ENTPS_INDU_CLASS_CODE,INDUSTRY_CLASS_CODE';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.COMMON_USD_PERC IS 'COMMON USD占比金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_PROD_AMOUNT_USD IS '拆分金额_USD';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.TIME_WINDOW_CODE IS '统计时间窗编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.START_PERIOD_ID IS '开始时间';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.END_CUST_KEY IS '最终客户KEY';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.ACCOUNT_DEPT_CUST_KEY IS '系统部报告客户KEY';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.CUST_ACCOUNT_NUM IS '客户账户号';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.CUST_EN_NAME IS '客户英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.GRD_NA_LEVEL_EN_NAME IS '最高层级客户NA级别英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRT_NA_LEVEL_CN_NAME IS '父客户NA级别中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DOMESTIC_OR_OVERSEA_CODE IS 'ICT国内或海外编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DOMESTIC_OR_OVERSEA_CNAME IS 'ICT国内或海外中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV3_INDUSTRY_CATG_CN_NAME IS '三级产业目录中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV3_INDUSTRY_CATG_EN_NAME IS '三级产业目录英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV1_INDUSTRY_CATG_CODE IS '一级产业目录代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV1_INDUSTRY_CATG_EN_NAME IS '一级产业目录英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_INDUSTRY_CATG_CN_NAME IS '零级产业目录中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.SOFTWARE_MARK IS '软硬件标识';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.IS_SEA IS '是否海外：N代表中国区,Y代表海外';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REGION_CODE IS '地区部编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.OFFICE_EN_NAME IS '办事处英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_GROUP_CODE IS '量纲分组编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_GROUP IS '量纲分组';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_CODE_L1 IS '量纲分组编码L1';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_L1_CN_NAME IS '量纲分组L1中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_L2_CN_NAME IS '量纲分组L2中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_L2_EN_NAME IS '量纲分组L2英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUBCATEGORY_CODE IS '产品量纲子类编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUB_DETAIL_CODE IS '量纲子类明细编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.FREQUENCY_BAND IS '频段';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.COMMON_RMB IS 'COMMON RMB金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.OTHER_AMOUNT_RMB IS 'OTHER RMB金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.OTHER_AMOUNT_USD IS 'OTHER USD金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_PROD_LIST_EN_NAME IS '产品零级目录英文名称(BG编码)';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_PROD_LIST_CN_NAME IS '产品零级目录中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV3_INDUSTRY_CATG_CODE IS '三级产业目录代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV2_INDUSTRY_CATG_EN_NAME IS '二级产业目录英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_INDUSTRY_CATG_CODE IS '零级产业目录代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PROD_CODE IS '产品编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.BASELINE_TAG IS '是否量纲基线';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REPOFFICE_CN_NAME IS '代表处中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.COUNTRY_ID IS '国家ID';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_CODE IS '量纲子类编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_L1_EN_NAME IS '量纲分组L1英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUB_DETAIL_EN_NAME IS '量纲子类明细英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PROD_AMOUNT_RMB IS '产品量纲RMB金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.USD_PERC IS 'USD占比率';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.COMMON_RMB_PERC IS 'COMMON RMB占比金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_PROD_AMOUNT_RMB IS '拆分金额_RMB';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.END_PERIOD_ID IS '结束时间';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRT_NA_FLAG IS '父客户NA标签';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PERIOD_ID IS '年月';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DATA_SOURCE IS '数据源';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DATA_FLAG IS '分类标识';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV2_INDUSTRY_CATG_CODE IS '二级产业目录代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV2_INDUSTRY_CATG_CN_NAME IS '二级产业目录中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV0_INDUSTRY_CATG_EN_NAME IS '零级产业目录英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REPOFFICE_CODE IS '代表处编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.OFFICE_CODE IS '办事处代码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_KEY IS '量纲KEY';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_GROUP_EN_NAME IS '量纲分组英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_GROUP_CODE_L2 IS '量纲分组编码L2';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUB_DETAIL_CN_NAME IS '量纲子类明细中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PROD_AMOUNT_USD IS '产品量纲USD金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.COMMON_USD IS 'COMMON USD金额';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.RMB_PERC IS 'RMB占比率';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.GRD_NA_FLAG IS '最高层及客户NA标签';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.GRD_NA_LEVEL_CODE IS '最高层级客户NA级别编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRT_NA_LEVEL_EN_NAME IS '父客户NA级别英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DOMESTIC_OR_OVERSEA_ENAME IS 'ICT国内或海外英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LV1_INDUSTRY_CATG_CN_NAME IS '一级产业目录中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.REPOFFICE_EN_NAME IS '代表处英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRODUCT_DIMENSION_EN_NAME IS '产品量纲英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.DIMENSION_SUBCATEGORY_EN_NAME IS '产品量纲子类英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_CUST_CATEGORY_CODE IS '系统部编码：CNBG：国内-大T系统部、海外-组合客户群；EBG: 国内-行业系统部(中国)、海外-行业系统部(全球)';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_CUST_CATEGORY_CN_NAME IS '系统部中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.NEW_CUST_CATEGORY_EN_NAME IS '系统部英文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PROD_UNIT_QTY IS '产品量纲数量';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.IS_ALLOC_EXCEPTION IS '分摊比例异常标识';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.ENTERPRISE_CUST_KEY IS '企业网报告客户KEY';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.CUST_NL_NAME IS '客户本地语言名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.GRD_NA_LEVEL_CN_NAME IS '最高层级客户NA级别中文名称';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.PRT_NA_LEVEL_CODE IS '父客户NA级别编码';
COMMENT ON COLUMN DWK_ICT_PS_PROD_ALLOC_DTL_I.LAST_UPDATE_DATE IS 'FOP最后更新日期';