DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I;
CREATE TABLE FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I (
    ROW_ID BIGSERIAL NOT NULL PRIMARY KEY,
    BS_CODE NVARCHAR2(250),
    LV0_PROD_LIST_CN_NAME NVARCHAR2(200),
    LV1_PROD_RD_TEAM_EN_NAME NVARCHAR2(600),
    PRODUCT_DIMENSION_EN_NAME NVARCHAR2(2000),
    DOMESTIC_OR_OVERSEA_CODE NVARCHAR2(1000),
    LV2_INDUSTRY_CATG_CODE NVARCHAR2(1000),
    LV2_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    DIMENSION_SUB_DETAIL_CODE NVARCHAR2(1000),
    DIMENSION_SUB_DETAIL_EN_NAME NVARCHAR2(2000),
    BS_CN_NAME NVARCHAR2(250),
    LV0_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    DATA_SOURCE NVARCHAR2(1000),
    BS_VALUE NUMERIC,
    BS_DENOMINATOR_VALUE NUMERIC,
    LV1_PROD_RD_TEAM_CN_NAME NVARCHAR2(600),
    LV2_PROD_RD_TEAM_CN_NAME NVARCHAR2(600),
    LV2_PROD_RD_TEAM_EN_NAME NVARCHAR2(600),
    LV3_PROD_RD_TEAM_CN_NAME NVARCHAR2(600),
    LV3_PROD_RD_TEAM_EN_NAME NVARCHAR2(600),
    PRODUCT_DIMENSION_GROUP_CODE NVARCHAR2(1000),
    PRODUCT_DIMENSION_GROUP NVARCHAR2(2000),
    LV1_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV0_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    DIMENSION_SUB_DETAIL_CN_NAME NVARCHAR2(2000),
    END_PERIOD_ID NUMERIC,
    LV0_PROD_LIST_CODE NVARCHAR2(50),
    LV0_PROD_LIST_EN_NAME NVARCHAR2(200),
    LV1_PROD_RND_TEAM_CODE NVARCHAR2(50),
    LV2_PROD_RND_TEAM_CODE NVARCHAR2(1000),
    DIMENSION_GROUP_L2_CN_NAME NVARCHAR2(1000),
    BS_ATTR_GROUP_ID NUMERIC,
    ATTR_GROUP_CODE NVARCHAR2(250),
    LV3_PROD_RND_TEAM_CODE NVARCHAR2(1000),
    DOMESTIC_OR_OVERSEA_CNAME NVARCHAR2(3000),
    DOMESTIC_OR_OVERSEA_ENAME NVARCHAR2(3000),
    LV3_INDUSTRY_CATG_CODE NVARCHAR2(1000),
    LV3_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV3_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV2_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV0_INDUSTRY_CATG_CODE NVARCHAR2(50),
    DIMENSION_SUBCATEGORY_CODE NVARCHAR2(1000),
    DIMENSION_SUBCATEGORY_EN_NAME NVARCHAR2(2000),
    STAT_PERIOD_ID NUMERIC,
    BS_NUMERATOR_VALUE NUMERIC,
    PRODUCT_DIMENSION_CODE NVARCHAR2(1000),
    PRODUCT_DIMENSION_CN_NAME NVARCHAR2(2000),
    DIMENSION_GROUP_CODE_L2 NVARCHAR2(1000),
    PRODUCT_DIMENSION_GROUP_EN_NAME NVARCHAR2(2000),
    LV1_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    BS_ID NUMERIC,
    TIME_WINDOW_CODE NVARCHAR2(100),
    START_PERIOD_ID NUMERIC,
    CURRENCY_CODE NVARCHAR2(1000),
    OVERSEA_FLAG NVARCHAR2(1000),
    DIMENSION_GROUP_L2_EN_NAME NVARCHAR2(1000),
    LV1_INDUSTRY_CATG_CODE NVARCHAR2(50),
    DIMENSION_SUBCATEGORY_CN_NAME NVARCHAR2(2000),
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON TABLE DWK_BS_PS_REV_UNIT_PRICE_I IS 'ICT产业产品量纲价格基线接口表';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_CODE IS '基线CODE';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_PROD_LIST_CN_NAME IS '产品零级目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_PROD_RD_TEAM_EN_NAME IS '一级产品研发团队英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_EN_NAME IS '产品量纲英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DOMESTIC_OR_OVERSEA_CODE IS 'ICT国内或海外编码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_INDUSTRY_CATG_CODE IS '二级产业目录代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_INDUSTRY_CATG_CN_NAME IS '二级产业目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUB_DETAIL_CODE IS '量纲子类明细编码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUB_DETAIL_EN_NAME IS '量纲子类明细英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_CN_NAME IS '基线中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_INDUSTRY_CATG_CN_NAME IS '零级产业目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DATA_SOURCE IS '数据源:1,非无线RAN与DIS类基线;2,无线RAN与DIS基线';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_VALUE IS '基线值 ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_DENOMINATOR_VALUE IS '分母值 ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_PROD_RD_TEAM_CN_NAME IS '一级产品研发团队中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_PROD_RD_TEAM_CN_NAME IS '二级产品研发团队中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_PROD_RD_TEAM_EN_NAME IS '二级产品研发团队英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_PROD_RD_TEAM_CN_NAME IS '三级产品研发团队中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_PROD_RD_TEAM_EN_NAME IS '三级产品研发团队英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_GROUP_CODE IS '量纲分组编码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_GROUP IS '量纲分组';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_INDUSTRY_CATG_EN_NAME IS '一级产业目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_INDUSTRY_CATG_EN_NAME IS '零级产业目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUB_DETAIL_CN_NAME IS '量纲子类明细中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.END_PERIOD_ID IS '结束时间';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_PROD_LIST_CODE IS '产品零级目录代码 为产品层级中的BG层';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_PROD_LIST_EN_NAME IS '产品零级目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_PROD_RND_TEAM_CODE IS '一级产品研发团队代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_PROD_RND_TEAM_CODE IS '二级产品研发团队代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_GROUP_L2_CN_NAME IS '量纲分组L2中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_ATTR_GROUP_ID IS '基线属性组ID ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.ATTR_GROUP_CODE IS '属性组编码 ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_PROD_RND_TEAM_CODE IS '三级产品研发团队代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DOMESTIC_OR_OVERSEA_CNAME IS 'ICT国内或海外中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DOMESTIC_OR_OVERSEA_ENAME IS 'ICT国内或海外英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_INDUSTRY_CATG_CODE IS '三级产业目录代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_INDUSTRY_CATG_CN_NAME IS '三级产业目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV3_INDUSTRY_CATG_EN_NAME IS '三级产业目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV2_INDUSTRY_CATG_EN_NAME IS '二级产业目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV0_INDUSTRY_CATG_CODE IS '零级产业目录代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUBCATEGORY_CODE IS '量纲子类编码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUBCATEGORY_EN_NAME IS '量纲子类英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.STAT_PERIOD_ID IS '统计时间';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_NUMERATOR_VALUE IS '分子值 ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_CODE IS '产品量纲编码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_CN_NAME IS '产品量纲中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_GROUP_CODE_L2 IS '量纲分组编码L2';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.PRODUCT_DIMENSION_GROUP_EN_NAME IS '量纲分组英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_INDUSTRY_CATG_CN_NAME IS '一级产业目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.BS_ID IS '基线ID';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.TIME_WINDOW_CODE IS '统计时间窗编码 ';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.START_PERIOD_ID IS '开始时间';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.CURRENCY_CODE IS '币种';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.OVERSEA_FLAG IS '海外标志';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_GROUP_L2_EN_NAME IS '量纲分组L2英文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LV1_INDUSTRY_CATG_CODE IS '一级产业目录代码';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.DIMENSION_SUBCATEGORY_CN_NAME IS '量纲子类中文名称';
COMMENT ON COLUMN DWK_BS_PS_REV_UNIT_PRICE_I.LAST_UPDATE_DATE IS '最后更新日期';