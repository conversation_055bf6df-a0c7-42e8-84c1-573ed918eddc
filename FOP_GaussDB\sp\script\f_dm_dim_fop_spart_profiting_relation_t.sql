-- ----------------------------
-- Function structure for f_dm_dim_fop_spart_profiting_relation_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_dim_fop_spart_profiting_relation_t"("p_date" int4, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_dim_fop_spart_profiting_relation_t"(IN "p_date" int4=NULL::integer, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-10-13
创建人  ：鲁广武
背景描述：Spart对象与L1、L2、L3关系维表(给前端),然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		              参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_dim_fop_spart_profiting_relation_t()

修改记录：20230602 qwx1110218 新增 p_date 传入参数，传参格式：yyyymmdd，主要是为了解决每月1号未调度问题
事例    ：select fin_dm_opt_fop.f_dm_dim_fop_spart_profiting_relation_t(20230601);

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_dim_fop_spart_profiting_relation_t('||p_date||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t';
	v_dml_row_count  number default 0 ;
	

begin
	x_success_flag := '1';          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'Spart对象与L1、L2、L3关系维表'||v_tbl_name||':开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 如果传入参数为空，则每月1号调度  
  if(p_date is null) then
	  ---后续每个月版本是每月1号取上月版本的数据作为每个月的历史数据
    if(current_date = substring(regexp_replace(current_date,'-',''),1,6)||'01') then

		---支持重跑，清除目标表要插入会计期的数据
	  delete from  fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
	   where period_id = substring(regexp_replace(current_date,'-',''),1,6);     --'当前年月'保留历史数据

		insert into fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
		(
		 period_id,	                                              ---会计期
		 item_code,                               								---ITEM编码
		 item_desc,                               								---ITEM描述
		 lv1_code,                                								---LV1编码
		 lv1_name,                                								---LV1名称
		 l1_name,                                 								---L1名称
		 l2_name,                                 								---L2名称
		 l2_name_prob,                            								---L2名称预测概率
		 l3_name,                                 								---L3名称
		 l3_name_prob,                            								---L3名称预测概率
		 l1_coefficient,                          								---L1系数
		 l1_coefficient_prob,                     								---L1系数预测概率
		 l2_coefficient,                          								---L2系数
		 l2_coefficient_prob,                     								---L2系数预测概率
		 l3_coefficient,                          								---L3系数
		 l3_coefficient_prob,                     								---L3系数预测概率
		 status,					  								                      ---状态（Save 保存、Submit 提交）
		 data_type,	                  								            ---数据类型（His 历史、Add 新增）
		 update_flag,	                  								          ---修改标识（Y 是、N 否）
		 remark,	                              								  ---备注
		 created_by,	                          								  ---创建人
		 creation_date,	                          								---创建时间
		 last_updated_by,	                      								  ---修改人
		 last_update_date,	                      								---修改时间
		 del_flag	                              								  ---是否删除
		)
	  with spart_profiting_relation_tmp as(
    select period_id,							               ---会计期
	    item_code,							                 ---ITEM编码 
      item_desc,                               ---ITEM描述
		  lv1_code,                                ---LV1编码
		  lv1_name,                                ---LV1名称
		  l1_name,                                 ---L1名称
		  l2_name,                                 ---L2名称
		  l2_name_prob,                            ---L2名称预测概率
		  l3_name,                                 ---L3名称
		  l3_name_prob,                            ---L3名称预测概率
		  l1_coefficient,                          ---L1系数
		  l1_coefficient_prob,                     ---L1系数预测概率
		  l2_coefficient,                          ---L2系数
		  l2_coefficient_prob,                     ---L2系数预测概率
		  l3_coefficient,                          ---L3系数
		  l3_coefficient_prob,                     ---L3系数预测概率
		  status,					  				               ---状态（Save 保存、Submit 提交）
		  data_type,		                           ---数据类型（His 历史、Add 新增）
		  update_flag,	                  	       ---修改标识（Y 是、N 否）
		  del_flag,                                ---是否删除
      row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
    from (
          select distinct  
			       period_id,								                ---会计期
	           item_code,                               ---ITEM编码
             item_desc,                               ---ITEM描述
		         lv1_code,                                ---LV1编码
		         lv1_name,                                ---LV1名称
		         l1_name,                                 ---L1名称
		         l2_name,                                 ---L2名称
		         l2_name_prob,                            ---L2名称预测概率
		         l3_name,                                 ---L3名称
		         l3_name_prob,                            ---L3名称预测概率
		         l1_coefficient,                          ---L1系数
		         l1_coefficient_prob,                     ---L1系数预测概率
		         l2_coefficient,                          ---L2系数
		         l2_coefficient_prob,                     ---L2系数预测概率
		         l3_coefficient,                          ---L3系数
		         l3_coefficient_prob,                     ---L3系数预测概率
		         status,					  			  ---状态（Save 保存、Submit 提交）
		         data_type,		                          ---数据类型（His 历史、Add 新增）
		         update_flag,	                  	      ---修改标识（Y 是、N 否）
		         del_flag                                 ---是否删除
            from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
           where del_flag = 'N'
             and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
			       and period_id = regexp_replace(substring(add_months(current_date,-1),1,7),'-','') --取上个月的数据
         )
    )
      select 		  
        substring(regexp_replace(current_date,'-',''),1,6) as period_id,		---'会计期'处理成当前年月
		    t.item_code,                               ---ITEM编码
		    t.item_desc,                               ---ITEM描述
		    t.lv1_code,                                ---LV1编码
		    t.lv1_name,                                ---LV1名称
		    t.l1_name,                                 ---L1名称
		    t.l2_name,                                 ---L2名称
		    t.l2_name_prob,                            ---L2名称预测概率
		    t.l3_name,                                 ---L3名称
		    t.l3_name_prob,                            ---L3名称预测概率
		    t.l1_coefficient,                          ---L1系数
		    t.l1_coefficient_prob,                     ---L1系数预测概率
		    t.l2_coefficient,                          ---L2系数
		    t.l2_coefficient_prob,                     ---L2系数预测概率
		    t.l3_coefficient,                          ---L3系数
		    t.l3_coefficient_prob,                     ---L3系数预测概率
		    t.status,					  				               ---状态（Save 保存、Submit 提交）
		    'His' as data_type,		                     ---数据类型（His 历史、Add 新增）
		    'N' as update_flag,	                  	   ---修改标识（Y 是、N 否）
        '' as remark,							                 ---备注
        -1 as created_by,                          ---创建人
        current_timestamp as creation_date,        ---创建时间
        -1 as last_updated_by,                     ---修改人
        current_timestamp as last_update_date,     ---修改时间
        t.del_flag                                 ---是否删除
      from spart_profiting_relation_tmp t
      where t.rn = 1
      ;
      
      v_dml_row_count := sql%rowcount;  -- 收集数据量
	    
	    -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => to_char(current_date,'yyyymm')||'月初始化数据量（取的上月数据）：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

      delete from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
	     where period_id = regexp_replace(substring(add_months(current_date,-1),1,7),'-','')   --删除上个月'Save'的数据
	       and upper(status) = 'SAVE';  
	    
	    v_dml_row_count := sql%rowcount;  -- 收集数据量
	    
	    -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '删除上个月 Save 的数据量：'||v_dml_row_count||',结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

   --不传参时，不是每个月"1"号时，调AI标签表
  else 
	   ----AI标签，依赖知识表示打标签作业，如果来源表中有数据，则删除目标表中对应数据
	if exists(select count(*) from fin_dm_opt_fop.kr_cpf_ai_spart_rel_t t where upper(t.data_type) = 'AI' and t.period_id = substring(regexp_replace(current_date,'-',''),1,6)) then
		---支持重跑，清除目标表要插入会计期的数据
	delete from  fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
	 where period_id = substring(regexp_replace(current_date,'-',''),1,6)     --当前年月
	   and upper(data_type) = 'ADD';                                          --数据类型为'Add'
	
	----AI标签  入目标表
	insert into fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t  ---Spart对象与L1、L2、L3关系维表已有一版初始化的数据
	(
	 period_id,								                ---会计期
	 item_code,                               ---ITEM编码
	 item_desc,                               ---ITEM描述
	 lv1_code,                                ---LV1编码
	 lv1_name,                                ---LV1名称
	 l1_name,                                 ---L1名称
	 l2_name,                                 ---L2名称
	 l2_name_prob,                            ---L2名称预测概率
	 l3_name,                                 ---L3名称
	 l3_name_prob,                            ---L3名称预测概率
	 l1_coefficient,                          ---L1系数
	 l1_coefficient_prob,                     ---L1系数预测概率
	 l2_coefficient,                          ---L2系数
	 l2_coefficient_prob,                     ---L2系数预测概率
	 l3_coefficient,                          ---L3系数
	 l3_coefficient_prob,                     ---L3系数预测概率
	 status,					  								      ---状态（Save 保存、Submit 提交）
	 data_type,	                  						---数据类型（His 历史、Add 新增）
	 update_flag,	                  					---修改标识（Y 是、N 否）
	 remark,	                                ---备注
	 created_by,	                            ---创建人
	 creation_date,	                          ---创建时间
	 last_updated_by,	                        ---修改人
	 last_update_date,	                      ---修改时间
	 del_flag	                                ---是否删除
	)
   select
      substring(regexp_replace(current_date,'-',''),1,6) as period_id,		---'会计期'处理成当前年月
      t.item_code,                               ---ITEM编码
      t.item_desc,                               ---ITEM描述
      t.lv1_code,                                ---LV1编码
      t.lv1_name,                                ---LV1名称
      t.l1_name,                                 ---L1名称
      t.l2_name,                                 ---L2名称
      t.l2_name_prob,                            ---L2名称预测概率
      case when t.l3_name='无' then '' else t.l3_name end as l3_name,---L3名称
      t.l3_name_prob,                            ---L3名称预测概率
      t.l1_coefficient,                          ---L1系数
      t.l1_coefficient_prob,                     ---L1系数预测概率
      t.l2_coefficient,                          ---L2系数
      t.l2_coefficient_prob,                     ---L2系数预测概率
      t.l3_coefficient,                          ---L3系数
      t.l3_coefficient_prob,                     ---L3系数预测概率
		  'Submit' as status,					  				     ---状态（Save 保存、Submit 提交）   
      'Add' as data_type,	                       ---数据类型(His 历史、Add 新增)
 	    'N' as update_flag,	                  		 ---修改标识（Y 是、N 否）     
      t.remark,	                                 ---备注
      t.created_by,	                             ---创建人
      t.creation_date,	                         ---'创建时间'
      t.last_updated_by,	                       ---修改人
      t.last_update_date,	                       ---修改时间
      'N' as del_flag	                           ---是否删除
    from fin_dm_opt_fop.kr_cpf_ai_spart_rel_t t        ---spart打标签结果(AI标签)
   where upper(t.data_type) = 'AI'                 ---取AI的数据
     and t.period_id = substring(regexp_replace(current_date,'-',''),1,6)     --当前年月   
     ; 
     
   v_dml_row_count := sql%rowcount;  -- 收集数据量
	    
	    -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '知识表示返回的AI标签数据量：'||v_dml_row_count||',结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    
   end if; 
	    
      end if;
      
    else
      -- 传入参数有值，主要用于每月1号如果忘记调度，则可以传参跑数
      -- 支持重跑，清除目标表要插入会计期的数据
	    delete from  fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t where period_id = substr(p_date,1,6);     -- 取传入参数的年月

		  insert into fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t(
		         period_id,	                                              ---会计期
		         item_code,                               								---ITEM编码
		         item_desc,                               								---ITEM描述
		         lv1_code,                                								---LV1编码
		         lv1_name,                                								---LV1名称
		         l1_name,                                 								---L1名称
		         l2_name,                                 								---L2名称
		         l2_name_prob,                            								---L2名称预测概率
		         l3_name,                                 								---L3名称
		         l3_name_prob,                            								---L3名称预测概率
		         l1_coefficient,                          								---L1系数
		         l1_coefficient_prob,                     								---L1系数预测概率
		         l2_coefficient,                          								---L2系数
		         l2_coefficient_prob,                     								---L2系数预测概率
		         l3_coefficient,                          								---L3系数
		         l3_coefficient_prob,                     								---L3系数预测概率
		         status,					  								                      ---状态（Save 保存、Submit 提交）
		         data_type,	                  								            ---数据类型（His 历史、Add 新增）
		         update_flag,	                  								          ---修改标识（Y 是、N 否）
		         remark,	                              								  ---备注
		         created_by,	                          								  ---创建人
		         creation_date,	                          								---创建时间
		         last_updated_by,	                      								  ---修改人
		         last_update_date,	                      								---修改时间
		         del_flag	                              								  ---是否删除
		  )
	    with spart_profiting_relation_tmp as(
      select period_id,							               ---会计期
	           item_code,							                 ---ITEM编码 
             item_desc,                               ---ITEM描述
		         lv1_code,                                ---LV1编码
		         lv1_name,                                ---LV1名称
		         l1_name,                                 ---L1名称
		         l2_name,                                 ---L2名称
		         l2_name_prob,                            ---L2名称预测概率
		         l3_name,                                 ---L3名称
		         l3_name_prob,                            ---L3名称预测概率
		         l1_coefficient,                          ---L1系数
		         l1_coefficient_prob,                     ---L1系数预测概率
		         l2_coefficient,                          ---L2系数
		         l2_coefficient_prob,                     ---L2系数预测概率
		         l3_coefficient,                          ---L3系数
		         l3_coefficient_prob,                     ---L3系数预测概率
		         status,					  				               ---状态（Save 保存、Submit 提交）
		         data_type,		                           ---数据类型（His 历史、Add 新增）
		         update_flag,	                  	       ---修改标识（Y 是、N 否）
		         del_flag,                                ---是否删除
             row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
        from (
              select distinct period_id,								                ---会计期
	                   item_code,                               ---ITEM编码
                     item_desc,                               ---ITEM描述
		                 lv1_code,                                ---LV1编码
		                 lv1_name,                                ---LV1名称
		                 l1_name,                                 ---L1名称
		                 l2_name,                                 ---L2名称
		                 l2_name_prob,                            ---L2名称预测概率
		                 l3_name,                                 ---L3名称
		                 l3_name_prob,                            ---L3名称预测概率
		                 l1_coefficient,                          ---L1系数
		                 l1_coefficient_prob,                     ---L1系数预测概率
		                 l2_coefficient,                          ---L2系数
		                 l2_coefficient_prob,                     ---L2系数预测概率
		                 l3_coefficient,                          ---L3系数
		                 l3_coefficient_prob,                     ---L3系数预测概率
		                 status,					  			  ---状态（Save 保存、Submit 提交）
		                 data_type,		                          ---数据类型（His 历史、Add 新增）
		                 update_flag,	                  	      ---修改标识（Y 是、N 否）
		                 del_flag                                 ---是否删除
                from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
               where del_flag = 'N'
                 and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
			           and period_id = to_char(to_date(p_date,'yyyymmdd') - interval'1 month','yyyymm') --取上个月的数据
             )
      )
      select substr(p_date,1,6) as period_id,		---'会计期'处理成当前年月
		         t.item_code,                               ---ITEM编码
		         t.item_desc,                               ---ITEM描述
		         t.lv1_code,                                ---LV1编码
		         t.lv1_name,                                ---LV1名称
		         t.l1_name,                                 ---L1名称
		         t.l2_name,                                 ---L2名称
		         t.l2_name_prob,                            ---L2名称预测概率
		         t.l3_name,                                 ---L3名称
		         t.l3_name_prob,                            ---L3名称预测概率
		         t.l1_coefficient,                          ---L1系数
		         t.l1_coefficient_prob,                     ---L1系数预测概率
		         t.l2_coefficient,                          ---L2系数
		         t.l2_coefficient_prob,                     ---L2系数预测概率
		         t.l3_coefficient,                          ---L3系数
		         t.l3_coefficient_prob,                     ---L3系数预测概率
		         t.status,					  				               ---状态（Save 保存、Submit 提交）
		         'His' as data_type,		                     ---数据类型（His 历史、Add 新增）
		         'N' as update_flag,	                  	   ---修改标识（Y 是、N 否）
             '' as remark,							                 ---备注
             -1 as created_by,                          ---创建人
             current_timestamp as creation_date,        ---创建时间
             -1 as last_updated_by,                     ---修改人
             current_timestamp as last_update_date,     ---修改时间
             t.del_flag                                 ---是否删除
        from spart_profiting_relation_tmp t
       where t.rn = 1
      ;
      
      v_dml_row_count := sql%rowcount;  -- 收集数据量
	    
	    -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => to_char(current_date,'yyyymm')||'月初始化数据量（取的上月数据）：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

      delete from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
	     where period_id = to_char(to_date(p_date,'yyyymmdd') - interval'1 month','yyyymm')   --删除上个月'Save'的数据
	       and upper(status) = 'SAVE'; 
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量
	    
	    -- 开始记录日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '删除上个月 Save 的数据量：'||v_dml_row_count||',结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    
    end if;




exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	         --2001表示失败
	
    --收集统计信息
    analyse fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

