-- ----------------------------
-- Function structure for f_dm_fop_spart_l1_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l1_info_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l1_info_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/***************************************************************************************************************************************************************************************
创建时间：2022-10-25
创建人  ：陈芖 cwx1182800
背景描述：SPART明细+L2~L3/L1~L3系数+type类型表按照逻辑加工入到作业对象L1层级，按月全量抽取，支持删除重跑（提供给知识表示）
参数描述：参数一(p_version_code)：版本编码，参数格式：202207_V1
		      参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l1_info_t()
          2023-08-01 lwx1186472	  202309版本区域增加'国内'、'海外'逻辑  
****************************************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l1_info_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l1_info_t';
	v_version_code varchar(50);  -- 版本编码
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
  set enable_force_vector_engine to on;
	x_success_flag := '1';
	
  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L1层级数据：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 currency_union_temp 临时表
  drop table if exists currency_union_temp;
	create temporary table currency_union_temp(
	   	   version_code                   varchar(100)       -- 版本编码
	   	 , period_id                      numeric            -- 会计期
			 , bg_name                        varchar(50)        -- BG编码
			 , bg_code                        varchar(200)       -- BG名称
			 , oversea_desc                   varchar(50)        -- 海外标志
			 , lv1_name                       varchar(600)       -- 重量级团队lv1名称
			 , lv1_code                       varchar(50)        -- 重量级团队lv1编码
			 , lv2_name                       varchar(600)       -- 重量级团队lv2名称
			 , lv2_code                       varchar(50)        -- 重量级团队lv2编码
			 , l1_name                        varchar(200)       -- l1名称
			 , l1_coefficient                 numeric(38,10)     -- l1系数
			 , l2_coefficient                 numeric(38,10)     -- l2系数
			 , currency                       varchar(50)        -- 币种
			 , ship_qty                       numeric(38,10)     -- 发货量（历史）
			 , spart_qty                      numeric(38,10)     -- 收入量（历史）
			 , equip_rev_amt                  numeric(38,10)     -- 设备收入金额
			 , equip_cost_amt                 numeric(38,10)     -- 设备成本金额
			 , equip_rev_cons_before_amt      numeric(38,10)     -- 设备收入额（对价前）
			 , equip_cost_cons_before_amt     numeric(38,10)     -- 设备成本额（对价前）
			 , equip_rev_cons_after_amt       numeric(38,10)     -- 设备收入额（对价后）
			 , equip_cost_cons_after_amt      numeric(38,10)     -- 设备成本 金额 对价后
			 , articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			 , source_table                   varchar(100)       -- 来源表
	)on commit preserve rows distribute by hash(period_id,lv2_code,l1_name)
	;

  -- 创建 oversea_desc_temp 临时表
  drop table if exists oversea_desc_temp;
	create temporary table oversea_desc_temp(
	   	       version_code                   varchar(100)       -- 版本编码
	   	     , period_id                      numeric            -- 会计期			 
			 , bg_code                        varchar(200)       -- BG名称
			 , bg_name                        varchar(50)        -- BG编码
			 , oversea_desc                   varchar(50)        -- 海外标志
			 , lv1_code                       varchar(50)        -- 重量级团队lv1编码
			 , lv1_name                       varchar(600)       -- 重量级团队lv1名称
			 , lv2_code                       varchar(50)        -- 重量级团队lv2编码
			 , lv2_name                       varchar(600)       -- 重量级团队lv2名称
			 , l1_name                        varchar(200)       -- l1名称
			 , equip_rev_cons_before_amt      numeric(38,10)     -- 设备收入额（对价前）
			 , equip_cost_cons_before_amt     numeric(38,10)     -- 设备成本额（对价前）
			 , equip_rev_cons_after_amt       numeric(38,10)     -- 设备收入额（对价后）
			 , equip_cost_cons_after_amt      numeric(38,10)     -- 设备成本 金额 对价后
			 , ship_qty                       numeric(38,10)     -- 发货量（历史）
			 , spart_qty                      numeric(38,10)     -- 收入量（历史）
			 , currency                       varchar(50)        -- 币种
			 , articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	)on commit preserve rows distribute by hash(period_id,lv2_code,l1_name)
	;
	
  -- 创建 l1_name_temp 临时表
  drop table if exists l1_name_temp;
	create temporary table l1_name_temp(l1_name varchar(200))on commit preserve rows;
  
	-- 取来源表的最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_detail_info_t 
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_info_t)
   group by substr(version_code,1,6)
	;

	-- 如果传入版本有值，则取对应的版本数据，否则取最大版本数据
	if(p_version_code is null or p_version_code = '') then
	  --取宽表中的最新标签里的L1，如果传入版本则取版本对应的数据，否则取最大版本的数据
	  insert into l1_name_temp(l1_name)
	  select distinct l1_name from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = v_max_version_code and l1_coefficient > 0
	  ;		--l00521248 20230120
	  
	  v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '从宽表中取的 l1_name 数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
	  
	  -- 判断来源表中是否有最大版本，如有，需要删除目标表中最大版本数据
	  delete fin_dm_opt_fop.dm_fop_spart_l1_info_his_t where version_code = v_max_version_code;

	  --拆分币种分为美元和人民币
	  insert into currency_union_temp(
	     	   version_code                    -- 版本编码
	     	 , period_id                       -- 会计期
	  		 , bg_name                         -- BG编码
	  		 , bg_code                         -- BG名称
	  		 , oversea_desc                    -- 区域描述
	  		 , lv1_name                        -- 重量级团队lv1名称
	  		 , lv1_code                        -- 重量级团队lv1编码
	  		 , lv2_name                        -- 重量级团队lv2名称
	  		 , lv2_code                        -- 重量级团队lv2编码
	  		 , l1_name                         -- l1名称
	  		 , l1_coefficient                  -- l1系数
	  		 , l2_coefficient                  -- l2系数
	  		 , currency                        -- 币种
	  		 , ship_qty                        -- 发货量（历史）
	  		 , spart_qty                       -- 收入量（历史）
	  		 , equip_rev_amt                   -- 设备收入金额
	  		 , equip_cost_amt                  -- 设备成本金额
	  		 , equip_rev_cons_before_amt       -- 设备收入额（对价前）
	  		 , equip_cost_cons_before_amt      -- 设备成本额（对价前）
	  		 , equip_rev_cons_after_amt        -- 设备收入额（对价后）
	  		 , equip_cost_cons_after_amt       -- 设备成本 金额 对价后
	  		 , articulation_flag               -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  		 , source_table                    -- 来源表
	  )
	  select version_code,  -- 版本编码
	      period_id,--会计期
	  		bg_name,--bg名称
	  		bg_code,--bg编码
	  		case when oversea_flag = 'Y' then '海外'
		         when oversea_flag = 'N' then '国内'
		         when oversea_flag = 'G' then '全球'
		         else oversea_flag
		    end as oversea_desc,    ---区域
	  		lv1_prod_rd_team_cn_name as lv1_name,--重量级团队lv1名称
	  		lv1_prod_rnd_team_code as lv1_code,--重量级团队lv1编码
	  		lv2_prod_rd_team_cn_name as lv2_name,--重量级团队lv2名称
	  		lv2_prod_rnd_team_code as lv2_code,--重量级团队lv2编码
	  		l1_name,--l1名称
	  		l1_coefficient,--l1系数
	  		l2_coefficient,--l2系数
	  		'CNY' as currency,
	  		sum ( nvl ( ship_qty, 0 ) ) as ship_qty,--发货量（历史）
	  		sum ( nvl ( spart_qty, 0 ) ) as spart_qty,--收入量（历史）
	  		sum ( nvl ( equip_rev_rmb_amt, 0 ) ) as equip_rev_amt,--设备收入金额
	  		sum ( nvl ( equip_cost_rmb_amt, 0 ) ) as equip_cost_amt,--设备成本金额
	  		sum ( nvl ( rmb_revenue, 0 ) ) as equip_rev_cons_before_amt,--设备收入额（对价前）
	  		sum ( nvl ( rmb_cost, 0 ) ) as equip_cost_cons_before_amt,--设备成本额（对价前）
	  		sum ( nvl ( equip_rev_rmb_amt, 0 ) ) as equip_rev_cons_after_amt,--设备收入额（对价后）
	  		sum ( nvl ( equip_cost_rmb_amt, 0 ) ) as equip_cost_cons_after_amt,--设备成本 金额 对价后
	  		articulation_flag, --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  		source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t
	   where upper(industry_type) = 'TGT'
	     and version_code = v_max_version_code  -- 取最大版本数据
    group by version_code,
        period_id,--会计期
	  		bg_name,--bg名称
	  		bg_code,--bg编码
	  		case when oversea_flag = 'Y' then '海外'
		         when oversea_flag = 'N' then '国内'
		         when oversea_flag = 'G' then '全球'
		         else oversea_flag
		    end,--区域描述
	  		lv1_prod_rd_team_cn_name,--重量级团队lv1名称
	  		lv1_prod_rnd_team_code,--重量级团队lv1编码
	  		lv2_prod_rd_team_cn_name,--重量级团队lv2名称
	  		lv2_prod_rnd_team_code,--重量级团队lv2编码
	  		l1_name,
	  		l1_coefficient,--l1系数
	  		l2_coefficient,--l2系数
	  		articulation_flag,
	  		source_table
    union all
	  select version_code,  -- 版本编码
	      period_id,--会计期
	  		bg_name,--bg名称
	  		bg_code,--bg编码
	  		case when oversea_flag = 'Y' then '海外'
		         when oversea_flag = 'N' then '国内'
		         when oversea_flag = 'G' then '全球'
		         else oversea_flag
		    end as oversea_desc,--区域描述
	  		lv1_prod_rd_team_cn_name as lv1_name,--重量级团队lv1名称
	  		lv1_prod_rnd_team_code as lv1_code,--重量级团队lv1编码
	  		lv2_prod_rd_team_cn_name as lv2_name,--重量级团队lv2名称
	  		lv2_prod_rnd_team_code as lv2_code,--重量级团队lv2编码
	  		l1_name,--l1名称
	  		l1_coefficient,--l1系数
	  		l2_coefficient,--l2系数
	  		'USD' as currency,
	  		sum ( nvl ( ship_qty, 0 ) ) as ship_qty,--发货量（历史）
	  		sum ( nvl ( spart_qty, 0 ) ) as spart_qty,--收入量（历史）
	  		sum ( nvl ( equip_rev_usd_amt, 0 ) ) as equip_rev_amt,--设备收入 金额
	  		sum ( nvl ( equip_cost_usd_amt, 0 ) ) as equip_cost_amt,--设备成本 金额
	  		sum ( nvl ( usd_revenue, 0 ) ) as equip_rev_cons_before_amt,--设备收入额（对价前）
	  		sum ( nvl ( usd_cost, 0 ) ) as equip_cost_cons_before_amt,--设备成本额（对价前）
	  		sum ( nvl ( equip_rev_usd_amt, 0 ) ) as equip_rev_cons_after_amt,--设备收入额（对价后）
	  		sum ( nvl ( equip_cost_usd_amt, 0 ) ) as equip_cost_cons_after_amt,--设备成本 金额 对价后
	  		articulation_flag, --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  		source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t
     where upper(industry_type) = 'TGT'
       and version_code = v_max_version_code  -- 取最大版本数据
    group by version_code,
        period_id,--会计期
	  		bg_name,--bg名称
	  		bg_code,--bg编码
	  		case when oversea_flag = 'Y' then '海外'
		         when oversea_flag = 'N' then '国内'
		         when oversea_flag = 'G' then '全球'
		         else oversea_flag
		    end,--区域描述
	  		lv1_prod_rd_team_cn_name,--重量级团队lv1名称
	  		lv1_prod_rnd_team_code,--重量级团队lv1编码
	  		lv2_prod_rd_team_cn_name,--重量级团队lv2名称
	  		lv2_prod_rnd_team_code, --重量级团队lv2编码
	  		l1_name,
	  		l1_coefficient,--l1系数
	  		l2_coefficient,--l2系数
	  		articulation_flag,
	  		source_table
	  ;

	  v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'currency_union_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	else
	  -- 需要根据传入版本判断来源表中是否有对应版本数据，如有，则删除目标表中对应的版本数据
	  if exists(select distinct version_code from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code) then
	    --取宽表中的最新标签里的L1，如果传入版本则取版本对应的数据，否则取最大版本的数据
	    insert into l1_name_temp(l1_name)
	    select distinct l1_name from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code and l1_coefficient > 0
	    ;		--l00521248 20230120
	    
	    v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '从宽表中取的 l1_name 数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
	    
	    select distinct version_code into v_version_code from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code;
	    delete fin_dm_opt_fop.dm_fop_spart_l1_info_his_t where version_code = v_version_code;
      
      --拆分币种分为美元和人民币
	    insert into currency_union_temp(
	       	   version_code                    -- 版本编码
	       	 , period_id                       -- 会计期
	    		 , bg_name                         -- BG编码
	    		 , bg_code                         -- BG名称
	    		 , oversea_desc                    -- 区域描述
	    		 , lv1_name                        -- 重量级团队lv1名称
	    		 , lv1_code                        -- 重量级团队lv1编码
	    		 , lv2_name                        -- 重量级团队lv2名称
	    		 , lv2_code                        -- 重量级团队lv2编码
	    		 , l1_name                         -- l1名称
	    		 , l1_coefficient                  -- l1系数
	    		 , l2_coefficient                  -- l2系数
	    		 , currency                        -- 币种
	    		 , ship_qty                        -- 发货量（历史）
	    		 , spart_qty                       -- 收入量（历史）
	    		 , equip_rev_amt                   -- 设备收入金额
	    		 , equip_cost_amt                  -- 设备成本金额
	    		 , equip_rev_cons_before_amt       -- 设备收入额（对价前）
	    		 , equip_cost_cons_before_amt      -- 设备成本额（对价前）
	    		 , equip_rev_cons_after_amt        -- 设备收入额（对价后）
	    		 , equip_cost_cons_after_amt       -- 设备成本 金额 对价后
	    		 , articulation_flag               -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		 , source_table                    -- 来源表
	    )
	    select version_code,  -- 版本编码
	        period_id,--会计期
	    		bg_name,--bg名称
	    		bg_code,--bg编码
	  		  case when oversea_flag = 'Y' then '海外'
		           when oversea_flag = 'N' then '国内'
		           when oversea_flag = 'G' then '全球'
		           else oversea_flag
		       end as oversea_desc,
	    		lv1_prod_rd_team_cn_name as lv1_name,--重量级团队lv1名称
	    		lv1_prod_rnd_team_code as lv1_code,--重量级团队lv1编码
	    		lv2_prod_rd_team_cn_name as lv2_name,--重量级团队lv2名称
	    		lv2_prod_rnd_team_code as lv2_code,--重量级团队lv2编码
	    		l1_name,--l1名称
	    		l1_coefficient,--l1系数
	    		l2_coefficient,--l2系数
	    		'CNY' as currency,
	    		sum ( nvl ( ship_qty, 0 ) ) as ship_qty,--发货量（历史）
	    		sum ( nvl ( spart_qty, 0 ) ) as spart_qty,--收入量（历史）
	    		sum ( nvl ( equip_rev_rmb_amt, 0 ) ) as equip_rev_amt,--设备收入金额
	    		sum ( nvl ( equip_cost_rmb_amt, 0 ) ) as equip_cost_amt,--设备成本金额
	    		sum ( nvl ( rmb_revenue, 0 ) ) as equip_rev_cons_before_amt,--设备收入额（对价前）
	    		sum ( nvl ( rmb_cost, 0 ) ) as equip_cost_cons_before_amt,--设备成本额（对价前）
	    		sum ( nvl ( equip_rev_rmb_amt, 0 ) ) as equip_rev_cons_after_amt,--设备收入额（对价后）
	    		sum ( nvl ( equip_cost_rmb_amt, 0 ) ) as equip_cost_cons_after_amt,--设备成本 金额 对价后
	    		articulation_flag, --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t
	     where upper(industry_type) = 'TGT'
	       and version_code = v_version_code   -- 取对应版本的数据
      group by version_code,
          period_id,--会计期
	    		bg_name,--bg名称
	    		bg_code,--bg编码
	    		case when oversea_flag = 'Y' then '海外'
		           when oversea_flag = 'N' then '国内'
		           when oversea_flag = 'G' then '全球'
		           else oversea_flag
		       end,--区域描述
	    		lv1_prod_rd_team_cn_name,--重量级团队lv1名称
	    		lv1_prod_rnd_team_code,--重量级团队lv1编码
	    		lv2_prod_rd_team_cn_name,--重量级团队lv2名称
	    		lv2_prod_rnd_team_code,--重量级团队lv2编码
	    		l1_name,
	    		l1_coefficient,--l1系数
	    		l2_coefficient,--l2系数
	    		articulation_flag,
	    		source_table
      union all
	    select version_code,  -- 版本编码
	        period_id,--会计期
	    		bg_name,--bg名称
	    		bg_code,--bg编码
	  		  case when oversea_flag = 'Y' then '海外'
		           when oversea_flag = 'N' then '国内'
		           when oversea_flag = 'G' then '全球'
		           else oversea_flag
		       end as oversea_desc,--区域描述
	    		lv1_prod_rd_team_cn_name as lv1_name,--重量级团队lv1名称
	    		lv1_prod_rnd_team_code as lv1_code,--重量级团队lv1编码
	    		lv2_prod_rd_team_cn_name as lv2_name,--重量级团队lv2名称
	    		lv2_prod_rnd_team_code as lv2_code,--重量级团队lv2编码
	    		l1_name,--l1名称
	    		l1_coefficient,--l1系数
	    		l2_coefficient,--l2系数
	    		'USD' as currency,
	    		sum ( nvl ( ship_qty, 0 ) ) as ship_qty,--发货量（历史）
	    		sum ( nvl ( spart_qty, 0 ) ) as spart_qty,--收入量（历史）
	    		sum ( nvl ( equip_rev_usd_amt, 0 ) ) as equip_rev_amt,--设备收入 金额
	    		sum ( nvl ( equip_cost_usd_amt, 0 ) ) as equip_cost_amt,--设备成本 金额
	    		sum ( nvl ( usd_revenue, 0 ) ) as equip_rev_cons_before_amt,--设备收入额（对价前）
	    		sum ( nvl ( usd_cost, 0 ) ) as equip_cost_cons_before_amt,--设备成本额（对价前）
	    		sum ( nvl ( equip_rev_usd_amt, 0 ) ) as equip_rev_cons_after_amt,--设备收入额（对价后）
	    		sum ( nvl ( equip_cost_usd_amt, 0 ) ) as equip_cost_cons_after_amt,--设备成本 金额 对价后
	    		articulation_flag, --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t
       where upper(industry_type) = 'TGT'
         and version_code = v_version_code   -- 取对应版本的数据
       group by	version_code,
          period_id,--会计期
	    		bg_name,--bg名称
	    		bg_code,--bg编码
	    		case when oversea_flag = 'Y' then '海外'
		           when oversea_flag = 'N' then '国内'
		           when oversea_flag = 'G' then '全球'
		           else oversea_flag
		       end,--区域描述
	    		lv1_prod_rd_team_cn_name,--重量级团队lv1名称
	    		lv1_prod_rnd_team_code,--重量级团队lv1编码
	    		lv2_prod_rd_team_cn_name,--重量级团队lv2名称
	    		lv2_prod_rnd_team_code, --重量级团队lv2编码
	    		l1_name,
	    		l1_coefficient,--l1系数
	    		l2_coefficient,--l2系数
	    		articulation_flag,
	    		source_table
	    ;

	    v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => 'currency_union_temp 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
    else
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本：'||p_version_code||'，来源表中没有此版本，请重新传入版本！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	    x_success_flag := '2001';
	    return; 
    end if;
  end if;

	--取l1系数大于0且最大会计期的l1名称和会计期，取历史数据的L1（即业务打的系数，AI打的系数不需要用到）
/*with l1_name_temp as (
	select 	distinct l1_name,period_id from  fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
	 where  upper(data_type) = 'HIS' and l1_coefficient > 0 and period_id = 202211 --(select max(period_id) from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t) --取最大会计期
			),*/

	--区域描述收敛为全球落表
	insert into oversea_desc_temp(
	   	       version_code                       -- 版本编码
	   	     , period_id                          -- 会计期			 
			 , bg_code                            -- BG名称
			 , bg_name                            -- BG编码
			 , oversea_desc                       -- 海外标志
			 , lv1_code                           -- 重量级团队lv1编码
			 , lv1_name                           -- 重量级团队lv1名称
			 , lv2_code                           -- 重量级团队lv2编码
			 , lv2_name                           -- 重量级团队lv2名称
			 , l1_name                            -- l1名称
			 , equip_rev_cons_before_amt          -- 设备收入额（对价前）
			 , equip_cost_cons_before_amt         -- 设备成本额（对价前）
			 , equip_rev_cons_after_amt           -- 设备收入额（对价后）
			 , equip_cost_cons_after_amt          -- 设备成本 金额 对价后
			 , ship_qty                           -- 发货量（历史）
			 , spart_qty                          -- 收入量（历史）
			 , currency                           -- 币种
			 , articulation_flag                  -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
)		
	--区域描述收敛为全球
	--202309版本区域增加'国内'、'海外'逻辑 	
	 select t1.version_code, -- 版本编码
	        t1.period_id,--	会计期
			t1.bg_code,--	bg编码
			t1.bg_name,--	bg名称
			t1.oversea_desc,--区域描述
			t1.lv1_code,--	重量级团队lv1编码
			t1.lv1_name,--	重量级团队lv1描述
			t1.lv2_code,--	重量级团队lv2编码
			t1.lv2_name,--	重量级团队lv2名称
			t1.l1_name,--	l1名称
			sum(t1.equip_rev_cons_before_amt)as equip_rev_cons_before_amt,-- 收入金额(对价前)
			sum(t1.equip_cost_cons_before_amt) as equip_cost_cons_before_amt,-- 成本金额(对价前)
			sum(t1.equip_rev_cons_after_amt) as equip_rev_cons_after_amt,-- 设备收入金额(对价后)
			sum(t1.equip_cost_cons_after_amt) as equip_cost_cons_after_amt,-- 设备成本金额(对价后)
			sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.ship_qty
				else nvl(t1.l2_coefficient,0)*t1.ship_qty
				end) as ship_qty,--发货量（历史）
			sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.spart_qty
				else nvl(t1.l2_coefficient,0)*t1.spart_qty
				end) as spart_qty,--收入量（历史）
			t1.currency,
			t1.articulation_flag
	  from  currency_union_temp t1
 left join  l1_name_temp t2
		on 	t1.l1_name = t2.l1_name
     where  t1.oversea_desc in('国内','海外','全球')    --国内、海外、S&OP预测的全球
  group by  t1.version_code,
            t1.period_id,
			t1.bg_code,
			t1.bg_name,
			t1.oversea_desc,
			t1.lv1_code,
			t1.lv1_name,
			t1.lv2_code,
			t1.lv2_name,
			t1.l1_name,
			t1.currency,
			t1.articulation_flag
union all	
	select t1.version_code, -- 版本编码
	    t1.period_id,--	会计期
			t1.bg_code,--	bg编码
			t1.bg_name,--	bg名称
			'全球' as oversea_desc,--区域描述
			t1.lv1_code,--	重量级团队lv1编码
			t1.lv1_name,--	重量级团队lv1描述
			t1.lv2_code,--	重量级团队lv2编码
			t1.lv2_name,--	重量级团队lv2名称
			t1.l1_name,--	l1名称
			sum(t1.equip_rev_cons_before_amt)as equip_rev_cons_before_amt,-- 收入金额(对价前)
			sum(t1.equip_cost_cons_before_amt) as equip_cost_cons_before_amt,-- 成本金额(对价前)
			sum(t1.equip_rev_cons_after_amt) as equip_rev_cons_after_amt,-- 设备收入金额(对价后)
			sum(t1.equip_cost_cons_after_amt) as equip_cost_cons_after_amt,-- 设备成本金额(对价后)
			sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.ship_qty
				else nvl(t1.l2_coefficient,0)*t1.ship_qty
				end) as ship_qty,--发货量（历史）
			sum(case when t1.l1_name = t2.l1_name then nvl(t1.l1_coefficient,0)*t1.spart_qty
				else nvl(t1.l2_coefficient,0)*t1.spart_qty
				end) as spart_qty,--收入量（历史）
			t1.currency,
			t1.articulation_flag
	  from currency_union_temp t1
 left join  l1_name_temp t2
		on 	t1.l1_name = t2.l1_name
 where lower(t1.source_table) not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t')  -- 排除S&OP表的国内、海外、全球数据
  group by t1.version_code,
      t1.period_id,--	会计期
			t1.bg_code,--	bg编码
			t1.bg_name,--	bg名称
			t1.lv1_code,--	重量级团队lv1编码
			t1.lv1_name,--	重量级团队lv1描述
			t1.lv2_code,--	重量级团队lv2编码
			t1.lv2_name,--	重量级团队lv2名称
			t1.l1_name,--	l1名称
			t1.currency,--币种
			t1.articulation_flag
        ;
	v_dml_row_count := v_dml_row_count + sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '临时表 oversea_desc_temp 数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

   --入 l1_his表
  insert into fin_dm_opt_fop.dm_fop_spart_l1_info_his_t (
			version_code,
			period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			mgp_adjust_ratio,
			carryover_ratio,
			unit_cost,														--单位成本 = l1对价前成本金额/l1收入数量
			unit_price,														--单位价格 = l1对价前收入金额/l1收入数量
			articulation_flag,
			remark,
			created_by,
			creation_date,
			last_updated_by,
			last_update_date,
			del_flag
			)				
	--bg编码和bg名称收敛为集团
with  bg_name_temp as  (
	select version_code,  -- 版本编码
	    period_id,--	会计期
			'PROD0002' as bg_code,--	bg编码
			'ICT' as bg_name,--	bg名称（集团即ICT）
			oversea_desc,--区域描述
			lv1_code,--	重量级团队lv1编码
			lv1_name,--	重量级团队lv1描述
			lv2_code,--	重量级团队lv2编码
			lv2_name,--	重量级团队lv2名称
			l1_name, --	l1名称
			sum(equip_rev_cons_before_amt) as equip_rev_cons_before_amt,-- 收入金额(对价前)
			sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt,-- 成本金额(对价前)
			sum(equip_rev_cons_after_amt) as equip_rev_cons_after_amt,-- 设备收入金额(对价后)
			sum(equip_cost_cons_after_amt) as equip_cost_cons_after_amt,-- 设备成本金额(对价后)
			sum(ship_qty) as ship_qty,--发货量（历史）
			sum(spart_qty) as spart_qty,--收入量（历史）
			currency,
			articulation_flag
	  from  oversea_desc_temp
  group by version_code,
      period_id,--	会计期
			oversea_desc,--区域描述
			lv1_code,--	重量级团队lv1编码
			lv1_name,--	重量级团队lv1描述
			lv2_code,--	重量级团队lv2编码
			lv2_name,--	重量级团队lv2名称
			l1_name,--	l1名称
			currency,--币种
			articulation_flag
 union all
    select version_code,
      period_id,--	会计期
			bg_code,--	bg编码
			bg_name,--	bg名称
			oversea_desc,--区域描述
			lv1_code,--	重量级团队lv1编码
			lv1_name,--	重量级团队lv1描述
			lv2_code,--	重量级团队lv2编码
			lv2_name,--	重量级团队lv2名称
			l1_name,--	l1名称
			equip_rev_cons_before_amt,-- 收入金额(对价前)
			equip_cost_cons_before_amt,-- 成本金额(对价前)
			equip_rev_cons_after_amt,-- 设备收入金额(对价后)
			equip_cost_cons_after_amt,-- 设备成本金额(对价后)
			ship_qty,--发货量（历史）
			spart_qty,--收入量（历史）
			currency,
			articulation_flag--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  from  oversea_desc_temp
			),
	-- 计算场景一的 mca调整率、制毛调整率（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）
	adjust_rate_sceno_tmp as (
	select version_code, -- 版本编码
	    period_id,--会计期
			bg_code,--bg编码
			bg_name,--bg名称
			oversea_desc,--区域
			lv1_code,--重量级团队lv1编码
			lv1_name,--重量级团队lv1描述
			currency,--币种
			sum(equip_rev_cons_before_amt) as equip_rev_cons_before_amt,--设备收入额（对价前）
			sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt,--设备成本额（对价前）
			sum(equip_rev_cons_after_amt) as equip_rev_cons_after_amt,--	设备收入额（对价后）
			sum(equip_cost_cons_after_amt) as equip_cost_cons_after_amt--设备成本额（对价后）
	  from bg_name_temp
	 where articulation_flag = 'SCENO1'
	 group by version_code,
	    period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			currency
	),
	--计算制毛率、mca调整率、结转率、单位成本、单位价格
  mgp_adjust_rate_t as (
	select t1.version_code,  -- 版本编码
	    t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			t1.ship_qty,--发货量（历史）
			t1.spart_qty,--收入量（历史）
			t1.equip_rev_cons_before_amt,--设备收入额（对价前）
			t1.equip_cost_cons_before_amt,--设备成本额（对价前）
			t1.equip_rev_cons_after_amt,--	设备收入额（对价后）
			t1.equip_cost_cons_after_amt,--设备成本额（对价后）
			(case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 0
				    when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				    when nvl(t1.equip_rev_cons_after_amt,0) <> 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0
					  then 1 - t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
				    else null
			 end) as mgp_ratio,--制毛率 = 1 - l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1')
			      then (case when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t2.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t2.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t2.equip_rev_cons_after_amt / t2.equip_rev_cons_before_amt
				          end)
			      when t1.articulation_flag in('SCENO2')
			      then (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_rev_cons_after_amt, 0 ) = 0 then 0
				               when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 then -999999
				               else 1 - t1.equip_rev_cons_after_amt / t1.equip_rev_cons_before_amt
				          end)
				    else null
			 end) as mca_adjust_ratio,--mca调整率 = 1 – 对价后收入/对价前收入
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t2.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_amt,0) <> 0 then t2.equip_cost_cons_before_amt  / t2.equip_rev_cons_before_amt
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt  / t1.equip_rev_cons_before_amt
					         end)
				     else null
			  end) as mgp_ratio_before,--l1对价前成本金额/l1对价前收入金额
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_amt,0) = 0 and nvl(t2.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_amt,0) <> 0 then t2.equip_cost_cons_after_amt  / t2.equip_rev_cons_after_amt
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_amt,0) = 0 and nvl(t1.equip_cost_cons_after_amt,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_amt,0) <> 0 then t1.equip_cost_cons_after_amt  / t1.equip_rev_cons_after_amt
						       end)
				   else null
				   end) as mgp_ratio_after,--l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 and t1.spart_qty = 0 then null
				    when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_qty = 0 then -999999
				    when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_qty / t1.ship_qty
				    else null
			 end) as carryover_ratio,--结转率 = l1收入数量/l1发货数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then t1.equip_cost_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_cost,	--单位成本 = l1对价前成本金额/l1收入数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_qty,0) <> 0 and nvl(t1.equip_rev_cons_before_amt,0) <> 0 then t1.equip_rev_cons_before_amt / t1.spart_qty
				    else null
			 end) as unit_price,	--单位价格 = l1对价前收入金额/l1收入数量
			t1.articulation_flag--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  from bg_name_temp t1
	  left join adjust_rate_sceno_tmp t2
	    on t1.version_code = t2.version_code
	   and t1.period_id = t2.period_id
		 and t1.bg_code      = t2.bg_code
		 and t1.oversea_desc = t2.oversea_desc
		 and t1.lv1_code     = t2.lv1_code
		 and t1.currency     = t2.currency
	)
	--计算制毛调整率
    select version_code,
      period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			(case when mgp_ratio_before <> -999999 and  mgp_ratio_after <> -999999 then mgp_ratio_before - mgp_ratio_after
				  when mgp_ratio_before = -999999 and  mgp_ratio_after = -999999 then 0
				  when  mgp_ratio_before = -999999 or  mgp_ratio_after = -999999  then -999999
			      end) as mgp_adjust_ratio,--制毛调整率
			carryover_ratio,
			unit_cost,
			unit_price,
			articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			'' as remark,--备注
			'-1' as created_by,--创建人
			current_timestamp as creation_date,--创建时间
			'-1' as last_updated_by,--修改人
			current_timestamp as last_update_date,--修改时间
			'N' as del_flag --是否删除
      from  mgp_adjust_rate_t
	  where (ship_qty <> 0 or
			spart_qty <> 0 or
			equip_rev_cons_before_amt <> 0 or
			equip_cost_cons_before_amt <> 0 or
			equip_rev_cons_after_amt <> 0 or
			equip_cost_cons_after_amt <> 0 )
			and (l1_name is not null or l1_name <> '')
	;

	v_dml_row_count := v_dml_row_count + sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'dm_fop_spart_l1_info_his_t 作业对象L1层级数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l1_info_t;
  
  -- 数据入到提供给知识表示的接口表
  if(p_version_code is null or p_version_code = '') then
    insert into fin_dm_opt_fop.dm_fop_spart_l1_info_t(
			period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			mgp_adjust_ratio,
			carryover_ratio,
			unit_cost,
			unit_price,
			articulation_flag,
			remark,
			created_by,
			creation_date,
			last_updated_by,
			last_update_date,
			del_flag
			)
		select period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			mgp_adjust_ratio,
			carryover_ratio,
			unit_cost,
			unit_price,
			articulation_flag,
			remark,
			created_by,
			creation_date,
			last_updated_by,
			last_update_date,
			del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
		 where version_code = v_max_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量
		
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，提供给知识表示的表 dm_fop_spart_l1_info_t 数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
		
  else 
    insert into fin_dm_opt_fop.dm_fop_spart_l1_info_t(
			period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			mgp_adjust_ratio,
			carryover_ratio,
			unit_cost,
			unit_price,
			articulation_flag,
			remark,
			created_by,
			creation_date,
			last_updated_by,
			last_update_date,
			del_flag
			)
		select period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_qty,
			spart_qty,
			equip_rev_cons_before_amt,
			equip_cost_cons_before_amt,
			equip_rev_cons_after_amt,
			equip_cost_cons_after_amt,
			mgp_ratio,
			mca_adjust_ratio,
			mgp_adjust_ratio,
			carryover_ratio,
			unit_cost,
			unit_price,
			articulation_flag,
			remark,
			created_by,
			creation_date,
			last_updated_by,
			last_update_date,
			del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
		 where version_code = p_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量
  
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '传入版本：'||p_version_code||'，提供给知识表示的表 dm_fop_spart_l1_info_t 数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
		
  end if;
  

--处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l1_info_his_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l1_info_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

