-- ----------------------------
-- Function structure for f_kr_cpf_lv1_aggr_fcst_comb_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_kr_cpf_lv1_aggr_fcst_comb_t"("p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_kr_cpf_lv1_aggr_fcst_comb_t"(IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2024-5-10
创建人  ：qwx1110218
背景描述：AI融合预测结果表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期(年月)
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_kr_cpf_lv1_aggr_fcst_comb_t(202212)
*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_kr_cpf_lv1_aggr_fcst_comb_t('||p_period||')';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t';
	v_dml_row_count number default 0 ;


begin
	x_success_flag := '1';        --1表示成功


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'L1预测数'||v_tbl_name||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  if(p_period is not null or p_period <> '') then
    -- 判断m表是否存在传入会计期，然后delete数据
    if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t_m where period_id = p_period) then

		-- 支持重跑，清除目标表传入会计期的数据
		delete from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t where period_id = p_period;

		-- 数据入到目标表
		insert into fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t(
           period_id
         , target_period
         , bg_code
         , bg_name
         , oversea_desc
         , lv1_code
         , lv1_name
         , lv2_code
         , lv2_name
         , currency
         , fcst_type
         , equip_rev_after_fcst
         , mgp_rate_after_fcst
         , phase_date
         , aggregate_flag
         , combined_expert
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , target_period
         , bg_code
         , bg_name
         , oversea_desc
         , lv1_code
         , lv1_name
         , lv2_code
         , lv2_name
         , currency
         , fcst_type
         , equip_rev_after_fcst
         , mgp_rate_after_fcst
         , phase_date
         , aggregate_flag
         , combined_expert
         , '' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
      from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t_m
     where period_id = p_period   -- 取传入会计期月份数据
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'AI融合预测结果表 '||v_tbl_name||',传入的会计期:'||p_period||',的数据量:'||v_dml_row_count||',结束运行',-- 日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    end if
    ;

  else
    -- 判断period_id是否是当前年月，然后delete数据
    if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t_m where period_id = substring(regexp_replace(current_date,'-',''),1,6)) then

    -- 支持重跑，清除目标表要插入会计期的数据
    delete from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t where period_id = substring(regexp_replace(current_date,'-',''),1,6);

    -- 数据入到目标表
		insert into fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t(
           period_id
         , target_period
         , bg_code
         , bg_name
         , oversea_desc
         , lv1_code
         , lv1_name
         , lv2_code
         , lv2_name
         , currency
         , fcst_type
         , equip_rev_after_fcst
         , mgp_rate_after_fcst
         , phase_date
         , aggregate_flag
         , combined_expert
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , target_period
         , bg_code
         , bg_name
         , oversea_desc
         , lv1_code
         , lv1_name
         , lv2_code
         , lv2_name
         , currency
         , fcst_type
         , equip_rev_after_fcst
         , mgp_rate_after_fcst
         , phase_date
         , aggregate_flag
         , combined_expert
         , '' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
      from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t_m
     where period_id = substring(regexp_replace(current_date,'-',''),1,6)  -- 取当前月份数据
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'AI融合预测结果表 '||v_tbl_name||',当前月份的会计期:'||substring(regexp_replace(current_date,'-',''),1,6)||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    end if
    ;
  end if
  ;

  exception
    when others then

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	  x_success_flag := '2001';	       --2001表示失败

    -- 收集统计信息
    analyse fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

