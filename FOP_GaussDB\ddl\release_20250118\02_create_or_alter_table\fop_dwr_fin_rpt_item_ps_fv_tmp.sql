-- ----------------------------
-- Table structure for fop_dwr_fin_rpt_item_ps_fv_tmp
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwr_fin_rpt_item_ps_fv_tmp";
CREATE TABLE "fin_dm_opt_fop"."fop_dwr_fin_rpt_item_ps_fv_tmp" (
  "period_id" numeric NOT NULL,
  "report_item_id" numeric NOT NULL,
  "geo_pc_key" numeric,
  "major_prod_key" numeric,
  "data_category_id" numeric,
  "report_scope_code" varchar(50) COLLATE "pg_catalog"."default",
  "rmb_fact_ex_rate_ptd_amt" numeric(38,10),
  "usd_fact_ex_rate_ptd_amt" numeric(38,10)
)
;
COMMENT ON TABLE "fin_dm_opt_fop"."fop_dwr_fin_rpt_item_ps_fv_tmp" IS 'P&S_损益明细报告数据表临时表';

