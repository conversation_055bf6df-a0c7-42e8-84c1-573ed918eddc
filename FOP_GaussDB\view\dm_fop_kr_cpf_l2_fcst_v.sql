-- ----------------------------
-- View structure for dm_fop_kr_cpf_l2_fcst_v
-- ----------------------------
DROP VIEW IF EXISTS "fin_dm_opt_fop"."dm_fop_kr_cpf_l2_fcst_v";
CREATE VIEW "fin_dm_opt_fop"."dm_fop_kr_cpf_l2_fcst_v" AS  SELECT  *
   FROM (         SELECT  *, 'FCST'::text AS data_type
                   FROM kr_cpf_l2_fcst_t
                  WHERE kr_cpf_l2_fcst_t.phase_date IS NULL OR instr(kr_cpf_l2_fcst_t.phase_date::text, '-'::text) = 0
        UNION 
                 SELECT  *, 'BUDGET'::text AS data_type
                   FROM kr_cpf_l2_fcst_t
                  WHERE kr_cpf_l2_fcst_t.phase_date IS NULL OR instr(kr_cpf_l2_fcst_t.phase_date::text, '-'::text) > 0) __unnamed_subquery__;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table dm_fop_capture_log_info_t
-- ----------------------------
ALTER TABLE "fin_dm_opt_fop"."dm_fop_capture_log_info_t" ADD CONSTRAINT "pk_dm_fop_capture_log_info_t" PRIMARY KEY ("log_id");

