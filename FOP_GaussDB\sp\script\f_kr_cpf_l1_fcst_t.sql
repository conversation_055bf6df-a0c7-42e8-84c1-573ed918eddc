-- ----------------------------
-- Function structure for f_kr_cpf_l1_fcst_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_kr_cpf_l1_fcst_t"("p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_kr_cpf_l1_fcst_t"(IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-11-28
创建人  ：鲁广武
更新时间：2023-02-16
更新人  ：鲁广武  lwx1186472
背景描述：L1预测数,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期(年月)
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_kr_cpf_l1_fcst_t(202212)
修改记录：2023-02-16 lwx1186472   新增会计期参数
          传入会计期，有值：传入值格式：f_kr_cpf_l1_fcst_t(202212) 
          传入会计期，无值：传入值格式：f_kr_cpf_l1_fcst_t()
*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_kr_cpf_l1_fcst_t('||p_period||')';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.kr_cpf_l1_fcst_t';
	v_dml_row_count number default 0 ;
    

begin
	x_success_flag := '1';        --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'L1预测数'||v_tbl_name||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    if (p_period is not null or p_period <> '')  
	 then

		--判断m表是否存在传入会计期，然后delete数据
      if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_l1_fcst_t_m where period_id = p_period)
        then
		 
		--支持重跑，清除目标表传入会计期的数据
		delete from fin_dm_opt_fop.kr_cpf_l1_fcst_t where period_id = p_period; 
		
		--插入目标表数据
		insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t  --L1预测数
		(
		   period_id
		  ,target_period
		  ,bg_code
		  ,bg_name
		  ,oversea_desc
		  ,lv1_code
		  ,lv1_name
		  ,lv2_code
		  ,lv2_name
		  ,l1_name
		  ,currency
		  ,fcst_type
		  ,equip_rev_after_fcst_conf
		  ,equip_rev_after_fcst
		  ,equip_rev_after_fcst_upper
		  ,equip_rev_after_fcst_lower
		  ,mgp_rate_after_fcst_conf
		  ,mgp_rate_after_fcst
		  ,mgp_rate_after_fcst_upper
		  ,mgp_rate_after_fcst_lower
		  ,unit_price_timeseries_fcst_conf
	      ,unit_price_timeseries_fcst
          ,unit_price_timeseries_fcst_upper
          ,unit_price_timeseries_fcst_lower
          ,unit_cost_timeseries_fcst_conf
          ,unit_cost_timeseries_fcst
          ,unit_cost_timeseries_fcst_upper
          ,unit_cost_timeseries_fcst_lower
          ,mca_adjust_ratio_fcst
          ,mgp_adjust_ratio_fcst
          ,carryover_ratio_fcst
          ,articulation_flag
          ,unit_cost_fcst_acc
          ,unit_price_fcst_acc
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  ,phase_date                 	
		 )
		select 
           period_id
          ,target_period
          ,bg_code
          ,bg_name
          ,oversea_desc
          ,lv1_code
          ,lv1_name
          ,lv2_code
          ,lv2_name
          ,l1_name
          ,currency
          ,fcst_type
          ,equip_rev_after_fcst_conf
          ,equip_rev_after_fcst
          ,equip_rev_after_fcst_upper
          ,equip_rev_after_fcst_lower
          ,mgp_rate_after_fcst_conf
          ,mgp_rate_after_fcst
          ,mgp_rate_after_fcst_upper
          ,mgp_rate_after_fcst_lower
          ,unit_price_timeseries_fcst_conf
          ,unit_price_timeseries_fcst
          ,unit_price_timeseries_fcst_upper
          ,unit_price_timeseries_fcst_lower
          ,unit_cost_timeseries_fcst_conf
          ,unit_cost_timeseries_fcst
          ,unit_cost_timeseries_fcst_upper
          ,unit_cost_timeseries_fcst_lower
          ,mca_adjust_ratio_fcst
          ,mgp_adjust_ratio_fcst
          ,carryover_ratio_fcst
          ,articulation_flag
          ,unit_cost_fcst_acc
          ,unit_price_fcst_acc
		  ,'' as remark                                  
		  ,-1 as created_by                              
		  ,current_timestamp as creation_date            
		  ,-1 as last_updated_by                         
		  ,current_timestamp as last_update_date         
		  ,'N' as del_flag                               
		  ,phase_date                 		
	from fin_dm_opt_fop.kr_cpf_l1_fcst_t_m                    
 where period_id = p_period                            --取传入会计期月份数据
	  ; 

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'L1预测数'||v_tbl_name||',传入的会计期:'||p_period||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      -- 24年9月版新增逻辑：由于知识表示此版本没有做区域=其他的时序预测，所以需要先造一版区域=其他的数据
      insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t(
		         period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag  		
		  )
		  select distinct period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , '分月法' as fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
		    from fin_dm_opt_fop.kr_cpf_l1_act_t
		   where period_id = p_period
		     and oversea_desc = '其他'
	   ;
	   
	   insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t(
		         period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag 		
		  )
		  select distinct period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , 'YTD法' as fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
		    from fin_dm_opt_fop.kr_cpf_l1_act_t
		   where period_id = p_period
		     and oversea_desc = '其他'
	   ;	  
	  
     end if
	    ;

  else		
		--判断period_id是否是当前年月，然后delete数据
      if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_l1_fcst_t_m where period_id = substring(regexp_replace(current_date,'-',''),1,6))
     then 
		
		--支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_fop.kr_cpf_l1_fcst_t where period_id = substring(regexp_replace(current_date,'-',''),1,6);
		
		--插入目标表数据
		insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t  --L1预测数
		  (
		   period_id
		  ,target_period
		  ,bg_code
		  ,bg_name
		  ,oversea_desc
		  ,lv1_code
		  ,lv1_name
		  ,lv2_code
		  ,lv2_name
		  ,l1_name
		  ,currency
		  ,fcst_type
		  ,equip_rev_after_fcst_conf
		  ,equip_rev_after_fcst
		  ,equip_rev_after_fcst_upper
		  ,equip_rev_after_fcst_lower
		  ,mgp_rate_after_fcst_conf
		  ,mgp_rate_after_fcst
		  ,mgp_rate_after_fcst_upper
		  ,mgp_rate_after_fcst_lower
		  ,unit_price_timeseries_fcst_conf
	      ,unit_price_timeseries_fcst
          ,unit_price_timeseries_fcst_upper
          ,unit_price_timeseries_fcst_lower
          ,unit_cost_timeseries_fcst_conf
          ,unit_cost_timeseries_fcst
          ,unit_cost_timeseries_fcst_upper
          ,unit_cost_timeseries_fcst_lower
          ,mca_adjust_ratio_fcst
          ,mgp_adjust_ratio_fcst
          ,carryover_ratio_fcst
          ,articulation_flag
          ,unit_cost_fcst_acc
          ,unit_price_fcst_acc
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  ,phase_date  		
		  )
		select 
		   period_id
		  ,target_period
		  ,bg_code
		  ,bg_name
		  ,oversea_desc
		  ,lv1_code
		  ,lv1_name
		  ,lv2_code
		  ,lv2_name
		  ,l1_name
		  ,currency
		  ,fcst_type
		  ,equip_rev_after_fcst_conf
		  ,equip_rev_after_fcst
		  ,equip_rev_after_fcst_upper
		  ,equip_rev_after_fcst_lower
		  ,mgp_rate_after_fcst_conf
		  ,mgp_rate_after_fcst
		  ,mgp_rate_after_fcst_upper
		  ,mgp_rate_after_fcst_lower
		  ,unit_price_timeseries_fcst_conf
	      ,unit_price_timeseries_fcst
          ,unit_price_timeseries_fcst_upper
          ,unit_price_timeseries_fcst_lower
          ,unit_cost_timeseries_fcst_conf
          ,unit_cost_timeseries_fcst
          ,unit_cost_timeseries_fcst_upper
          ,unit_cost_timeseries_fcst_lower
          ,mca_adjust_ratio_fcst
          ,mgp_adjust_ratio_fcst
          ,carryover_ratio_fcst
          ,articulation_flag
          ,unit_cost_fcst_acc
          ,unit_price_fcst_acc
		  ,'' as remark                                  
		  ,-1 as created_by                              
		  ,current_timestamp as creation_date            
		  ,-1 as last_updated_by                         
		  ,current_timestamp as last_update_date         
		  ,'N' as del_flag                               
		  ,phase_date                 
	from fin_dm_opt_fop.kr_cpf_l1_fcst_t_m                    --L1当前月数据
 where period_id = substring(regexp_replace(current_date,'-',''),1,6)      --取当前月份数据
	  ;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'L1预测数'||v_tbl_name||',当前月份的会计期:'||substring(regexp_replace(current_date,'-',''),1,6)||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      -- 24年9月版新增逻辑：由于知识表示此版本没有做区域=其他的时序预测，所以需要先造一版区域=其他的数据
      insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t(
		         period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag 		
		  )
		  select distinct period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , '分月法' as fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
		    from fin_dm_opt_fop.kr_cpf_l1_act_t
		   where period_id = substring(regexp_replace(current_date,'-',''),1,6)
		     and oversea_desc = '其他'
	   ;
	   
	   insert into fin_dm_opt_fop.kr_cpf_l1_fcst_t(
		         period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag		
		  )
		  select distinct period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , currency
           , 'YTD' as fcst_type
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
		    from fin_dm_opt_fop.kr_cpf_l1_act_t
		   where period_id = substring(regexp_replace(current_date,'-',''),1,6)
		     and oversea_desc = '其他'
	   ;
	  
	end if 
	   ;
end if 
   ;
   
     --收集统计信息
    analyse fin_dm_opt_fop.kr_cpf_l1_fcst_t;	

exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	       --2001表示失败

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

