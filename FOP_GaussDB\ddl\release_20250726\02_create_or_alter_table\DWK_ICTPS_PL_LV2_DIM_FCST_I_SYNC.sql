DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC;
CREATE TABLE FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC (
    ROW_ID BIGSERIAL PRIMARY KEY,
    STAT_PERIOD NUMERIC,
    CUBE_CODE VARCHAR(100),
    SCENARIO_CODE VARCHAR(625),
    INDEX_LEVEL_NO VARCHAR(100),
    CURRENCY_CODE VARCHAR(625),
    VIEW_CODE VARCHAR(100),
    VIEW_DIM_CODE VARCHAR(100),
    VIEW_LEVEL_NO VARCHAR(100),
    PRODUCT_CODE VARCHAR(625),
    CUSTOMER_DIM_CODE VARCHAR(100),
    DEPARTMENT_CODE VARCHAR(100),
    DEPARTMENT_DIM_CODE VARCHAR(100),
    CAPEXTY<PERSON>E_CODE VARCHAR(100),
    MISCELLANEOUS1_DIM_CODE VARCHAR(100),
    MISCELLANEOUS2_LEVEL_NO VARCHAR(100),
    MISCELLANEOUS3_DIM_CODE VARCHAR(100),
    AMOUNT NUMERIC,
    PERIOD_CODE VARCHAR(625),
    MEASUREMENT_DIM_CODE VARCHAR(100),
    TRANSACTIONMODEL_DIM_CODE VARCHAR(100),
    GEOGRAPHY_CODE VARCHAR(625),
    REPORTCATEGORY_CODE VARCHAR(100),
    REPORTCATEGORY_LEVEL_NO VARCHAR(100),
    INDEXDETAIL_CODE VARCHAR(100),
    PARTITION_VALUE NUMERIC,
    ATTRIBUTE4 VARCHAR(100),
    SCE_FLAG VARCHAR(250),
    YEAR_CODE VARCHAR(625),
    VERSION_CODE VARCHAR(625),
    MEASUREMENT_LEVEL_NO VARCHAR(100),
    TRANSACTIONMODEL_CODE VARCHAR(100),
    PRODUCT_DIM_CODE VARCHAR(100),
    PRODUCT_LEVEL_NO VARCHAR(100),
    GEOGRAPHY_DIM_CODE VARCHAR(100),
    GEOGRAPHY_LEVEL_NO VARCHAR(100),
    CUSTOMER_CODE VARCHAR(625),
    INDEXDETAIL_DIM_CODE VARCHAR(100),
    MISCELLANEOUS2_DIM_CODE VARCHAR(100),
    MISCELLANEOUS3_LEVEL_NO VARCHAR(100),
    TYPE_CODE VARCHAR(625),
    PARTITION_CODE VARCHAR(100),
    ATTRIBUTE5 VARCHAR(100),
    APP_CODE VARCHAR(100),
    VERSION_DIM_CODE VARCHAR(100),
    VERSION_LEVEL_NO VARCHAR(100),
    INDEX_DIM_CODE VARCHAR(100),
    MEASUREMENT_CODE VARCHAR(100),
    DEPARTMENT_LEVEL_NO VARCHAR(100),
    REPORTCATEGORY_DIM_CODE VARCHAR(100),
    INDEXDETAIL_LEVEL_NO VARCHAR(100),
    CAPEXTYPE_LEVEL_NO VARCHAR(100),
    MISCELLANEOUS3_CODE VARCHAR(100),
    DIM_VERSION_CODE VARCHAR(100),
    DW_LAST_UPDATE_DATE TIMESTAMP,
    INDEX_CODE VARCHAR(100),
    TRANSACTIONMODEL_LEVEL_NO VARCHAR(100),
    CUSTOMER_LEVEL_NO VARCHAR(100),
    CAPEXTYPE_DIM_CODE VARCHAR(100),
    MISCELLANEOUS1_CODE VARCHAR(100),
    MISCELLANEOUS1_LEVEL_NO VARCHAR(100),
    MISCELLANEOUS2_CODE VARCHAR(100),
    ATTRIBUTE1 VARCHAR(100),
    ATTRIBUTE2 VARCHAR(100),
    ATTRIBUTE3 VARCHAR(100),
    CREATE_DATE TIMESTAMP,
    LAST_UPDATE_DATE TIMESTAMP,
    FOP_LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON TABLE DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC IS '产业分析师预测结果';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.STAT_PERIOD IS '导入数据底座会计期';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CUBE_CODE IS '模型编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.SCENARIO_CODE IS '经营活动编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEX_LEVEL_NO IS '指标维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CURRENCY_CODE IS '币种编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VIEW_CODE IS '数据视角编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VIEW_DIM_CODE IS '数据视角维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VIEW_LEVEL_NO IS '数据视角维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PRODUCT_CODE IS '产品编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CUSTOMER_DIM_CODE IS '客户维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.DEPARTMENT_CODE IS '部门编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.DEPARTMENT_DIM_CODE IS '部门维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CAPEXTYPE_CODE IS '资产分类编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS1_DIM_CODE IS '综合1维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS2_LEVEL_NO IS '综合2维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS3_DIM_CODE IS '综合3维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.AMOUNT IS '数值,金额';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PERIOD_CODE IS '期间编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MEASUREMENT_DIM_CODE IS '度量维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.TRANSACTIONMODEL_DIM_CODE IS '交易模式维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.GEOGRAPHY_CODE IS '区域编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.REPORTCATEGORY_CODE IS '数据口径编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.REPORTCATEGORY_LEVEL_NO IS '数据口径维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEXDETAIL_CODE IS '指标明细编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PARTITION_VALUE IS '分区值';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ATTRIBUTE4 IS '预留字段4';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.SCE_FLAG IS '数据场景标识';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.YEAR_CODE IS '年份编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VERSION_CODE IS '版本编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MEASUREMENT_LEVEL_NO IS '度量维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.TRANSACTIONMODEL_CODE IS '交易模式编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PRODUCT_DIM_CODE IS '产品维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PRODUCT_LEVEL_NO IS '产品维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.GEOGRAPHY_DIM_CODE IS '区域维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.GEOGRAPHY_LEVEL_NO IS '区域维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CUSTOMER_CODE IS '客户编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEXDETAIL_DIM_CODE IS '指标明细维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS2_DIM_CODE IS '综合2维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS3_LEVEL_NO IS '综合3维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.TYPE_CODE IS '类型编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.PARTITION_CODE IS '分区编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ATTRIBUTE5 IS '预留字段5';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.APP_CODE IS '作业岛编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VERSION_DIM_CODE IS '版本维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.VERSION_LEVEL_NO IS '版本维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEX_DIM_CODE IS '指标维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MEASUREMENT_CODE IS '度量编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.DEPARTMENT_LEVEL_NO IS '部门维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.REPORTCATEGORY_DIM_CODE IS '数据口径维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEXDETAIL_LEVEL_NO IS '指标明细维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CAPEXTYPE_LEVEL_NO IS '资产分类维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS3_CODE IS '综合3编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.DIM_VERSION_CODE IS 'DIM维度树版本编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.DW_LAST_UPDATE_DATE IS 'DW最后更新日期';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.INDEX_CODE IS '指标编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.TRANSACTIONMODEL_LEVEL_NO IS '交易模式维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CUSTOMER_LEVEL_NO IS '客户维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CAPEXTYPE_DIM_CODE IS '资产分类维度树编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS1_CODE IS '综合1编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS1_LEVEL_NO IS '综合1维度树层级';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.MISCELLANEOUS2_CODE IS '综合2编码';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ATTRIBUTE1 IS '预留字段1';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ATTRIBUTE2 IS '预留字段2';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.ATTRIBUTE3 IS '预留字段3';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.LAST_UPDATE_DATE IS 'FOX最后修改时间';
COMMENT ON COLUMN DWK_ICTPS_PL_LV2_DIM_FCST_I_SYNC.FOP_LAST_UPDATE_DATE IS 'FOP最后更新日期';