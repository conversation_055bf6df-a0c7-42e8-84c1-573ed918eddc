-- ----------------------------
-- Function structure for f_apd_fop_ict_fcst_holistic_view_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_apd_fop_ict_fcst_holistic_view_t"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_apd_fop_ict_fcst_holistic_view_t"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-1-31
创建人  ：鲁广武
背景描述：ICT业务预测全景图,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		              参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_apd_fop_ict_fcst_holistic_view_t()

*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_apd_fop_ict_fcst_holistic_view_t';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t';
	v_dml_row_count  number default 0 ;
	

begin
	x_success_flag := '1';          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'ICT业务预测全景图'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  
   --落地临时表,用来更新数据
   	drop table if exists ict_fcst_holistic_view_temp;
    create temporary table ict_fcst_holistic_view_temp
   (
	lv1_code varchar(100),							--LV1编码
	lv1_name varchar(200),                            --LV1名称
	lv2_code varchar(100),                            --LV2编码
	lv2_name varchar(200),                            --LV2名称
	lv3_code varchar(100),                            --LV3编码
	lv3_name varchar(200),                            --LV3名称
	l1_name varchar(100),                             --L1名称 
	articulation_flag varchar(50),                    --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	industry_type varchar(50),                        --产业类型（TGT 目标产业、OTHR 其它产业）
	analysis_flag varchar(10),                        --是否分析场景（Y 是、N 否） 
	remark varchar(500),
	created_by bigint,
	creation_date timestamp(6) without time zone,
	last_updated_by bigint,
	last_update_date timestamp(6) without time zone,
	del_flag varchar(10),	
	status varchar(50)				                --状态(Import 导入、Submit 提交) 	
	) on commit preserve rows distribute by replication;	

    ---插入临时表数据
	insert into ict_fcst_holistic_view_temp	
        (
		 lv1_code,                               --LV1编码
		 lv1_name,                               --LV1名称
		 lv2_code,                               --LV2编码
		 lv2_name,                               --LV2名称
		 lv3_code,                               --LV3编码
		 lv3_name,                               --LV3名称
		 l1_name,                                --L1名称 
		 articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		 industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业）
		 analysis_flag,                          --是否分析场景（Y 是、N 否）
		 remark,                                 --备注   
		 created_by,                             --创建人  
		 creation_date,                          --创建时间 
		 last_updated_by,                        --修改人  
		 last_update_date,                       --修改时间 
		 del_flag,                               --是否删除 		 
		 status                                  --状态(Import 导入、Submit 提交) 				
	    ) 
	---产品维表处理成临时表
     	with product_info_tmp as(
    select lv1_code,
     lv1_name, 
     lv2_code,
     lv2_name,
     lv3_code,
     lv3_name,
     row_number() over(partition by lv1_name,lv2_name,lv3_name order by scd_active_end_date desc) as rn	
     from (
   select distinct 
     lv1_prod_rnd_team_code as lv1_code,
     lv1_prod_rd_team_cn_name as lv1_name, 
     lv2_prod_rnd_team_code as lv2_code,
     lv2_prod_rd_team_cn_name as lv2_name,
     lv3_prod_rnd_team_code as lv3_code,
     lv3_prod_rd_team_cn_name as lv3_name,
     scd_active_end_date
     from dmdim.dm_dim_product_d
    where del_flag = 'N'
      and lv1_prod_rd_team_cn_name in(select distinct lv1_name from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t)
      and scd_active_ind = 1 )
  ),
  ict_fcst_holistic_view_temp2 as (
     select distinct
		     lv1_code,                               --LV1编码
		     lv1_name,                               --LV1名称
		     lv2_code,                               --LV2编码
		     lv2_name,                               --LV2名称
		     lv3_code,                               --LV3编码
		     lv3_name,                               --LV3名称
		     l1_name,                                --L1名称 
		     articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		     (case when l1_name is null then 'OTHR' else 'TGT' end) as industry_type, --产业类型（TGT 目标产业、OTHR 其它产业）
		     analysis_flag,                          --是否分析场景（Y 是、N 否）
		     remark,                                 --备注   
		     created_by,                             --创建人  
		     creation_date,                          --创建时间 
		     last_updated_by,                        --修改人  
		     last_update_date,                       --修改时间 
		     del_flag,                               --是否删除 			 
		     status                                  --状态(Import 导入、Submit 提交)
        from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t
       where status = 'Submit'  
         and del_flag = 'N' 
  	  )  
   select distinct
		     t1.lv1_code,                               --LV1编码
		     t1.lv1_name,                               --LV1名称
		     t1.lv2_code,                               --LV2编码
		     t1.lv2_name,                               --LV2名称
		     t2.lv3_code,                               --LV3编码
		     t1.lv3_name,                               --LV3名称
		     t1.l1_name,                                --L1名称 
		     t1.articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		     t1.industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业）
		     t1.analysis_flag,                          --是否分析场景（Y 是、N 否）
		     t1.remark,                                 --备注   
		     t1.created_by,                             --创建人  
		     t1.creation_date,                          --创建时间 
		     t1.last_updated_by,                        --修改人  
		     t1.last_update_date,                       --修改时间 
		     t1.del_flag,                               --是否删除 			 
		     t1.status                                  --状态(Import 导入、Submit 提交)
        from ict_fcst_holistic_view_temp2 t1  
   left join product_info_tmp t2                 --产品维表
          on t1.lv1_name = t2.lv1_name
	        and t1.lv2_name = t2.lv2_name
	        and t1.lv3_name = t2.lv3_name
          and t2.rn = 1
    where	t1.lv1_name is not null 
      and t1.lv2_name is not null
      and t1.lv3_name is not null
union all
   select distinct
		     t2.lv1_code,                               --LV1编码
		     t1.lv1_name,                               --LV1名称
		     t1.lv2_code,                               --LV2编码
		     t1.lv2_name,                               --LV2名称
		     t1.lv3_code,                               --LV3编码
		     t1.lv3_name,                               --LV3名称
		     t1.l1_name,                                --L1名称 
		     t1.articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		     t1.industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业）
		     t1.analysis_flag,                          --是否分析场景（Y 是、N 否）
		     t1.remark,                                 --备注   
		     t1.created_by,                             --创建人  
		     t1.creation_date,                          --创建时间 
		     t1.last_updated_by,                        --修改人  
		     t1.last_update_date,                       --修改时间 
		     t1.del_flag,                               --是否删除 				 
		     t1.status                                  --状态(Import 导入、Submit 提交)
        from ict_fcst_holistic_view_temp2 t1  
   left join product_info_tmp t2                 --产品维表
          on t1.lv1_name = t2.lv1_name
          and t2.rn = 1
    where	t1.lv1_name is not null
      and t1.lv2_name is null
      and t1.lv3_name is null
  union all
     select distinct
		     t2.lv1_code,                               --LV1编码
		     t1.lv1_name,                               --LV1名称
		     t2.lv2_code,                               --LV2编码
		     t1.lv2_name,                               --LV2名称
		     t1.lv3_code,                               --LV3编码
		     t1.lv3_name,                               --LV3名称
		     t1.l1_name,                                --L1名称 
		     t1.articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		     t1.industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业） 
		     t1.analysis_flag,                          --是否分析场景（Y 是、N 否）
		     t1.remark,                                 --备注   
		     t1.created_by,                             --创建人  
		     t1.creation_date,                          --创建时间 
		     t1.last_updated_by,                        --修改人  
		     t1.last_update_date,                       --修改时间 
		     t1.del_flag,                               --是否删除 				 
		     t1.status                                  --状态(Import 导入、Submit 提交)
        from ict_fcst_holistic_view_temp2 t1  
   left join product_info_tmp t2                 --产品维表
          on t1.lv1_name = t2.lv1_name
	        and t1.lv2_name = t2.lv2_name
          and t2.rn = 1
    where t1.lv1_name is not null
      and t1.lv2_name is not null
      and t1.lv3_name is null
     ;

		
		---支持重跑，清除目标表的数据
	    truncate table fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t;
		
		---插入目标表数据
		insert into fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t
		(
		 lv1_code,                               --LV1编码
		 lv1_name,                               --LV1名称
		 lv2_code,                               --LV2编码
		 lv2_name,                               --LV2名称
		 lv3_code,                               --LV3编码
		 lv3_name,                               --LV3名称
		 l1_name,                                --L1名称 
		 articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		 industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业）
		 analysis_flag,                          --是否分析场景（Y 是、N 否） 
		 remark,                                 --备注   
		 created_by,                             --创建人  
		 creation_date,                          --创建时间 
		 last_updated_by,                        --修改人  
		 last_update_date,                       --修改时间 
		 del_flag,                               --是否删除 
		 status                                  --状态(Import 导入、Submit 提交) 	
		)
   select distinct
		     lv1_code,                               --LV1编码
		     lv1_name,                               --LV1名称
		     lv2_code,                               --LV2编码
		     lv2_name,                               --LV2名称
		     lv3_code,                               --LV3编码
		     lv3_name,                               --LV3名称
		     l1_name,                                --L1名称 
		     articulation_flag,                      --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		     industry_type,                          --产业类型（TGT 目标产业、OTHR 其它产业） 
		     analysis_flag,                          --是否分析场景（Y 是、N 否） 
			 remark,                                 --备注   
			 created_by,                             --创建人  
			 creation_date,                          --创建时间 
			 last_updated_by,                        --修改人  
			 last_update_date,                       --修改时间 
			 del_flag,                               --是否删除
		     status                                  --状态(Import 导入、Submit 提交) 
        from ict_fcst_holistic_view_temp             --ict全景图临时表
     ;  
     

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'ICT业务预测全景图'||v_tbl_name||'：结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';		          --2001表示失败
	
    --收集统计信息
    analyse fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

