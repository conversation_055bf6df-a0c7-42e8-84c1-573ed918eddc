-- ----------------------------
-- Table structure for apd_fop_ict_fcst_holistic_view_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t";
CREATE TABLE "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t" (
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv3_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv3_name" varchar(200) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "industry_type" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "analysis_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv1_code" IS 'LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv1_name" IS 'LV1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv2_code" IS 'LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv2_name" IS 'LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv3_code" IS 'LV3编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."lv3_name" IS 'LV3名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."l1_name" IS 'L1名称 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."articulation_flag" IS '勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."industry_type" IS '产业类型（TGT 目标产业、OTHR 其它产业） ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."remark" IS '备注   ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."created_by" IS '创建人  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."creation_date" IS '创建时间 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."last_updated_by" IS '修改人  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."last_update_date" IS '修改时间 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."del_flag" IS '是否删除 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."status" IS '状态（Import 导入、Submit 提交）';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t"."analysis_flag" IS '是否分析场景（Y 是、N 否）';
COMMENT ON TABLE "fin_dm_opt_fop"."apd_fop_ict_fcst_holistic_view_t" IS 'ICT业务预测全景图';

