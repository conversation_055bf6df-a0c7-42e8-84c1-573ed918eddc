-- ----------------------------
-- Function structure for f_apd_fop_spart_label_config_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_apd_fop_spart_label_config_t"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_apd_fop_spart_label_config_t"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*
创建时间：2023-2-16
创建人  ：qwx1110218
背景描述：标签配置表中新增LV1信息，LV1编码需要从维表中取，L1~L3系数取“Spart对象与L1、L2、L3关系表”中最大版本且提交的数据；调度频率与传给知识表示打标签的表一起；
参数描述：参数一(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_apd_fop_spart_label_config_t();

*/

declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_apd_fop_spart_label_config_t()';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.apd_fop_spart_label_config_t';

	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '标签配置：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 spart_label_config_tmp 临时表
  drop table if exists spart_label_config_tmp;
	create temporary table spart_label_config_tmp(
	       lv1_code          varchar(100)    -- LV1编码
       , lv1_name          varchar(200)    -- LV1名称
       , l1_name           varchar(100)    -- L1名称
       , l2_name           varchar(100)    -- L2名称
       , l3_name           varchar(100)    -- L3名称
       , l1_coefficient    numeric(38,10)  -- L1系数
       , l2_coefficient    numeric(38,10)  -- L2系数
       , l3_coefficient    numeric(38,10)  -- L3系数
       , status            varchar(50)     -- 状态（Import 导入、Submit 提交）
       , remark            varchar(500)
       , created_by        int8
       , creation_date     timestamp(6)
       , last_updated_by   int8
       , last_update_date  timestamp(6)
       , del_flag          varchar(10)
	)on commit preserve rows distribute by replication
	;

	-- 创建 spart_profiting_relation_tmp 临时表
  drop table if exists spart_profiting_relation_tmp;
	create temporary table spart_profiting_relation_tmp(
       l1_name     varchar(100)    -- L1名称
       , l2_name   varchar(100)    -- L2名称
       , l3_name   varchar(100)    -- L3名称
       , l1_coefficient  numeric(38,10)  -- L1系数
       , l2_coefficient  numeric(38,10)  -- L2系数
       , l3_coefficient  numeric(38,10)  -- L3系数
       , data_type    varchar(50) -- 数据类型（His 历史、Add 新增）
       , update_flag  varchar(10) -- 修改标识（Y 是、N 否）
       , rn int4
  )on commit preserve rows distribute by replication
  ;

  insert into spart_profiting_relation_tmp(
         l1_name
       , l2_name
       , l3_name
       , l1_coefficient
       , l2_coefficient
       , l3_coefficient
       , data_type
       , update_flag
       , rn
  )
  select distinct l1_name
       , l2_name
       , l3_name
       , l1_coefficient
       , l2_coefficient
       , l3_coefficient
       , data_type    -- 数据类型（His 历史、Add 新增）
       , update_flag  -- 修改标识（Y 是、N 否）
       , row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
    from (
          select distinct item_code
               , l1_name
               , l2_name
               , l3_name
               , l1_coefficient
               , l2_coefficient
               , l3_coefficient
               , data_type    -- 数据类型（His 历史、Add 新增）
               , update_flag  -- 修改标识（Y 是、N 否）
            from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
           where del_flag = 'N'
             and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
             and period_id = (select max(period_id) from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t where del_flag = 'N' and upper(status) = 'SUBMIT')  -- 取目标表中最大版本的
         )
  ;

  -- 从目标表取数据关联产品维，数据入到临时表
  insert into spart_label_config_tmp(
	       lv1_code           -- LV1编码
       , lv1_name           -- LV1名称
       , l1_name            -- L1名称
       , l2_name            -- L2名称
       , l3_name            -- L3名称
       , l1_coefficient     -- L1系数
       , l2_coefficient     -- L2系数
       , l3_coefficient     -- L3系数
       , status             -- 状态（Import 导入、Submit 提交）
       , remark
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
	)
	with product_info_tmp as(
    select lv1_code, lv1_name, row_number() over(partition by lv1_name order by scd_active_end_date desc) as rn
     from (
   select distinct lv1_prod_rnd_team_code as lv1_code, lv1_prod_rd_team_cn_name as lv1_name, scd_active_end_date
     from dmdim.dm_dim_product_d
    where del_flag = 'N'
      and scd_active_ind = 1
      and lv1_prod_rd_team_cn_name in(select distinct lv1_name from fin_dm_opt_fop.apd_fop_spart_label_config_t))
  ),
  spart_label_config_tmp2 as (
  select distinct lv1_name
       , l1_name
       , l2_name
       , l3_name
       , status
       , remark
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
	  from fin_dm_opt_fop.apd_fop_spart_label_config_t
	 where status = 'Submit'  -- 只取提交的
	   and del_flag = 'N'
  )
  select t2.lv1_code
       , t1.lv1_name
       , t1.l1_name
       , t1.l2_name
       , t1.l3_name
       , t3.l1_coefficient
       , t3.l2_coefficient
       , t3.l3_coefficient
       , t1.status
       , t1.remark
       , t1.created_by
       , t1.creation_date
       , t1.last_updated_by
       , t1.last_update_date
       , t1.del_flag
	  from spart_label_config_tmp2 t1
	  left join product_info_tmp t2
	    on t1.lv1_name = t2.lv1_name
	   and t2.rn = 1
	  left join spart_profiting_relation_tmp t3
	    on t1.l1_name = t3.l1_name
	   and t1.l2_name = t3.l2_name
	   and t1.l3_name = t3.l3_name
	   and t3.rn = 1
  ;

  v_dml_row_count := sql%rowcount;  -- 收集数据量

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'spart_label_config_tmp 临时表数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 清空目标表数据
  truncate table fin_dm_opt_fop.apd_fop_spart_label_config_t;

  -- 数据入到目标表
  insert into fin_dm_opt_fop.apd_fop_spart_label_config_t(
	       lv1_code           -- LV1编码
       , lv1_name           -- LV1名称
       , l1_name            -- L1名称
       , l2_name            -- L2名称
       , l3_name            -- L3名称
       , l1_coefficient     -- L1系数
       , l2_coefficient     -- L2系数
       , l3_coefficient     -- L3系数
       , status             -- 状态（Import 导入、Submit 提交）
       , remark             -- 备注
       , created_by         -- 创建人
       , creation_date      -- 创建时间
       , last_updated_by    -- 修改人
       , last_update_date   -- 修改时间
       , del_flag           -- 是否删除
	)
	-- 通过L1、L2、L3关联匹配上L1~L3系数
	select /*+set global(best_agg_plan 1) set global(query_dop 0)*/t1.lv1_code
       , t1.lv1_name
       , t1.l1_name
       , t1.l2_name
       , t1.l3_name
       , t1.l1_coefficient
       , t1.l2_coefficient
       , t1.l3_coefficient
       , t1.status
       , t1.remark
       , t1.created_by
       , t1.creation_date
       , t1.last_updated_by
       , t1.last_update_date
       , t1.del_flag
	  from spart_label_config_tmp t1
	 where l1_coefficient is not null
	    or l2_coefficient is not null
	    or l3_coefficient is not null
	union all
	-- 没有匹配上系数的，再通过L1、L2关联去匹配
	 select /*+set global(best_agg_plan 1) set global(query_dop 0)*/t1.lv1_code
       , t1.lv1_name
       , t1.l1_name
       , t1.l2_name
       , t1.l3_name
       , t3.l1_coefficient
       , t3.l2_coefficient
       , t3.l3_coefficient
       , t1.status
       , t1.remark
       , t1.created_by
       , t1.creation_date
       , t1.last_updated_by
       , t1.last_update_date
       , t1.del_flag
	  from spart_label_config_tmp t1
	  left join spart_profiting_relation_tmp t3
	    on t1.l1_name = t3.l1_name
	   and t1.l2_name = t3.l2_name
	   and t3.rn = 1
	 where t1.l1_coefficient is null
	   and t1.l2_coefficient is null
	   and t1.l3_coefficient is null
  ;


  v_dml_row_count := sql%rowcount;  -- 收集数据量

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '入到 apd_fop_spart_label_config_t 目标表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_row_count => null,
        p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';

	-- 收集统计信息
	analyse fin_dm_opt_fop.apd_fop_spart_label_config_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

