-- ----------------------------
-- Table structure for dq_exception_check_monitor_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dq_exception_check_monitor_t";
CREATE TABLE "fin_dm_opt_fop"."dq_exception_check_monitor_t" (
  "exception_check_monitor_id" numeric NOT NULL,
  "cycle_id" varchar(14) COLLATE "pg_catalog"."default" NOT NULL,
  "period" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "program_id" numeric NOT NULL,
  "task_id" numeric,
  "task_name" varchar(300) COLLATE "pg_catalog"."default",
  "task_desc" varchar(512) COLLATE "pg_catalog"."default",
  "batch_id" numeric,
  "batch_name" varchar(300) COLLATE "pg_catalog"."default",
  "batch_desc" varchar(512) COLLATE "pg_catalog"."default",
  "seq_id" numeric,
  "seq_name" varchar(300) COLLATE "pg_catalog"."default",
  "seq_desc" varchar(512) COLLATE "pg_catalog"."default",
  "job_id" numeric,
  "job_name" varchar(300) COLLATE "pg_catalog"."default",
  "job_desc" varchar(512) COLLATE "pg_catalog"."default",
  "rule_group_code" varchar(150) COLLATE "pg_catalog"."default",
  "rule_group_name" varchar(150) COLLATE "pg_catalog"."default",
  "check_rule_id" numeric,
  "version" varchar(20) COLLATE "pg_catalog"."default",
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "status" numeric NOT NULL,
  "error_log" varchar(2000) COLLATE "pg_catalog"."default",
  "param_group_id" numeric,
  "param_group_name" varchar(100) COLLATE "pg_catalog"."default",
  "root_group_code" varchar(150) COLLATE "pg_catalog"."default",
  "root_group_name" varchar(150) COLLATE "pg_catalog"."default",
  "sub_version" numeric,
  "folder_id" numeric,
  "category1" varchar(300) COLLATE "pg_catalog"."default",
  "category2" varchar(300) COLLATE "pg_catalog"."default",
  "scheduler_type" varchar(50) COLLATE "pg_catalog"."default",
  "scheduler_info" varchar(500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."exception_check_monitor_id" IS 'monitor id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."cycle_id" IS 'moia调度批次id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."period" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."program_id" IS '检查类型标识， 1 preparation check; 2 exception check';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."task_id" IS 'moia task id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."task_name" IS 'moia task name';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."batch_id" IS 'moia batch id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."batch_name" IS 'moia batch name';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."seq_id" IS 'moia seq id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."seq_name" IS 'moia seq name';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."job_id" IS 'moia job id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."job_name" IS 'moia job name';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."rule_group_code" IS '规则组编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."rule_group_name" IS '规则组名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."check_rule_id" IS 'check rule id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."version" IS 'check rule id 对应的版本';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."start_time" IS '开始时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."end_time" IS '结束时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."status" IS '状态， 0表示"Error"；1表示"Success"； 2表示"Warning";';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."error_log" IS '错误日志， 记录执行规则出错时的错误信息。';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."param_group_id" IS '参数组ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."param_group_name" IS '参数组名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."root_group_code" IS '规则根节点code';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."root_group_name" IS '规则组根节点名字';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."sub_version" IS '规则子版本号。moyia或online的备份数据用sequence生成';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."scheduler_type" IS '调度类型， 如：moia， online';
COMMENT ON COLUMN "fin_dm_opt_fop"."dq_exception_check_monitor_t"."scheduler_info" IS '调度器信息';

