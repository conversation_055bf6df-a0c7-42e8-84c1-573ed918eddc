-- ----------------------------
-- Function structure for f_kr_cpf_lv1_aggr_act_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_kr_cpf_lv1_aggr_act_t"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_kr_cpf_lv1_aggr_act_t"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-11-28
创建人  ：鲁广武
背景描述：LV1-LV2-L1设备收入与制毛率历史数,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_kr_cpf_lv1_aggr_act_t()

*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_kr_cpf_lv1_aggr_act_t';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t';
	v_dml_row_count number default 0 ;
    

begin
	x_success_flag := '1';        --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'LV1-LV2-L1设备收入与制毛率历史数'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

		--判断period_id是否是当前年月，然后delete数据
    if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t_m where period_id = substring(regexp_replace(current_date,'-',''),1,6))
     then 
     
		--支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t where period_id = substring(regexp_replace(current_date,'-',''),1,6);
		
		--插入目标表数据
		insert into fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t  --LV1-LV2-L1设备收入与制毛率历史数
		(period_id					--会计期
		,target_period              --目标时点
		,bg_code                    --BG编码
		,bg_name                    --BG名称
		,oversea_desc               --区域
		,lv1_code                   --重量级团队LV1编码
		,lv1_name                   --重量级团队LV1描述
		,lv2_code                   --重量级团队LV2编码
		,lv2_name                   --重量级团队LV2名称
		,l1_name                    --L1名称
		,currency                   --币种
		,equip_rev_after_act        --对价后设备收入额
		,mgp_rate_after_act         --对价后制毛率
		,remark                     --备注
		,created_by                 --创建人
		,creation_date              --创建时间
		,last_updated_by            --修改人
		,last_update_date           --修改时间
		,del_flag                   --是否删除
		,aggregate_flag             --汇聚标志（N表示直接预测全球的，Y表示由国内和海外汇总得到的全球的）；同步的时候只取N的
		)
		select 
		 period_id					--会计期
		,target_period              --目标时点
		,bg_code                    --BG编码
		,bg_name                    --BG名称
		,oversea_desc               --区域
		,lv1_code                   --重量级团队LV1编码
		,lv1_name                   --重量级团队LV1描述
		,lv2_code                   --重量级团队LV2编码
		,lv2_name                   --重量级团队LV2名称
		,l1_name                    --L1名称
		,currency                   --币种
		,equip_rev_after_act        --对价后设备收入额
		,mgp_rate_after_act         --对价后制毛率
		,'' as remark                                  --备注
		,-1 as created_by                              --创建人
		,current_timestamp as creation_date            --创建时间
		,-1 as last_updated_by                         --修改人
		,current_timestamp as last_update_date         --修改时间
		,'N' as del_flag                               --是否删除
		,aggregate_flag
	from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t_m              --LV1-LV2-L1设备收入与制毛率当前月数据
 where period_id = substring(regexp_replace(current_date,'-',''),1,6)       --取当前月份数据
		;
		
	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'LV1-LV2-L1设备收入与制毛率历史数'||v_tbl_name||':取当前月份数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

else
		--支持重跑，清除目标表中m表会计期的数据
		delete from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t where period_id = (select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t_m);
		
		--插入目标表数据
		insert into fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t  --LV1-LV2-L1设备收入与制毛率历史数
		(period_id					--会计期
		,target_period              --目标时点
		,bg_code                    --BG编码
		,bg_name                    --BG名称
		,oversea_desc               --区域
		,lv1_code                   --重量级团队LV1编码
		,lv1_name                   --重量级团队LV1描述
		,lv2_code                   --重量级团队LV2编码
		,lv2_name                   --重量级团队LV2名称
		,l1_name                    --L1名称
		,currency                   --币种
		,equip_rev_after_act        --对价后设备收入额
		,mgp_rate_after_act         --对价后制毛率
		,remark                     --备注
		,created_by                 --创建人
		,creation_date              --创建时间
		,last_updated_by            --修改人
		,last_update_date           --修改时间
		,del_flag                   --是否删除
		,aggregate_flag             --汇聚标志（N表示直接预测全球的，Y表示由国内和海外汇总得到的全球的）；同步的时候只取N的
		)
		select 
		 period_id					--会计期
		,target_period              --目标时点
		,bg_code                    --BG编码
		,bg_name                    --BG名称
		,oversea_desc               --区域
		,lv1_code                   --重量级团队LV1编码
		,lv1_name                   --重量级团队LV1描述
		,lv2_code                   --重量级团队LV2编码
		,lv2_name                   --重量级团队LV2名称
		,l1_name                    --L1名称
		,currency                   --币种
		,equip_rev_after_act        --对价后设备收入额
		,mgp_rate_after_act         --对价后制毛率
		,'' as remark                                  --备注
		,-1 as created_by                              --创建人
		,current_timestamp as creation_date            --创建时间
		,-1 as last_updated_by                         --修改人
		,current_timestamp as last_update_date         --修改时间
		,'N' as del_flag                               --是否删除
		,aggregate_flag
	from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t_m              --LV1-LV2-L1设备收入与制毛率当前月数据
 where period_id = (select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t_m)       --取m表的会计期数据
		;
		
	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'LV1-LV2-L1设备收入与制毛率历史数'||v_tbl_name||':取m表的会计期数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

   end if 
	  ;

exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_fop.kr_cpf_lv1_aggr_act_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

