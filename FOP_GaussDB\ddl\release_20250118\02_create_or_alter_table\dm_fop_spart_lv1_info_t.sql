-- ----------------------------
-- Table structure for dm_fop_spart_lv1_info_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t" (
  "period_id" numeric,
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(20) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_cons_after_amt" numeric(38,10),
  "equip_cost_cons_after_amt" numeric(38,10),
  "mgp_ratio" numeric(38,10),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."equip_rev_cons_after_amt" IS '设备收入额（对价后）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."equip_cost_cons_after_amt" IS '设备成本额（对价后）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."mgp_ratio" IS '制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_spart_lv1_info_t" IS '重量级团队LV1层级数据表';

