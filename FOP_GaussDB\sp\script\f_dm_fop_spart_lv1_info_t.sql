-- ----------------------------
-- Function structure for f_dm_fop_spart_lv1_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_lv1_info_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_lv1_info_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-10-13
创建人  ：鲁广武
更新时间：2023-02-08
更新人  ：鲁广武  lwx1186472
背景描述：重量级团队LV1层级数据表(提供给知识表示),然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_lv1_info_t('202302_V1')
修改记录：2023-02-08 lwx1186472   新增版本参数
          传入版本参数，有值：传入值格式：f_dm_fop_spart_lv1_info_t('202302_V1') 
          传入版本参数，无值：传入值格式：f_dm_fop_spart_lv1_info_t()  
          2023-04-19 lwx1186472   修改取最大版本逻辑 
          2023-08-01 lwx1186472	  202309版本区域增加'国内'、'海外'逻辑 
          2024-09-05 qwx1110218   202409版本区域新增：'其他'；全球=国内+海外+其他；
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_lv1_info_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_lv1_info_t';
	v_tbl_name2 varchar(50) := 'fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t';
	v_max_version_code varchar(50);  -- 宽表的最大版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;

	
begin
	x_success_flag := '1';           --1表示成功	


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '重量级团队LV1层级数据表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的版本编码:'||p_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    	  


--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
  ---判断p_version_code在宽表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t where version_code = p_version_code)
   then     
     ---删除重跑目标表中已有传入参数的数据
	 delete from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t where version_code = p_version_code;
		
  -- his_t目标表取宽表传入参数的数据	
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t 
     (
	  version_code,                           --版本
      period_id,								 ---会计期
      bg_code,                                ---BG编码
      bg_name,                                ---BG名称
      oversea_desc,                           ---区域
      lv1_code,                               ---重量级团队LV1编码
      lv1_name,                               ---重量级团队LV1描述
      equip_rev_cons_after_amt,               ---设备收入额（对价后）
      equip_cost_cons_after_amt,              ---设备成本额（对价后）
      mgp_ratio,                              ---制毛率
      currency,                               ---币种
	  remark,                                 ---备注
	  created_by,                             ---创建人
	  creation_date,                          ---创建时间
	  last_updated_by,                        ---修改人
	  last_update_date,                       ---修改时间
	  del_flag                                ---是否删除	  
     )	

	---币种合并
	with currency_union as (
	select
	    t1.version_code,                                        --版本
		t1.period_id,											---会计期
		t1.bg_code,                                             ---BG编码
		t1.bg_name,                                             ---BG名称
		(case when t1.oversea_flag = 'Y' then '海外'
		      when t1.oversea_flag = 'N' then '国内'
		      when t1.oversea_flag = 'OTH' then '其他'
		 end) as oversea_desc,                                    ---区域
		t1.lv1_prod_rnd_team_code as lv1_code,                  ---重量级团队LV1编码
		t1.lv1_prod_rd_team_cn_name as lv1_name,                ---重量级团队LV1描述
		t1.equip_rev_rmb_amt as equip_rev_cons_after_amt,       ---设备收入额（对价后）
		t1.equip_cost_rmb_amt as equip_cost_cons_after_amt,     ---设备成本额（对价后）
		'CNY' as currency,										                  ---币种
		t1.del_flag												                      ---是否删除
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1               ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
   where t1.version_code = p_version_code                   -- 取宽表中传入的版本编码
     and t1.del_flag = 'N'                                  ---是否删除
	 and t1.industry_type = 'OTHR'                            ---产业类型（TGT 目标产业、OTHR 其它产业）
	 and t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t'     ---来源表

	union all                                                  ----合并币种

	select
	    t1.version_code,                           --版本
		t1.period_id,											                      ---会计期
		t1.bg_code,                                             ---BG编码
		t1.bg_name,                                             ---BG名称
		(case when t1.oversea_flag = 'Y' then '海外'
		      when t1.oversea_flag = 'N' then '国内'
		      when t1.oversea_flag = 'OTH' then '其他'
		 end) as oversea_desc,                                    ---区域
		t1.lv1_prod_rnd_team_code as lv1_code,                  ---重量级团队LV1编码
		t1.lv1_prod_rd_team_cn_name as lv1_name,                ---重量级团队LV1描述
		t1.equip_rev_usd_amt as equip_rev_cons_after_amt,       ---设备收入额（对价后）
		t1.equip_cost_usd_amt as equip_cost_cons_after_amt,     ---设备成本额（对价后）
		'USD' as currency,                                      ---币种
		t1.del_flag												                      ---是否删除
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1 			        ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
   where t1.version_code = p_version_code                   -- 取宽表中传入的版本编码
     and t1.del_flag = 'N'                                  ---是否删除
	 and t1.industry_type = 'OTHR'                            ---产业类型（TGT 目标产业、OTHR 其它产业）
	 and t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t'     ---来源表
		),

	---转换'全球'且计算出'全球'相应指标（设备收入 = 收入金额；制毛率 = 1 - 成本金额/收入金额）
	---202309版本区域增加'国内'、'海外'逻辑 
		oversea_desc_union as (
	select
	    t4.version_code,                           --版本
		t4.period_id,                                                     		   ---会计期
		t4.bg_code,                                                              ---BG编码
		t4.bg_name,                                                              ---BG名称
		t4.oversea_desc,                                                  ---区域
		t4.lv1_code,                                                             ---重量级团队LV1编码
		t4.lv1_name,                                                             ---重量级团队LV1描述
		sum(nvl(t4.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,     ---"全球"设备收入额（对价后）
		sum(nvl(t4.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,   ---"全球"设备成本额（对价后）
		case when sum(nvl(t4.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then -999999
		else 1-sum(nvl(t4.equip_cost_cons_after_amt,0))/sum(nvl(t4.equip_rev_cons_after_amt,0)) end as mgp_ratio,  ----"全球"制毛率=1-成本金额/收入金额
		t4.currency                                                              ---币种
	from currency_union t4
   where t4.oversea_desc is not null    --国内、海外需要排除空值
	group by
		t4.version_code,
		t4.period_id,
		t4.bg_code,
		t4.bg_name,
		t4.oversea_desc,
		t4.lv1_code,
		t4.lv1_name,
		t4.currency
		
union all		

	select
	    t4.version_code,                           --版本
		t4.period_id,                                                     		   ---会计期
		t4.bg_code,                                                              ---BG编码
		t4.bg_name,                                                              ---BG名称
		'全球' as oversea_desc,                                                  ---区域
		t4.lv1_code,                                                             ---重量级团队LV1编码
		t4.lv1_name,                                                             ---重量级团队LV1描述
		sum(nvl(t4.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,     ---"全球"设备收入额（对价后）
		sum(nvl(t4.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,   ---"全球"设备成本额（对价后）
		case when sum(nvl(t4.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then -999999
		else 1-sum(nvl(t4.equip_cost_cons_after_amt,0))/sum(nvl(t4.equip_rev_cons_after_amt,0)) end as mgp_ratio,  ----"全球"制毛率=1-成本金额/收入金额
		t4.currency                                                              ---币种
	from currency_union t4
	group by
		t4.version_code,
		t4.period_id,
		t4.bg_code,
		t4.bg_name,
		t4.lv1_code,
		t4.lv1_name,
		t4.currency
		)

	select
	    t5.version_code,         --版本
		t5.period_id,		                                ---会计期
		t5.bg_code,						                          ---BG编码
		t5.bg_name,                                     ---BG名称
		t5.oversea_desc,                                ---区域
		t5.lv1_code,                                    ---重量级团队LV1编码
		t5.lv1_name,                                    ---重量级团队LV1描述
		t5.equip_rev_cons_after_amt,                    ---"全球"(政企、运营商网络)设备收入额（对价后）
		t5.equip_cost_cons_after_amt,                   ---"全球"(政企、运营商网络)设备成本额（对价后）
		t5.mgp_ratio,                                   ---"全球"(政企、运营商网络)制毛率
		t5.currency,					                          ---币种
		'' as remark,							---备注
		-1 as created_by,                       ---创建人
		current_timestamp as creation_date,     ---创建时间
		-1 as last_updated_by,                  ---修改人
		current_timestamp as last_update_date,  ---修改时间
		'N' as del_flag                         ---是否删除			
	from oversea_desc_union t5

	union all                       ---合并bg_name(并入集团)

	select
	    t6.version_code,                --版本
		t6.period_id,															                              ---会计期
		'PROD0002' as bg_code,                                                  ---BG编码  -- 20230619 update by qwx1110218
		'ICT' as bg_name,                                                       ---BG名称  -- 20230619 update by qwx1110218
		t6.oversea_desc,                                                        ---区域
		t6.lv1_code,                                                            ---重量级团队LV1编码
		t6.lv1_name,                                                            ---重量级团队LV1描述
		sum(nvl(t6.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,    ---"全球"(集团)设备收入额（对价后）
		sum(nvl(t6.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,  ---"全球"(集团)设备成本额（对价后）
		case when sum(nvl(t6.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t6.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t6.equip_rev_cons_after_amt,0)) = 0 then -999999
		else (1 - sum(nvl(t6.equip_cost_cons_after_amt,0))/sum(nvl(t6.equip_rev_cons_after_amt,0))) end as mgp_ratio,  ----"全球"(集团)制毛率=1-成本金额/收入金额
		t6.currency,															                              ---币种
		'' as remark,							---备注
		-1 as created_by,                       ---创建人
		current_timestamp as creation_date,     ---创建时间
		-1 as last_updated_by,                  ---修改人
		current_timestamp as last_update_date,  ---修改时间
		'N' as del_flag                         ---是否删除			
	from oversea_desc_union t6
	group by
	    t6.version_code,
		t6.period_id,
		t6.oversea_desc,
		t6.lv1_code,
		t6.lv1_name,
		t6.currency
		;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '重量级团队LV1层级数据表'||v_tbl_name2||',传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

     ---删除重跑目标表中的数据，只留一个版本的数据
	 truncate table fin_dm_opt_fop.dm_fop_spart_lv1_info_t;

  -- 目标表取his_t表传入参数的数据
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_t 
     (
      period_id,								 ---会计期
      bg_code,                                ---BG编码
      bg_name,                                ---BG名称
      oversea_desc,                           ---区域
      lv1_code,                               ---重量级团队LV1编码
      lv1_name,                               ---重量级团队LV1描述
      equip_rev_cons_after_amt,               ---设备收入额（对价后）
      equip_cost_cons_after_amt,              ---设备成本额（对价后）
      mgp_ratio,                              ---制毛率
      currency,                               ---币种
	  remark,                                 ---备注
	  created_by,                             ---创建人
	  creation_date,                          ---创建时间
	  last_updated_by,                        ---修改人
	  last_update_date,                       ---修改时间
	  del_flag                                ---是否删除	  
     )	
    select 
          period_id,						      ---会计期
          bg_code,                                ---BG编码
          bg_name,                                ---BG名称
          oversea_desc,                           ---区域
          lv1_code,                               ---重量级团队LV1编码
          lv1_name,                               ---重量级团队LV1描述
          equip_rev_cons_after_amt,               ---设备收入额（对价后）
          equip_cost_cons_after_amt,              ---设备成本额（对价后）
          mgp_ratio,                              ---制毛率
          currency,                               ---币种
		  '' as remark,							  ---备注
		  -1 as created_by,                       ---创建人
		  current_timestamp as creation_date,     ---创建时间
		  -1 as last_updated_by,                  ---修改人
		  current_timestamp as last_update_date,  ---修改时间
		  'N' as del_flag                         ---是否删除		
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t
     where version_code = p_version_code      --his_t表传入的版本号	  
    ;	

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '重量级团队LV1层级数据表'||v_tbl_name||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  else
		
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => '宽表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;



--当p_version_code无值时，取最大的版本号
else 

    --取宽表最大版本号
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code 
	  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t)
     group by substr(version_code,1,6);  -- 取宽表最大版本号		

     ---删除重跑目标表中最大的版本号的数据
	 delete from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t where version_code = v_max_version_code;
		
  -- his_t目标表取宽表最大版本数据	
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t 
     (
	  version_code,                           --版本
      period_id,								 ---会计期
      bg_code,                                ---BG编码
      bg_name,                                ---BG名称
      oversea_desc,                           ---区域
      lv1_code,                               ---重量级团队LV1编码
      lv1_name,                               ---重量级团队LV1描述
      equip_rev_cons_after_amt,               ---设备收入额（对价后）
      equip_cost_cons_after_amt,              ---设备成本额（对价后）
      mgp_ratio,                              ---制毛率
      currency,                               ---币种
	  remark,                                 ---备注
	  created_by,                             ---创建人
	  creation_date,                          ---创建时间
	  last_updated_by,                        ---修改人
	  last_update_date,                       ---修改时间
	  del_flag                                ---是否删除	  
     )	

	---币种合并
	with currency_union as (
	select
	    t1.version_code,                                        --版本
		t1.period_id,											---会计期
		t1.bg_code,                                             ---BG编码
		t1.bg_name,                                             ---BG名称
		(case when t1.oversea_flag = 'Y' then '海外'
		      when t1.oversea_flag = 'N' then '国内'
		      when t1.oversea_flag = 'OTH' then '其他'
		 end) as oversea_desc,                                    ---区域
		t1.lv1_prod_rnd_team_code as lv1_code,                  ---重量级团队LV1编码
		t1.lv1_prod_rd_team_cn_name as lv1_name,                ---重量级团队LV1描述
		t1.equip_rev_rmb_amt as equip_rev_cons_after_amt,       ---设备收入额（对价后）
		t1.equip_cost_rmb_amt as equip_cost_cons_after_amt,     ---设备成本额（对价后）
		'CNY' as currency,										                  ---币种
		t1.del_flag												                      ---是否删除
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1               ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
   where t1.version_code = v_max_version_code                   -- 取宽表中最大的版本编码
     and t1.del_flag = 'N'                                  ---是否删除
	 and t1.industry_type = 'OTHR'                            ---产业类型（TGT 目标产业、OTHR 其它产业）
	 and t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t'     ---来源表

	union all                                                  ----合并币种

	select
	    t1.version_code,                           --版本
		t1.period_id,											                      ---会计期
		t1.bg_code,                                             ---BG编码
		t1.bg_name,                                             ---BG名称
		(case when t1.oversea_flag = 'Y' then '海外'
		      when t1.oversea_flag = 'N' then '国内'
		      when t1.oversea_flag = 'OTH' then '其他'
		 end) as oversea_desc,                                    ---区域
		t1.lv1_prod_rnd_team_code as lv1_code,                  ---重量级团队LV1编码
		t1.lv1_prod_rd_team_cn_name as lv1_name,                ---重量级团队LV1描述
		t1.equip_rev_usd_amt as equip_rev_cons_after_amt,       ---设备收入额（对价后）
		t1.equip_cost_usd_amt as equip_cost_cons_after_amt,     ---设备成本额（对价后）
		'USD' as currency,                                      ---币种
		t1.del_flag												                      ---是否删除
	from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t t1 			        ---SPART明细+L2~L3/L1~L3系数+type类型(宽表)
   where t1.version_code = v_max_version_code                   -- 取宽表最大的版本编码
     and t1.del_flag = 'N'                                  ---是否删除
	 and t1.industry_type = 'OTHR'                            ---产业类型（TGT 目标产业、OTHR 其它产业）
	 and t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t'     ---来源表
		),

	---转换'全球'且计算出'全球'相应指标（设备收入 = 收入金额；制毛率 = 1 - 成本金额/收入金额）
	---202309版本区域增加'国内'、'海外'逻辑 
		oversea_desc_union as (
	select
	    t4.version_code,                           --版本
		t4.period_id,                                                     		   ---会计期
		t4.bg_code,                                                              ---BG编码
		t4.bg_name,                                                              ---BG名称
		t4.oversea_desc,                                                  ---区域
		t4.lv1_code,                                                             ---重量级团队LV1编码
		t4.lv1_name,                                                             ---重量级团队LV1描述
		sum(nvl(t4.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,     ---"全球"设备收入额（对价后）
		sum(nvl(t4.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,   ---"全球"设备成本额（对价后）
		case when sum(nvl(t4.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then -999999
		else 1-sum(nvl(t4.equip_cost_cons_after_amt,0))/sum(nvl(t4.equip_rev_cons_after_amt,0)) end as mgp_ratio,  ----"全球"制毛率=1-成本金额/收入金额
		t4.currency                                                              ---币种
	from currency_union t4
   where t4.oversea_desc is not null    --国内、海外需要排除空值	
	group by
		t4.version_code,
		t4.period_id,
		t4.bg_code,
		t4.bg_name,
		t4.oversea_desc,
		t4.lv1_code,
		t4.lv1_name,
		t4.currency
		
union all	
		
	select
	    t4.version_code,                           --版本
		t4.period_id,                                                     		   ---会计期
		t4.bg_code,                                                              ---BG编码
		t4.bg_name,                                                              ---BG名称
		'全球' as oversea_desc,                                                  ---区域
		t4.lv1_code,                                                             ---重量级团队LV1编码
		t4.lv1_name,                                                             ---重量级团队LV1描述
		sum(nvl(t4.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,     ---"全球"设备收入额（对价后）
		sum(nvl(t4.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,   ---"全球"设备成本额（对价后）
		case when sum(nvl(t4.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t4.equip_rev_cons_after_amt,0)) = 0 then -999999
		else 1-sum(nvl(t4.equip_cost_cons_after_amt,0))/sum(nvl(t4.equip_rev_cons_after_amt,0)) end as mgp_ratio,  ----"全球"制毛率=1-成本金额/收入金额
		t4.currency                                                              ---币种
	from currency_union t4
	group by
		t4.version_code,
		t4.period_id,
		t4.bg_code,
		t4.bg_name,
		t4.lv1_code,
		t4.lv1_name,
		t4.currency
		)

	select
	    t5.version_code,         --版本
		t5.period_id,		                                ---会计期
		t5.bg_code,						                          ---BG编码
		t5.bg_name,                                     ---BG名称
		t5.oversea_desc,                                ---区域
		t5.lv1_code,                                    ---重量级团队LV1编码
		t5.lv1_name,                                    ---重量级团队LV1描述
		t5.equip_rev_cons_after_amt,                    ---"全球"(政企、运营商网络)设备收入额（对价后）
		t5.equip_cost_cons_after_amt,                   ---"全球"(政企、运营商网络)设备成本额（对价后）
		t5.mgp_ratio,                                   ---"全球"(政企、运营商网络)制毛率
		t5.currency,					                          ---币种
		'' as remark,							---备注
		-1 as created_by,                       ---创建人
		current_timestamp as creation_date,     ---创建时间
		-1 as last_updated_by,                  ---修改人
		current_timestamp as last_update_date,  ---修改时间
		'N' as del_flag                         ---是否删除			
	from oversea_desc_union t5

	union all                       ---合并bg_name(并入集团)

	select
	    t6.version_code,                --版本
		t6.period_id,															                              ---会计期
		'PROD0002' as bg_code,                                                  ---BG编码  -- 20230619 update by qwx1110218
		'ICT' as bg_name,                                                       ---BG名称  -- 20230619 update by qwx1110218
		t6.oversea_desc,                                                        ---区域
		t6.lv1_code,                                                            ---重量级团队LV1编码
		t6.lv1_name,                                                            ---重量级团队LV1描述
		sum(nvl(t6.equip_rev_cons_after_amt,0)) as equip_rev_cons_after_amt,    ---"全球"(集团)设备收入额（对价后）
		sum(nvl(t6.equip_cost_cons_after_amt,0)) as equip_cost_cons_after_amt,  ---"全球"(集团)设备成本额（对价后）
		case when sum(nvl(t6.equip_cost_cons_after_amt,0)) = 0 and sum(nvl(t6.equip_rev_cons_after_amt,0)) = 0 then 0
			 when sum(nvl(t6.equip_rev_cons_after_amt,0)) = 0 then -999999
		else (1 - sum(nvl(t6.equip_cost_cons_after_amt,0))/sum(nvl(t6.equip_rev_cons_after_amt,0))) end as mgp_ratio,  ----"全球"(集团)制毛率=1-成本金额/收入金额
		t6.currency,															                              ---币种
		'' as remark,							---备注
		-1 as created_by,                       ---创建人
		current_timestamp as creation_date,     ---创建时间
		-1 as last_updated_by,                  ---修改人
		current_timestamp as last_update_date,  ---修改时间
		'N' as del_flag                         ---是否删除			
	from oversea_desc_union t6
	group by
	    t6.version_code,
		t6.period_id,
		t6.oversea_desc,
		t6.lv1_code,
		t6.lv1_name,
		t6.currency
		;

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '重量级团队LV1层级数据表'||v_tbl_name2||',宽表中最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||'',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

     --从his_t目标表中取最大版本号
     ---删除重跑目标表中的数据，只留一个版本的数据
	 truncate table fin_dm_opt_fop.dm_fop_spart_lv1_info_t;
	
  -- 目标表取his_t表最大版本数据
     insert into fin_dm_opt_fop.dm_fop_spart_lv1_info_t 
     (
      period_id,								 ---会计期
      bg_code,                                ---BG编码
      bg_name,                                ---BG名称
      oversea_desc,                           ---区域
      lv1_code,                               ---重量级团队LV1编码
      lv1_name,                               ---重量级团队LV1描述
      equip_rev_cons_after_amt,               ---设备收入额（对价后）
      equip_cost_cons_after_amt,              ---设备成本额（对价后）
      mgp_ratio,                              ---制毛率
      currency,                               ---币种
	  remark,                                 ---备注
	  created_by,                             ---创建人
	  creation_date,                          ---创建时间
	  last_updated_by,                        ---修改人
	  last_update_date,                       ---修改时间
	  del_flag                                ---是否删除	  
     )	
    select 
          period_id,						      ---会计期
          bg_code,                                ---BG编码
          bg_name,                                ---BG名称
          oversea_desc,                           ---区域
          lv1_code,                               ---重量级团队LV1编码
          lv1_name,                               ---重量级团队LV1描述
          equip_rev_cons_after_amt,               ---设备收入额（对价后）
          equip_cost_cons_after_amt,              ---设备成本额（对价后）
          mgp_ratio,                              ---制毛率
          currency,                               ---币种
		  '' as remark,							  ---备注
		  -1 as created_by,                       ---创建人
		  current_timestamp as creation_date,     ---创建时间
		  -1 as last_updated_by,                  ---修改人
		  current_timestamp as last_update_date,  ---修改时间
		  'N' as del_flag                         ---是否删除		
	 from fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t
     where version_code = v_max_version_code      --his_t表最大版本号	  
    ;	

	v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '重量级团队LV1层级数据表'||v_tbl_name||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

end if
   ;


exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';	       --2001表示失败
	
    --收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_lv1_info_his_t;
    analyse fin_dm_opt_fop.dm_fop_spart_lv1_info_t;	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

