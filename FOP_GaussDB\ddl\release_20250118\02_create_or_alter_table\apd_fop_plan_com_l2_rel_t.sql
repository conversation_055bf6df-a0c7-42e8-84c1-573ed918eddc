-- ----------------------------
-- Table structure for apd_fop_plan_com_l2_rel_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t";
CREATE TABLE "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t" (
  "plan_com_lv1" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(600) COLLATE "pg_catalog"."default",
  "busi_lv4" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_coefficient" numeric(38,6),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(200) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."busi_lv4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."l2_coefficient" IS 'L2系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."status" IS '状态（Import 导入、Submit 提交）';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."lv1_code" IS 'LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t"."lv1_name" IS 'LV1名称';
COMMENT ON TABLE "fin_dm_opt_fop"."apd_fop_plan_com_l2_rel_t" IS 'L2与计委包关系表';

