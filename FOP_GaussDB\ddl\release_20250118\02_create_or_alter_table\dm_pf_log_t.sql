-- ----------------------------
-- Table structure for dm_pf_log_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_pf_log_t";
CREATE TABLE "fin_dm_opt_fop"."dm_pf_log_t" (
  "log_id" numeric NOT NULL,
  "version_id" numeric,
  "sp_name" varchar(500) COLLATE "pg_catalog"."default",
  "para_list" varchar(4000) COLLATE "pg_catalog"."default",
  "step_num" numeric,
  "cal_log_desc" varchar(500) COLLATE "pg_catalog"."default",
  "formula_sql_txt" text COLLATE "pg_catalog"."default",
  "dml_row_count" numeric,
  "result_status" varchar(20) COLLATE "pg_catalog"."default",
  "errbuf" varchar(4000) COLLATE "pg_catalog"."default",
  "created_by" numeric,
  "creation_date" timestamp(6)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."log_id" IS '主键id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."version_id" IS '版本id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."sp_name" IS '过程名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."para_list" IS '参数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."step_num" IS '计算步骤';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."cal_log_desc" IS '计算日志描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."formula_sql_txt" IS '公式计算拼接生成sql';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."dml_row_count" IS '影响行数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."result_status" IS '执行状态：1-success/2001-error';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."errbuf" IS '结果信息';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_pf_log_t"."creation_date" IS '创建时间';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_pf_log_t" IS '定价预测日志表';

