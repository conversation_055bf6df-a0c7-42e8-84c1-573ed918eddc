-- ----------------------------
-- Table structure for kr_cpf_lv1_aggr_act_t_m
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m";
CREATE TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m" (
  "period_id" numeric,
  "target_period" varchar(200) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_after_act" numeric(38,10),
  "mgp_rate_after_act" numeric(38,10),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8 DEFAULT (-1),
  "creation_date" timestamp(6) DEFAULT pg_systimestamp(),
  "last_updated_by" int8 DEFAULT (-1),
  "last_update_date" timestamp(6) DEFAULT pg_systimestamp(),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'N'::character varying,
  "aggregate_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."target_period" IS '目标时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."lv2_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."lv2_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."equip_rev_after_act" IS '对价后设备收入额';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."mgp_rate_after_act" IS '对价后制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m"."aggregate_flag" IS '汇聚标志（N表示直接预测全球的，Y表示由国内和海外汇总得到的全球的）';
COMMENT ON TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_act_t_m" IS 'LV1-LV2-L1设备收入与制毛率当前月数据';

