-- ----------------------------
-- Table structure for fop_mr_dm_ps_for_opt_pl_dtl_v
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_mr_dm_ps_for_opt_pl_dtl_v";
CREATE TABLE "fin_dm_opt_fop"."fop_mr_dm_ps_for_opt_pl_dtl_v" (
  "version_id" varchar(50) COLLATE "pg_catalog"."default",
  "period_id" numeric,
  "report_item_id" numeric,
  "geo_pc_key" numeric,
  "major_prod_key" numeric,
  "data_category_id" numeric,
  "report_scope_code" varchar(50) COLLATE "pg_catalog"."default",
  "rmb_fact_ex_rate_ptd_amt" numeric(38,10),
  "usd_fact_ex_rate_ptd_amt" numeric(38,10)
)
;

