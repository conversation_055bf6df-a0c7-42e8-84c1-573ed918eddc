CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING,OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：LV1-LV2-设备收入与制毛率预测数,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
		  参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T();
*/
 
 DECLARE
	V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T()';
	V_TBL_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T';
	V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月_001
	V_VERSION_MONTH VARCHAR(50);  -- 目标表的当前版本年月，格式：年月
	V_STEP_NUM   NUMERIC; --步骤号
	V_DML_ROW_COUNT  NUMBER DEFAULT 0 ;


BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';                                 --1表示成功
	
	-- 如果是传 VERSION_CODE 调函数取 传入的 P_VERSION_CODE ，如果是自动调度的 则从版本表取最新版本号
	 IF P_VERSION_CODE IS NOT NULL 
	 THEN
	 SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
	 ELSE 
	   -- 从版本表取最新版本号	    
	 SELECT VERSION_CODE INTO V_VERSION_CODE
	 FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
	 WHERE STEP = 1
	 ORDER BY LAST_UPDATE_DATE DESC
	 LIMIT 1
	 ;
	 END IF 
	 ;
 
	
	 --1.开始日志
  V_STEP_NUM := 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => 'LV1-LV2-设备收入与制毛率预测数'||V_TBL_NAME||',版本编码:'||V_VERSION_CODE||'，版本年月:'||V_VERSION_MONTH||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
	  
      
	    -- 取聚合后的LV1\LV2表对价后收入和对价后的制毛率，合成一张表
	  DROP TABLE IF EXISTS LV1_LV2_FCST_TMP;
	  CREATE TEMPORARY TABLE  LV1_LV2_FCST_TMP
		     AS
         SELECT PERIOD_ID
               ,PHASE_DATE
               ,TARGET_PERIOD
               ,FCST_TYPE
               ,BG_CODE
               ,BG_NAME
               ,OVERSEA_CODE
               ,OVERSEA_DESC
               ,LV1_CODE
               ,LV1_NAME
               ,LV2_CODE
               ,LV2_NAME
               ,CURRENCY
               ,EQUIP_REV_CONS_AFTER_AMT 
               ,MGP_RATIO_AFTER
		FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_ARTICULATED_AGGR_T
		WHERE VERSION_CODE = V_VERSION_CODE
		UNION ALL
		SELECT PERIOD_ID
               ,PHASE_DATE
               ,TARGET_PERIOD
               ,FCST_TYPE
               ,BG_CODE
               ,BG_NAME
               ,OVERSEA_CODE
               ,OVERSEA_DESC
               ,LV1_CODE
               ,LV1_NAME
               ,NULL AS LV2_CODE
               ,NULL AS LV2_NAME
               ,CURRENCY
               ,EQUIP_REV_CONS_AFTER_AMT
               ,MGP_RATIO_AFTER
		FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_ARTICULATED_AGGR_T
		WHERE VERSION_CODE = V_VERSION_CODE
		 ;
		
		  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
		  
		  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '取聚合后的LV1\LV2表对价后收入和对价后的制毛率合成一张表，LV1_LV2_FCST_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		
		DELETE FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T WHERE PERIOD_ID = CAST(SUBSTR(V_VERSION_CODE,1,6) AS INT);
		
		INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T (
		       PERIOD_ID
              ,PHASE_DATE
              ,TARGET_PERIOD
              ,FCST_TYPE
              ,BG_CODE
              ,BG_NAME
              ,OVERSEA_CODE
              ,OVERSEA_DESC
              ,LV1_CODE
              ,LV1_NAME
              ,LV2_CODE
              ,LV2_NAME
              ,CURRENCY
              ,EQUIP_REV_AFTER_FCST
              ,MGP_RATE_AFTER_FCST
              ,REMARK
              ,CREATED_BY
              ,CREATION_DATE
              ,LAST_UPDATED_BY
              ,LAST_UPDATE_DATE
              ,DEL_FLAG
		      )
			   SELECT PERIOD_ID
               ,PHASE_DATE
               ,TARGET_PERIOD
               ,FCST_TYPE
               ,BG_CODE
               ,BG_NAME
               ,OVERSEA_CODE
               ,OVERSEA_DESC
               ,LV1_CODE
               ,LV1_NAME
               ,LV2_CODE
               ,LV2_NAME
               ,CURRENCY
               ,EQUIP_REV_CONS_AFTER_AMT  AS EQUIP_REV_AFTER_FCST
               ,MGP_RATIO_AFTER           AS MGP_RATE_AFTER_FCST
			   ,'' AS REMARK
	           ,'-1' AS CREATED_BY
	           ,CURRENT_TIMESTAMP AS CREATION_DATE
	           ,'-1' AS LAST_UPDATED_BY
	           ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
	           ,'N' AS DEL_FLAG
		   FROM LV1_LV2_FCST_TMP
		    ;
	


		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      		
		-- 写结束日志
	     V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '生成的版本编码:'||V_VERSION_CODE||'结果表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	--收集统计信息
	ANALYSE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T;

--处理异常信息
	EXCEPTION
		WHEN OTHERS THEN
		
		 PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	X_SUCCESS_FLAG := 'FAIL';		        --2001表示失败

	
		
 END;
 $BODY$
 LANGUAGE PLPGSQL VOLATILE
  COST 100

  
