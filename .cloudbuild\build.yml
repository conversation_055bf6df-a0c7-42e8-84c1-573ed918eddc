---
version: 2.0

#构建环境
env:
  label: BPIT_Build_Default  #通用可信构建资源
params:
  - name: product
    value: cloudbuild2.0
  - name: cmc_password
    value: encryption:xxxxxxxx
#构建步骤
steps:
  PRE_BUILD:
    - checkout
  BUILD:
    - build_execute: #执行构建
        command: "sh build.sh" # 构建命令，如sh build.sh 或 make 或 mvn clean package,构建规范要求构建脚本需要创建单独的脚本目录管理，如”script“         
  POST_BUILD:  #构建后步骤
   #新增数字签名配置
    - sh:
         command: |
             signclient "target/*.zip" #不同签名包之间用分号分隔，demo.war 为示例，按项目实际情况修改
    - artget:
        artifact_type: cloudartifact  # 仓库类型
        action: push #选填。默认值为push,当上传包数量超过10个时必填。
        file_path: "package,target/*.zip;cms,target/*.zip.cms" #上传包的路径是相对路径，相对于workspace,按项目实际情况修改
        version_output_path: .
    - version_set # 记录version_set