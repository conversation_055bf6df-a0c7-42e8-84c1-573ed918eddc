-- ----------------------------
-- Table structure for fop_dwk_grp_sop_act_fcst_qty_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i" (
  "period" varchar(30) COLLATE "pg_catalog"."default",
  "measure_code" varchar(100) COLLATE "pg_catalog"."default",
  "period_flag" varchar(64) COLLATE "pg_catalog"."default",
  "bg_code" varchar(63) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(250) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(250) COLLATE "pg_catalog"."default",
  "ict_domestic_overseas_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "region_code" varchar(63) COLLATE "pg_catalog"."default",
  "region_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "region_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "repoffice_code" varchar(63) COLLATE "pg_catalog"."default",
  "repoffice_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "repoffice_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "hrms_region_code" varchar(63) COLLATE "pg_catalog"."default",
  "hrms_region_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "hrms_region_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "hrms_repoffice_code" varchar(63) COLLATE "pg_catalog"."default",
  "hrms_repoffice_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "hrms_repoffice_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv1_prod_rnd_team_code" varchar(63) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv2_prod_rnd_team_code" varchar(63) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv3_prod_rnd_team_code" varchar(63) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_cn_name" varchar(750) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_en_name" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(750) COLLATE "pg_catalog"."default",
  "attr4" varchar(750) COLLATE "pg_catalog"."default",
  "unit" varchar(750) COLLATE "pg_catalog"."default",
  "month" varchar(10) COLLATE "pg_catalog"."default",
  "qty" numeric,
  "scenario_flag" varchar(50) COLLATE "pg_catalog"."default",
  "dw_last_update_date" timestamp(0)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."period" IS '期次';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."measure_code" IS '维度';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."period_flag" IS '期次标识';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."bg_cn_name" IS 'BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."ict_domestic_overseas_cn_name" IS '国内海外标识(国内、海外)';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."region_code" IS '财经地区部代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."region_cn_name" IS '财经地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."region_en_name" IS '财经地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."repoffice_code" IS '财经代表处代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."repoffice_cn_name" IS '财经代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."repoffice_en_name" IS '财经代表处英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_region_code" IS 'HRMS地区部代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_region_cn_name" IS 'HRMS地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_region_en_name" IS 'HRMS地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_repoffice_code" IS 'HRMS代表处代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_repoffice_cn_name" IS 'HRMS代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."hrms_repoffice_en_name" IS 'HRMS代表处英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv1_prod_rnd_team_code" IS '一级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv1_prod_rd_team_cn_name" IS '一级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv1_prod_rd_team_en_name" IS '一级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv2_prod_rnd_team_code" IS '二级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv2_prod_rd_team_cn_name" IS '二级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv2_prod_rd_team_en_name" IS '二级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv3_prod_rnd_team_code" IS '三级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv3_prod_rd_team_cn_name" IS '三级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."lv3_prod_rd_team_en_name" IS '三级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."attr4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."unit" IS '单位';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."month" IS '月份';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."qty" IS '计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."scenario_flag" IS '数据场景标识(实际数:ACTUAL;预测数:FORECAST)';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_sop_act_fcst_qty_i"."dw_last_update_date" IS 'DW最后更新日期';

