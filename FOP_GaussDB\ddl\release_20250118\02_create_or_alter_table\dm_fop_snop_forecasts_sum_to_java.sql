-- ----------------------------
-- Table structure for dm_fop_snop_forecasts_sum_to_java
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java" (
  "version_code" varchar(100) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(1000) COLLATE "pg_catalog"."default",
  "busi_lv4" varchar(1000) COLLATE "pg_catalog"."default",
  "phase_date" varchar(100) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."version_code" IS '版本编码             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv0_prod_rnd_team_code" IS '重量级团队LV0编码    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv0_prod_rd_team_cn_name" IS '重量级团队LV0中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv0_prod_rd_team_en_name" IS '重量级团队LV0英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv1_prod_rnd_team_code" IS '重量级团队LV1编码    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv1_prod_rd_team_cn_name" IS '重量级团队LV1中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv2_prod_rnd_team_code" IS '重量级团队LV2编码    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv2_prod_rd_team_cn_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv2_prod_rd_team_en_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv3_prod_rnd_team_code" IS '重量级团队LV3编码    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv3_prod_rd_team_cn_name" IS '重量级团队LV3中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."lst_lv3_prod_rd_team_en_name" IS '重量级团队LV3英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."plan_com_lv1" IS '一级计委包           ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."plan_com_lv2" IS '二级计委包           ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."plan_com_lv3" IS '三级计委包           ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."busi_lv4" IS '四级业务包           ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."phase_date" IS '期次分区字段         ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."created_by" IS '创建人               ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."creation_date" IS '创建时间             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."last_updated_by" IS '修改人               ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."last_update_date" IS '修改时间             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java"."del_flag" IS '是否删除             ';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_to_java" IS '产品线S&OP预测汇总表（JAVA）';

