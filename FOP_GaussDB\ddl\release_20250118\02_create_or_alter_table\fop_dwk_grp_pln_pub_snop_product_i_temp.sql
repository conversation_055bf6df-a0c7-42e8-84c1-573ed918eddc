-- ----------------------------
-- Table structure for fop_dwk_grp_pln_pub_snop_product_i_temp
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp";
CREATE TABLE "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp" (
  "phase_no" varchar(75) COLLATE "pg_catalog"."default",
  "phase_date" varchar(75) COLLATE "pg_catalog"."default",
  "month" varchar(75) COLLATE "pg_catalog"."default",
  "fcst_month" numeric,
  "is_release_version_flag" text COLLATE "pg_catalog"."default",
  "snop_code" varchar(375) COLLATE "pg_catalog"."default",
  "item_code" varchar(250) COLLATE "pg_catalog"."default",
  "item_key" numeric,
  "coa_no" varchar(250) COLLATE "pg_catalog"."default",
  "prod_key" numeric,
  "measure_code" varchar(113) COLLATE "pg_catalog"."default",
  "bucket_id" numeric,
  "area_id" varchar(225) COLLATE "pg_catalog"."default",
  "area_key" numeric,
  "area_type" varchar(225) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "site_code" varchar(225) COLLATE "pg_catalog"."default",
  "site_key" numeric,
  "header_id" numeric,
  "line_id" numeric,
  "bucket_desc" varchar(250) COLLATE "pg_catalog"."default",
  "review_status" varchar(38) COLLATE "pg_catalog"."default",
  "pgroup_code" varchar(125) COLLATE "pg_catalog"."default",
  "end_date" timestamp(0),
  "port_qty" numeric,
  "plan_type" varchar(38) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(750) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(750) COLLATE "pg_catalog"."default",
  "attr4" varchar(750) COLLATE "pg_catalog"."default",
  "plan_unit_quantity" numeric,
  "prod_type_code" varchar(65) COLLATE "pg_catalog"."default",
  "prod_type" varchar(750) COLLATE "pg_catalog"."default",
  "prod_sous_type_code" varchar(65) COLLATE "pg_catalog"."default",
  "prod_sous_type" varchar(750) COLLATE "pg_catalog"."default",
  "unit" varchar(750) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_list_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "material_id" numeric,
  "product_id" numeric,
  "product_main_yn" varchar(13) COLLATE "pg_catalog"."default",
  "quantity" numeric,
  "ss_id" numeric,
  "bd_bg_code" text COLLATE "pg_catalog"."default",
  "bd_bg_cn_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bg_en_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bu_code" text COLLATE "pg_catalog"."default",
  "bd_bu_cn_name" varchar(1250) COLLATE "pg_catalog"."default",
  "bd_bu_en_name" varchar(1250) COLLATE "pg_catalog"."default",
  "custom_attr_1" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_2" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_3" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_4" varchar(1000) COLLATE "pg_catalog"."default",
  "custom_attr_5" varchar(1000) COLLATE "pg_catalog"."default",
  "last_update_date_code" timestamp(0),
  "del_flag" varchar(2) COLLATE "pg_catalog"."default",
  "crt_cycle_id" numeric,
  "last_upd_cycle_id" numeric,
  "crt_job_instance_id" numeric,
  "upd_job_instance_id" numeric,
  "dw_last_update_date" timestamp(0)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."phase_no" IS '期次';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."phase_date" IS '期次分区字段';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."month" IS '源系统预测月';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."fcst_month" IS '预测月';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."is_release_version_flag" IS '是否发布版本标识';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."snop_code" IS 'SOP物料编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."item_code" IS 'ITEM编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."item_key" IS 'ITEM_KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."coa_no" IS 'COA编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."prod_key" IS '产品KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."measure_code" IS 'SOP计划数据维度';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bucket_id" IS 'BUCKET_ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."area_id" IS 'SOP计划主体编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."area_key" IS 'SOP计划主体KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."area_type" IS 'SOP计划类型';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bg_cn_name" IS 'BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv0_prod_rnd_team_code" IS '零级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv0_prod_rd_team_cn_name" IS '零级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv0_prod_rd_team_en_name" IS '零级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_rnd_team_code" IS '一级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_rd_team_cn_name" IS '一级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_rd_team_en_name" IS '一级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_rnd_team_code" IS '二级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_rd_team_cn_name" IS '二级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_rd_team_en_name" IS '二级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_rnd_team_code" IS '三级重量级团队代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_rd_team_cn_name" IS '三级重量级团队中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_rd_team_en_name" IS '三级重量级团队英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."site_code" IS '计划SITE';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."site_key" IS '计划SITEKEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."header_id" IS 'SOP计划头ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."line_id" IS 'SOP计划行ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bucket_desc" IS 'BUCKET描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."review_status" IS 'SOP计划状态';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."pgroup_code" IS '短编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."end_date" IS '编码失效时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."port_qty" IS '计委包系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."plan_type" IS '计划类型';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."attr4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."plan_unit_quantity" IS '计划单元计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."prod_type_code" IS '产品大类编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."prod_type" IS '产品大类';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."prod_sous_type_code" IS '产品小类编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."prod_sous_type" IS '产品小类';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."unit" IS '单位';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_list_code" IS '一级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_list_cn_name" IS '一级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv1_prod_list_en_name" IS '一级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_list_code" IS '二级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_list_cn_name" IS '二级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv2_prod_list_en_name" IS '二级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_list_code" IS '三级产品目录代码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_list_cn_name" IS '三级产品目录中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."lst_lv3_prod_list_en_name" IS '三级产品目录英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."material_id" IS '物料ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."product_id" IS '产品ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."product_main_yn" IS '是否主产品行';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."quantity" IS 'SNOP数量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."ss_id" IS '源系统ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bg_code" IS '预算BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bg_cn_name" IS '预算BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bg_en_name" IS '预算BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bu_code" IS '预算BU编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bu_cn_name" IS '预算BU中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."bd_bu_en_name" IS '预算BU英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."custom_attr_1" IS '弹性域1';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."custom_attr_2" IS '弹性域2';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."custom_attr_3" IS '弹性域3';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."custom_attr_4" IS '弹性域4';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."custom_attr_5" IS '弹性域5';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."last_update_date_code" IS 'SOP编码信息更新人日期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."del_flag" IS '删除标记';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."crt_cycle_id" IS '数据创建批号ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."last_upd_cycle_id" IS '数据更新批号ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."crt_job_instance_id" IS '创建任务ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."upd_job_instance_id" IS '更新任务ID';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp"."dw_last_update_date" IS '最后更新时间';
COMMENT ON TABLE "fin_dm_opt_fop"."fop_dwk_grp_pln_pub_snop_product_i_temp" IS '业财联接_ICT产业SOP计划计划量_sop编码粒度_SOP预测计划管理单元计划量临时表';

