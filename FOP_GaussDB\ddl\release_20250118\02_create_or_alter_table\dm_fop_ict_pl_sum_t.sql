-- ----------------------------
-- Table structure for dm_fop_ict_pl_sum_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t" (
  "period_id" numeric NOT NULL,
  "report_item_l1_code" varchar(50) COLLATE "pg_catalog"."default",
  "report_item_l1_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "report_item_l2_code" varchar(50) COLLATE "pg_catalog"."default",
  "report_item_l2_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "rmb_fact_ex_rate_ptd_amt" numeric(38,10),
  "usd_fact_ex_rate_ptd_amt" numeric(38,10),
  "prod_key" numeric,
  "prod_code" varchar(50) COLLATE "pg_catalog"."default",
  "prod_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "geo_pc_key" numeric,
  "oversea_flag" varchar(20) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "version_code" varchar(100) COLLATE "pg_catalog"."default",
  "prod_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "region_code" varchar(50) COLLATE "pg_catalog"."default",
  "region_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "region_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "rep_office_code" varchar(50) COLLATE "pg_catalog"."default",
  "rep_office_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "rep_office_en_name" varchar(200) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."report_item_l1_code" IS '报表项1级编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."report_item_l1_cn_name" IS '报表项2级中文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."report_item_l2_code" IS '报表项2级编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."rmb_fact_ex_rate_ptd_amt" IS '人民币实际汇率当期发生额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."usd_fact_ex_rate_ptd_amt" IS '美元实际汇率当期发生额';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."prod_key" IS '产品KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."prod_code" IS '产品编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."prod_cn_name" IS '产品名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."bg_name" IS 'BG中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv0_prod_rnd_team_code" IS '重量级团队LV0编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv0_prod_rd_team_cn_name" IS '重量级团队LV0中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv0_prod_rd_team_en_name" IS '重量级团队LV0英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv1_prod_rnd_team_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv1_prod_rd_team_cn_name" IS '重量级团队LV1中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv2_prod_rnd_team_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv2_prod_rd_team_cn_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv2_prod_rd_team_en_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv3_prod_rnd_team_code" IS '重量级团队LV3编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv3_prod_rd_team_cn_name" IS '重量级团队LV3中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."lv3_prod_rd_team_en_name" IS '重量级团队LV3英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."geo_pc_key" IS '区域责任中心KEY';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."oversea_flag" IS '海外标志';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."version_code" IS '版本编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."prod_en_name" IS '产品英文描述 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."bg_en_name" IS 'BG英文名称 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."region_code" IS '地区部编码  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."region_cn_name" IS '地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."region_en_name" IS '地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."rep_office_code" IS '代表处编码  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."rep_office_cn_name" IS '代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t"."rep_office_en_name" IS '代表处英文名称';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_ict_pl_sum_t" IS 'ICT损益汇总表';

