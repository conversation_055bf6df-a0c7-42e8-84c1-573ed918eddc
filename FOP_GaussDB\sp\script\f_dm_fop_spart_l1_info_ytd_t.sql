-- ----------------------------
-- Function structure for f_dm_fop_spart_l1_info_ytd_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l1_info_ytd_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l1_info_ytd_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-06-01
创建人  ：鲁广武  lwx1186472
背景描述：作业对象L1层级数据年累计表（只保留一个版本的数据）(提供给知识表示)
参数描述：参数一(p_version_code)：版本编码，格式：当前年月日_V1...VN
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l1_info_ytd_t()										   		  
*/ 		

								   
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l1_info_ytd_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t';
	v_tbl_name2 varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t';	
	v_version_code varchar(50);  -- 版本编码
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';
  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L1层级数据年累计表：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


--判断p_version_code是否有值，取传入参数，无值取最大的版本号
--当p_version_code有值时，取传入参数
  if (p_version_code is not null or p_version_code <> '') 
   then
   
  ---判断p_version_code在his_t表中是否有值
  if exists (select 1 from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t where version_code = p_version_code)
   then   
  
     ----计算ytd的相应指标
     ---删除重跑his_ytd表中已有传入参数的数据
     delete fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t where version_code = p_version_code;
	  
  --ytd指标数据入目标表   
  insert into fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t 
             (
                version_code
               ,period_id
               ,bg_code
               ,bg_name
               ,oversea_desc
               ,lv1_code
               ,lv1_name
               ,lv2_code
               ,lv2_name
               ,l1_name
               ,currency
               ,ship_ytd
               ,spart_ytd
               ,equip_rev_cons_before_ytd
               ,equip_cost_cons_before_ytd
               ,equip_rev_cons_after_ytd
               ,equip_cost_cons_after_ytd
               ,mgp_ratio_ytd
               ,mca_adjust_ratio_ytd
               ,mgp_adjust_ratio_ytd
               ,carryover_ratio_ytd
               ,unit_cost_ytd
               ,unit_price_ytd
               ,articulation_flag
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			  )
	-- 计算金额、数量类ytd(直取)
  with qty_amt_ytd_tmp as (	
     select version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as ship_ytd,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency 
										   ,articulation_flag
								  order by period_id) as spart_ytd,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
						          order by period_id) as equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_rev_cons_after_ytd,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_cost_cons_after_ytd,--设备成本本年累计（对价后）
    		articulation_flag--勾稽方法标签
       from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
      where	version_code = p_version_code   
),
	-- 计算场景一的 mca调整率、制毛调整率_ytd（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）
	adjust_rate_sceno_ytd_tmp as (
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_rev_cons_after_ytd,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_cost_cons_after_ytd--设备成本本年累计（对价后）
	  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t   
	 where version_code = p_version_code
	   and articulation_flag = 'SCENO1'
	),			   
	--计算制毛率、mca调整率、结转率、单位成本、单位价格（ytd）
  mgp_adjust_rate_ytd_tmp as (
	 select t1.version_code,  -- 版本编码
	        t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			t1.ship_ytd,--发货量（本年累计）
			t1.spart_ytd,--收入量（本年累计）
			t1.equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
			t1.equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
			t1.equip_rev_cons_after_ytd,--	设备收入本年累计（对价后）
			t1.equip_cost_cons_after_ytd,--设备成本本年累计（对价后）
			(case when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) = 0 then 0
				    when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0 then -999999
				    when nvl(t1.equip_rev_cons_after_ytd,0) <> 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0
					  then 1 - t1.equip_cost_cons_after_ytd  / t1.equip_rev_cons_after_ytd
				    else null
			 end) as mgp_ratio_ytd,--制毛率(本年累计) = 1 - l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1')
			      then (case when nvl ( t2.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t2.equip_rev_cons_after_ytd, 0 ) = 0 then 0
				               when nvl ( t2.equip_rev_cons_before_ytd, 0 ) = 0 then -999999
				               else 1 - t2.equip_rev_cons_after_ytd / t2.equip_rev_cons_before_ytd
				          end)
			      when t1.articulation_flag in('SCENO2')
			      then (case when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_rev_cons_after_ytd, 0 ) = 0 then 0
				               when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 then -999999
				               else 1 - t1.equip_rev_cons_after_ytd / t1.equip_rev_cons_before_ytd
				          end)
				    else null
			 end) as mca_adjust_ratio_ytd,--mca调整率(本年累计) = 1 – 对价后收入/对价前收入
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t2.equip_cost_cons_before_ytd,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t2.equip_cost_cons_before_ytd,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_ytd,0) <> 0 then t2.equip_cost_cons_before_ytd  / t2.equip_rev_cons_before_ytd
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_ytd,0) <> 0 then t1.equip_cost_cons_before_ytd  / t1.equip_rev_cons_before_ytd
					         end)
				     else null
			  end) as mgp_ratio_before_ytd,--l1对价前成本金额/l1对价前收入金额
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_ytd,0) = 0 and nvl(t2.equip_cost_cons_after_ytd,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_ytd,0) = 0 and nvl(t2.equip_cost_cons_after_ytd,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_ytd,0) <> 0 then t2.equip_cost_cons_after_ytd  / t2.equip_rev_cons_after_ytd
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_ytd,0) <> 0 then t1.equip_cost_cons_after_ytd  / t1.equip_rev_cons_after_ytd
						       end)
				   else null
				   end) as mgp_ratio_after_ytd,--l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_ytd = 0 and t1.spart_ytd = 0 then null
				    when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_ytd = 0 then -999999
				    when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_ytd / t1.ship_ytd
				    else null
			 end) as carryover_ratio_ytd,--结转率(本年累计) = l1收入数量/l1发货数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) <> 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then t1.equip_cost_cons_before_ytd / t1.spart_ytd
				    else null
			 end) as unit_cost_ytd,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) <> 0 and nvl(t1.equip_rev_cons_before_ytd,0) <> 0 then t1.equip_rev_cons_before_ytd / t1.spart_ytd
				    else null
			 end) as unit_price_ytd,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			t1.articulation_flag--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  from qty_amt_ytd_tmp t1
	  left join adjust_rate_sceno_ytd_tmp t2
	    on t1.version_code = t2.version_code
	   and t1.period_id    = t2.period_id
	   and t1.bg_code      = t2.bg_code
	   and t1.oversea_desc = t2.oversea_desc
	   and t1.lv1_code     = t2.lv1_code
	   and t1.currency     = t2.currency
	)
----计算制毛调整率（本年累计）
     select version_code,
            period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_ytd,
			spart_ytd,
			equip_rev_cons_before_ytd,
			equip_cost_cons_before_ytd,
			equip_rev_cons_after_ytd,
			equip_cost_cons_after_ytd,
			mgp_ratio_ytd,
			mca_adjust_ratio_ytd,
			(case when mgp_ratio_before_ytd <> -999999 and mgp_ratio_after_ytd <> -999999 then mgp_ratio_before_ytd - mgp_ratio_after_ytd
				  when mgp_ratio_before_ytd = -999999  and mgp_ratio_after_ytd = -999999  then 0
				  when mgp_ratio_before_ytd = -999999   or mgp_ratio_after_ytd = -999999  then -999999
			      end) as mgp_adjust_ratio_ytd,--制毛调整率
			carryover_ratio_ytd,
			unit_cost_ytd,
			unit_price_ytd,
			articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			'' as remark,--备注
			'-1' as created_by,--创建人
			current_timestamp as creation_date,--创建时间
			'-1' as last_updated_by,--修改人
			current_timestamp as last_update_date,--修改时间
			'N' as del_flag --是否删除
       from mgp_adjust_rate_ytd_tmp   
	;	

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '作业对象L1层级数据年累计表(多个版本)'||v_tbl_name2||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t;
   
  --入目标表
    insert into fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t(
			 period_id
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l1_name
			,currency
			,ship_ytd
			,spart_ytd
			,equip_rev_cons_before_ytd
			,equip_cost_cons_before_ytd
			,equip_rev_cons_after_ytd
			,equip_cost_cons_after_ytd
			,mgp_ratio_ytd
			,mca_adjust_ratio_ytd
			,mgp_adjust_ratio_ytd
			,carryover_ratio_ytd
			,unit_cost_ytd
			,unit_price_ytd
			,articulation_flag
			,remark
			,created_by
			,creation_date
			,last_updated_by
			,last_update_date
			,del_flag
			)
		select  period_id
			   ,bg_code
			   ,bg_name
			   ,oversea_desc
			   ,lv1_code
			   ,lv1_name
			   ,lv2_code
			   ,lv2_name
			   ,l1_name
			   ,currency
			   ,ship_ytd
			   ,spart_ytd
			   ,equip_rev_cons_before_ytd
			   ,equip_cost_cons_before_ytd
			   ,equip_rev_cons_after_ytd
			   ,equip_cost_cons_after_ytd
			   ,mgp_ratio_ytd
			   ,mca_adjust_ratio_ytd
			   ,mgp_adjust_ratio_ytd
			   ,carryover_ratio_ytd
			   ,unit_cost_ytd
			   ,unit_price_ytd
			   ,articulation_flag
			   ,remark
			   ,created_by
			   ,creation_date
			   ,last_updated_by
			   ,last_update_date
			   ,del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t
		 where version_code = p_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量

  -- 写结束日志		
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '作业对象L1层级数据年累计表(一个版本)'||v_tbl_name||'传入的版本编码:'||p_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  else
		
  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  =>3,
        p_log_cal_log_desc => 'his_t表无此版本编码:'||p_version_code||',请重新传入版本！结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	x_success_flag := '2001';       --2001表示失败	  
	return;
end if;

		
else 
  
	-- 取来源表的最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
      from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t 
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t)
     group by substr(version_code,1,6)
	;    
	  -- 判断来源表中是否有最大版本，如有，需要删除his_ytd目标表中最大版本数据
	  delete fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t where version_code = v_max_version_code;
	  
  --ytd指标数据入目标表   
  insert into fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t 
             (
                version_code
               ,period_id
               ,bg_code
               ,bg_name
               ,oversea_desc
               ,lv1_code
               ,lv1_name
               ,lv2_code
               ,lv2_name
               ,l1_name
               ,currency
               ,ship_ytd
               ,spart_ytd
               ,equip_rev_cons_before_ytd
               ,equip_cost_cons_before_ytd
               ,equip_rev_cons_after_ytd
               ,equip_cost_cons_after_ytd
               ,mgp_ratio_ytd
               ,mca_adjust_ratio_ytd
               ,mgp_adjust_ratio_ytd
               ,carryover_ratio_ytd
               ,unit_cost_ytd
               ,unit_price_ytd
               ,articulation_flag
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag
			  )
	-- 计算金额、数量类ytd(直取)
  with qty_amt_ytd_tmp as (	
     select version_code,  -- 版本编码
    	    period_id,--会计期
    		bg_code,--bg编码
    		bg_name,--bg名称
    		oversea_desc,--区域
    		lv1_code,--重量级团队lv1编码
    		lv1_name,--重量级团队lv1描述
    		lv2_code,--重量级团队lv2编码
    		lv2_name,--重量级团队lv2名称
    		l1_name,--l1名称
    		currency,--币种
    		sum(ship_qty)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag 
								  order by period_id) as ship_ytd,--发货量（本年累计）
    		sum(spart_qty)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag 
								  order by period_id) as spart_ytd,--收入量（本年累计）
    		sum(equip_rev_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
						          order by period_id) as equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
    		sum(equip_cost_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
    		sum(equip_rev_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_rev_cons_after_ytd,--设备收入本年累计（对价后）
    		sum(equip_cost_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,lv2_code
                                           ,lv2_name
                                           ,l1_name
										   ,currency
										   ,articulation_flag
								  order by period_id) as equip_cost_cons_after_ytd,--设备成本本年累计（对价后）
    		articulation_flag--勾稽方法标签
       from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t
      where	version_code = v_max_version_code   

),
	-- 计算场景一的 mca调整率、制毛调整率_ytd（l1对价前成本金额/l1对价前收入金额、l1对价后成本金额/l1对价后收入金额）
	adjust_rate_sceno_ytd_tmp as (
	select distinct
	       version_code, -- 版本编码
	       period_id,--会计期
		   bg_code,--bg编码
		   bg_name,--bg名称
		   oversea_desc,--区域
		   lv1_code,--重量级团队lv1编码
		   lv1_name,--重量级团队lv1描述
		   currency,--币种
		   sum(equip_rev_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
		   sum(equip_cost_cons_before_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
		   sum(equip_rev_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency  
								  order by period_id) as equip_rev_cons_after_ytd,--	设备收入本年累计（对价后）
		   sum(equip_cost_cons_after_amt)over(partition by version_code
                                           ,substr(period_id,1,4)		
                                           ,bg_code     
                                           ,bg_name     
                                           ,oversea_desc
                                           ,lv1_code   
                                           ,lv1_name
										   ,currency 
								  order by period_id) as equip_cost_cons_after_ytd--设备成本本年累计（对价后）
	  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_t   
	 where version_code = v_max_version_code
	   and articulation_flag = 'SCENO1'
	),			   
	--计算制毛率、mca调整率、结转率、单位成本、单位价格（ytd）
  mgp_adjust_rate_ytd_tmp as (
	select t1.version_code,  -- 版本编码
	       t1.period_id,--会计期
			t1.bg_code,--bg编码
			t1.bg_name,--bg名称
			t1.oversea_desc,--区域
			t1.lv1_code,--重量级团队lv1编码
			t1.lv1_name,--重量级团队lv1描述
			t1.lv2_code,--重量级团队lv2编码
			t1.lv2_name,--重量级团队lv2名称
			t1.l1_name,--l1名称
			t1.currency,--币种
			t1.ship_ytd,--发货量（本年累计）
			t1.spart_ytd,--收入量（本年累计）
			t1.equip_rev_cons_before_ytd,--设备收入本年累计（对价前）
			t1.equip_cost_cons_before_ytd,--设备成本本年累计（对价前）
			t1.equip_rev_cons_after_ytd,--	设备收入本年累计（对价后）
			t1.equip_cost_cons_after_ytd,--设备成本本年累计（对价后）
			(case when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) = 0 then 0
				    when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0 then -999999
				    when nvl(t1.equip_rev_cons_after_ytd,0) <> 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0
					  then 1 - t1.equip_cost_cons_after_ytd  / t1.equip_rev_cons_after_ytd
				    else null
			 end) as mgp_ratio_ytd,--制毛率(本年累计) = 1 - l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1')
			      then (case when nvl ( t2.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t2.equip_rev_cons_after_ytd, 0 ) = 0 then 0
				               when nvl ( t2.equip_rev_cons_before_ytd, 0 ) = 0 then -999999
				               else 1 - t2.equip_rev_cons_after_ytd / t2.equip_rev_cons_before_ytd
				          end)
			      when t1.articulation_flag in('SCENO2')
			      then (case when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 and nvl ( t1.equip_rev_cons_after_ytd, 0 ) = 0 then 0
				               when nvl ( t1.equip_rev_cons_before_ytd, 0 ) = 0 then -999999
				               else 1 - t1.equip_rev_cons_after_ytd / t1.equip_rev_cons_before_ytd
				          end)
				    else null
			 end) as mca_adjust_ratio_ytd,--mca调整率(本年累计) = 1 – 对价后收入/对价前收入
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t2.equip_cost_cons_before_ytd,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t2.equip_cost_cons_before_ytd,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_before_ytd,0) <> 0 then t2.equip_cost_cons_before_ytd  / t2.equip_rev_cons_before_ytd
					         end)
					   when t1.articulation_flag in('SCENO2')
					   then (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_before_ytd,0) <> 0 then t1.equip_cost_cons_before_ytd  / t1.equip_rev_cons_before_ytd
					         end)
				     else null
			  end) as mgp_ratio_before_ytd,--l1对价前成本金额/l1对价前收入金额
			 (case when t1.articulation_flag in('SCENO1')
			       then (case when nvl(t2.equip_rev_cons_after_ytd,0) = 0 and nvl(t2.equip_cost_cons_after_ytd,0) = 0 then 1
				                when nvl(t2.equip_rev_cons_after_ytd,0) = 0 and nvl(t2.equip_cost_cons_after_ytd,0) <> 0 then -999999
				                when nvl(t2.equip_rev_cons_after_ytd,0) <> 0 then t2.equip_cost_cons_after_ytd  / t2.equip_rev_cons_after_ytd
						       end)
						 when t1.articulation_flag in('SCENO2')
						 then (case when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) = 0 then 1
				                when nvl(t1.equip_rev_cons_after_ytd,0) = 0 and nvl(t1.equip_cost_cons_after_ytd,0) <> 0 then -999999
				                when nvl(t1.equip_rev_cons_after_ytd,0) <> 0 then t1.equip_cost_cons_after_ytd  / t1.equip_rev_cons_after_ytd
						       end)
				   else null
				   end) as mgp_ratio_after_ytd,--l1对价后成本金额/l1对价后收入金额
			(case when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_ytd = 0 and t1.spart_ytd = 0 then null
				    when t1.articulation_flag in('SCENO1','SCENO2') and t1.ship_ytd = 0 then -999999
				    when t1.articulation_flag in('SCENO1','SCENO2') then t1.spart_ytd / t1.ship_ytd
				    else null
			 end) as carryover_ratio_ytd,--结转率(本年累计) = l1收入数量/l1发货数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) <> 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then t1.equip_cost_cons_before_ytd / t1.spart_ytd
				    else null
			 end) as unit_cost_ytd,	--单位成本(本年累计) = l1对价前成本金额/l1收入数量
			(case when t1.articulation_flag= 'SCENO1' and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) = 0 then -999999
				    when t1.articulation_flag= 'SCENO1' and nvl(t1.spart_ytd,0) <> 0 and nvl(t1.equip_rev_cons_before_ytd,0) <> 0 then t1.equip_rev_cons_before_ytd / t1.spart_ytd
				    else null
			 end) as unit_price_ytd,	--单位价格(本年累计) = l1对价前收入金额/l1收入数量
			t1.articulation_flag--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  from qty_amt_ytd_tmp t1
	  left join adjust_rate_sceno_ytd_tmp t2
	    on t1.version_code = t2.version_code
	   and t1.period_id    = t2.period_id
	   and t1.bg_code      = t2.bg_code
	   and t1.oversea_desc = t2.oversea_desc
	   and t1.lv1_code     = t2.lv1_code
	   and t1.currency     = t2.currency
	)
----计算制毛调整率（本年累计）
     select version_code,
            period_id,
			bg_code,
			bg_name,
			oversea_desc,
			lv1_code,
			lv1_name,
			lv2_code,
			lv2_name,
			l1_name,
			currency,
			ship_ytd,
			spart_ytd,
			equip_rev_cons_before_ytd,
			equip_cost_cons_before_ytd,
			equip_rev_cons_after_ytd,
			equip_cost_cons_after_ytd,
			mgp_ratio_ytd,
			mca_adjust_ratio_ytd,
			(case when mgp_ratio_before_ytd <> -999999 and mgp_ratio_after_ytd <> -999999 then mgp_ratio_before_ytd - mgp_ratio_after_ytd
				  when mgp_ratio_before_ytd = -999999  and mgp_ratio_after_ytd = -999999  then 0
				  when mgp_ratio_before_ytd = -999999   or mgp_ratio_after_ytd = -999999  then -999999
			      end) as mgp_adjust_ratio_ytd,--制毛调整率
			carryover_ratio_ytd,
			unit_cost_ytd,
			unit_price_ytd,
			articulation_flag,--勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			'' as remark,--备注
			'-1' as created_by,--创建人
			current_timestamp as creation_date,--创建时间
			'-1' as last_updated_by,--修改人
			current_timestamp as last_update_date,--修改时间
			'N' as del_flag --是否删除
       from mgp_adjust_rate_ytd_tmp   
	;	

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '作业对象L1层级数据年累计表(多个版本)'||v_tbl_name2||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t;
  
  --入目标表
    insert into fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t(
			 period_id
			,bg_code
			,bg_name
			,oversea_desc
			,lv1_code
			,lv1_name
			,lv2_code
			,lv2_name
			,l1_name
			,currency
			,ship_ytd
			,spart_ytd
			,equip_rev_cons_before_ytd
			,equip_cost_cons_before_ytd
			,equip_rev_cons_after_ytd
			,equip_cost_cons_after_ytd
			,mgp_ratio_ytd
			,mca_adjust_ratio_ytd
			,mgp_adjust_ratio_ytd
			,carryover_ratio_ytd
			,unit_cost_ytd
			,unit_price_ytd
			,articulation_flag
			,remark
			,created_by
			,creation_date
			,last_updated_by
			,last_update_date
			,del_flag
			)
		select  period_id
			   ,bg_code
			   ,bg_name
			   ,oversea_desc
			   ,lv1_code
			   ,lv1_name
			   ,lv2_code
			   ,lv2_name
			   ,l1_name
			   ,currency
			   ,ship_ytd
			   ,spart_ytd
			   ,equip_rev_cons_before_ytd
			   ,equip_cost_cons_before_ytd
			   ,equip_rev_cons_after_ytd
			   ,equip_cost_cons_after_ytd
			   ,mgp_ratio_ytd
			   ,mca_adjust_ratio_ytd
			   ,mgp_adjust_ratio_ytd
			   ,carryover_ratio_ytd
			   ,unit_cost_ytd
			   ,unit_price_ytd
			   ,articulation_flag
			   ,remark
			   ,created_by
			   ,creation_date
			   ,last_updated_by
			   ,last_update_date
			   ,del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t
		 where version_code = v_max_version_code
		;
		
		v_dml_row_count := sql%rowcount;  -- 收集数据量
	
   --写结束日志	
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '作业对象L1层级数据年累计表(一个版本)'||v_tbl_name||'最大的版本编码:'||v_max_version_code||',的数据量:'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

		
  end if;
  

--处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.p_dm_pf_capture_exception(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l1_info_his_ytd_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l1_info_ytd_t;	
	
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

