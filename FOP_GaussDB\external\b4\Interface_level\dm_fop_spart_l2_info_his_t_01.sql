-- Step 2：数据提取

标签名称：spart_detail_info_tmp1   数据源：fin_dm_opt_fop_uat

select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_flag
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , l2_name
     , coa_l2_name
     , rmb_revenue
     , rmb_cost
     , equip_rev_rmb_amt
     , equip_cost_rmb_amt
     , equip_rev_usd_amt
     , equip_cost_usd_amt
     , usd_revenue
     , usd_cost
     , snop_quantity
     , snop_plan_quantity
     , ship_qty
     , spart_qty
     , plan_unit_quantity
     , articulation_flag
     , source_table
  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
 where articulation_flag in ('SCENO1','SCENO2')
   and upper(industry_type) = 'TGT'
   and version_code = '${V_DETAIL_MAX_VERSION_CODE}'
   and del_flag = 'N'
;

-- Step 3：SQL-Script
/*S&OP预算：如果<10月取期次年份的全年数据，如果>=10月取期次年份下一年的全年*/
cache lazy table spart_detail_info_tmp
as
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(snop_plan_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  /*S&OP预算*/
   and substr(period_id,1,4) = '${V_SNOP_BUDGET_PHASE_YEAR}'  /*取期次年份的全年数据，取最大版本的所有期次*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
 union all
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end ) as varchar(200)) as l2_name
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0)) as equip_rev_cons_before_amt    /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))    as equip_cost_cons_before_amt   /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt   /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt  /*设备成本金额(对价后)*/
     , sum(coalesce(snop_plan_quantity,0)) as plan_qty   /*发货量（SNOP）*/
     , sum(coalesce(ship_qty,0))           as ship_qty   /*发货量（历史）*/
     , sum(coalesce(spart_qty,0))          as spart_qty  /*收入量（历史）*/
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  /*S&OP预算*/
   and substr(period_id,1,4) = '${V_SNOP_BUDGET_PHASE_YEAR}'  /*取期次年份的全年数据，取最大版本的所有期次*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
 union all
/*非S&OP计算逻辑*/
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(snop_quantity,0) + coalesce(snop_plan_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
 union all
select version_code    /*版本编码*/
     , cast(period_id as int) as period_id     /*会计期  */
     , phase_date
     , bg_code       /*bg编码  */
     , bg_name       /*bg名称  */
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code    /*重量级团队lv1编码*/
     , lv1_prod_rd_team_cn_name as lv1_name    /*重量级团队lv1描述*/
     , lv2_prod_rnd_team_code   as lv2_code    /*重量级团队lv2编码*/
     , lv2_prod_rd_team_cn_name as lv2_name    /*重量级团队lv2名称*/
     , l1_name   /*l1名称*/
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient	  /*l1系数*/
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient      /*l2系数*/
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end ) as varchar(200)) as l2_name  /*l2名称*/
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0))        as equip_rev_cons_before_amt   /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))           as equip_cost_cons_before_amt  /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt    /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt   /*设备成本金额(对价后)*/
     , sum(coalesce(snop_quantity,0) + coalesce(snop_plan_quantity,0)) as plan_qty  /*发货量（snop）*/
     , sum(coalesce(ship_qty,0))  as ship_qty  /*发货量（历史）*/
     , sum(coalesce(spart_qty,0)) as spart_qty  /*收入量（历史）*/
     , articulation_flag   /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
     , source_table
  from spart_detail_info_tmp1
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
 union all
/*S&OP预测的取数逻辑与传入起始日期参数有关，所以逻辑需要单独取*/
/*传入参数为空时，期次取系统当前年月的6号至上月6号*/
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(plan_unit_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'   /*S&OP预测*/
   and phase_date >= '${V_SNOP_FCST_BEGIN_PHASE_DATE}'   /*如果传入参数为空，则取系统当前年月的上个月6号*/
   and phase_date < '${V_SNOP_FCST_END_PHASE_DATE}'      /*如果传入参数为空，则取系统当前年月6号*/
   and substr(period_id,1,4) = '${V_SNOP_FCST_YEAR}'     /*如果传入参数为空，则取系统当前年份数据*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
 union all
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
	   , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0))        as equip_rev_cons_before_amt   /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))           as equip_cost_cons_before_amt  /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt    /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt   /*设备成本金额(对价后)*/
     , sum(coalesce(plan_unit_quantity,0)) as plan_qty   /*发货量（SNOP）*/
     , sum(coalesce(ship_qty,0))  as ship_qty   /*发货量（历史）*/
     , sum(coalesce(spart_qty,0)) as spart_qty  /*收入量（历史）*/
     , articulation_flag  /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'   /*S&OP预测*/
   and phase_date >= '${V_SNOP_FCST_BEGIN_PHASE_DATE}'   /*如果传入参数为空，则取系统当前年月的上个月6号*/
   and phase_date < '${V_SNOP_FCST_END_PHASE_DATE}'      /*如果传入参数为空，则取系统当前年月6号*/
   and substr(period_id,1,4) = '${V_SNOP_FCST_YEAR}'     /*如果传入参数为空，则取系统当前年份数据*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , articulation_flag
     , source_table
;

/*数据入到对接l2作业层级函数接口表*/
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_coefficient
     , l2_name
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , currency
     , articulation_flag
     , source_table
     , '' as remark
     ,  -1 as created_by
     ,  current_timestamp as creation_date
     ,  -1 as last_updated_by
     ,  current_timestamp as last_update_date
     ,  'N' as del_flag
  from spart_detail_info_tmp
  
  	  	-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_l2_info_his_t_01      模式：TRUNCATE_TABLE
