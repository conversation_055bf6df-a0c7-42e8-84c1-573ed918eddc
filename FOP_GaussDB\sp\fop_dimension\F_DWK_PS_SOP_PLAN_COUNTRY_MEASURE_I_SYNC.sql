CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC(OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
/*
创建时间：2025-07-21
创建人  ：周博孝
背景描述：将 DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC 表中的数据同步到目标表 DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I
参数描述：X_SUCCESS_FLAG: 返回状态 SUCCESS / FAIL
事例    ：SELECT FIN_DM_OPT_FOP.F_DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC();
*/

DECLARE
    V_SP_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.F_DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC';
	V_TBL_NAME VARCHAR(100) := 'DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I';
	V_DML_ROW_COUNT INTEGER DEFAULT 0;
	V_STEP_NUM   NUMERIC; --步骤号

BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';

	-- 写日志，开始
	V_STEP_NUM := 1;
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '同步表'||V_TBL_NAME||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

    -- 清理目标表中与源表中相同
    DELETE FROM FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I
    WHERE PERIOD IN (
        SELECT DISTINCT PERIOD
        FROM FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC
    );

    INSERT INTO FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I (
        ID,
        PERIOD,
        BG_CN,
        LV1_INDUSTRY_CATG_CODE,
        UOM,
        DEMISION_FLAG,
        NO_ATTR4,
        DIMENSION_SUBCATEGORY_CN_NAME,
        DIMENSION_SUBCATEGORY_EN_NAME,
        CN_DIMENSION_GROUP_L2_CODE,
        CN_DIMENSION_GROUP_L2,
        DW_LAST_UPDATE_DATE,
        MONTH,
        LV2_INDUSTRY_CATG_EN_NAME,
        NO_ATTR2,
        FREQUENCY_BAND,
        DIMENSION_SUB_DETAIL_EN_NAME,
        MEASURE_CODE,
        LV3_ORG_CN,
        LV0_INDUSTRY_CATG_CODE,
        LV2_INDUSTRY_CATG_CN_NAME,
        LV3_INDUSTRY_CATG_CODE,
        DIMENSION_KEY,
        INDUSTRY_CATG_CODE,
        DIMENSION_TYPE,
        PRODUCT_DIMENSION_CODE,
        INDUSTRY_DIMENSION_EN_NAME,
        DIMENSION_SUBCATEGORY_CODE,
        CN_DIMENSION_GROUP_L1_CODE,
        LV2_ORG_CN,
        LV0_INDUSTRY_CATG_CN_NAME,
        ATTR1,
        ATTR2,
        ATTR4,
        COUNTRY_MEASURE,
        PRODUCT_DIMENSION_EN_NAME,
        INDUSTRY_DIMENSION_CN_NAME,
        PERIOD_FLAG,
        LV1_ORG_CN,
        LV0_INDUSTRY_CATG_EN_NAME,
        LV1_INDUSTRY_CATG_CN_NAME,
        LV2_INDUSTRY_CATG_CODE,
        LV3_INDUSTRY_CATG_CN_NAME,
        LV3_INDUSTRY_CATG_EN_NAME,
        ATTR3,
        NO_ATTR3,
        NO_UOM,
        PRODUCT_DIMENSION_CN_NAME,
        INDUSTRY_DIMENSION_CODE,
        DIMENSION_SUB_DETAIL_CODE,
        DIMENSION_SUB_DETAIL_CN_NAME,
        QTY,
        CN_DIMENSION_GROUP_L1,
        LAST_UPDATE_DATE
    )
    SELECT
        ID,
        PERIOD,
        BG_CN,
        LV1_INDUSTRY_CATG_CODE,
        UOM,
        DEMISION_FLAG,
        NO_ATTR4,
        DIMENSION_SUBCATEGORY_CN_NAME,
        DIMENSION_SUBCATEGORY_EN_NAME,
        CN_DIMENSION_GROUP_L2_CODE,
        CN_DIMENSION_GROUP_L2,
        DW_LAST_UPDATE_DATE,
        MONTH,
        LV2_INDUSTRY_CATG_EN_NAME,
        NO_ATTR2,
        FREQUENCY_BAND,
        DIMENSION_SUB_DETAIL_EN_NAME,
        MEASURE_CODE,
        LV3_ORG_CN,
        LV0_INDUSTRY_CATG_CODE,
        LV2_INDUSTRY_CATG_CN_NAME,
        LV3_INDUSTRY_CATG_CODE,
        DIMENSION_KEY,
        INDUSTRY_CATG_CODE,
        DIMENSION_TYPE,
        PRODUCT_DIMENSION_CODE,
        INDUSTRY_DIMENSION_EN_NAME,
        DIMENSION_SUBCATEGORY_CODE,
        CN_DIMENSION_GROUP_L1_CODE,
        LV2_ORG_CN,
        LV0_INDUSTRY_CATG_CN_NAME,
        ATTR1,
        ATTR2,
        ATTR4,
        COUNTRY_MEASURE,
        PRODUCT_DIMENSION_EN_NAME,
        INDUSTRY_DIMENSION_CN_NAME,
        PERIOD_FLAG,
        LV1_ORG_CN,
        LV0_INDUSTRY_CATG_EN_NAME,
        LV1_INDUSTRY_CATG_CN_NAME,
        LV2_INDUSTRY_CATG_CODE,
        LV3_INDUSTRY_CATG_CN_NAME,
        LV3_INDUSTRY_CATG_EN_NAME,
        ATTR3,
        NO_ATTR3,
        NO_UOM,
        PRODUCT_DIMENSION_CN_NAME,
        INDUSTRY_DIMENSION_CODE,
        DIMENSION_SUB_DETAIL_CODE,
        DIMENSION_SUB_DETAIL_CN_NAME,
        QTY,
        CN_DIMENSION_GROUP_L1,
        LAST_UPDATE_DATE
    FROM FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I_SYNC;

    V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '同步表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	ANALYSE FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I;

EXCEPTION
	WHEN OTHERS THEN
		-- 捕获异常并记录日志
		PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	    X_SUCCESS_FLAG := 'FAIL';

END;
$BODY$
LANGUAGE PLPGSQL VOLATILE
  COST 100;