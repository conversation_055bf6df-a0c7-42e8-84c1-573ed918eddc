DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I;
CREATE TABLE FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I (
    ROW_ID BIGSERIAL PRIMARY KEY NOT NULL ,
    BS_ID NUMERIC,
    BS_CODE NVARCHAR2(250),
    BS_CN_NAME NVARCHAR2(250),
    BS_ATTR_GROUP_ID NUMERIC,
    TIME_WINDOW_CODE NVARCHAR2(125),
    ATTR_GROUP_CODE NVARCHAR2(250),
    STAT_PERIOD_ID NUMERIC,
    START_PERIOD_ID NUMERIC,
    END_PERIOD_ID NUMERIC,
    LV0_PROD_LIST_CODE NVARCHAR2(65),
    LV0_PROD_LIST_EN_NAME NVARCHAR2(200),
    LV0_PROD_LIST_CN_NAME NVARCHAR2(200),
    LV2_PROD_RND_TEAM_CODE NVARCHAR2(1000),
    LV2_PROD_RD_TEAM_EN_NAME NVARCHAR2(600),
    <PERSON>V2_PROD_RD_TEAM_CN_NAME NVARCHAR2(600),
    DIMENSION_GROUP_L2_EN_NAME NVARCHAR2(1000),
    DIMENSION_GROUP_L2_CN_NAME NVARCHAR2(1000),
    DIMENSION_GROUP_CODE_L2 NVARCHAR2(1000),
    PRODUCT_DIMENSION_GROUP_CODE NVARCHAR2(1000),
    PRODUCT_DIMENSION_GROUP_EN_NAME NVARCHAR2(2000),
    PRODUCT_DIMENSION_GROUP NVARCHAR2(2000),
    REGION_CODE NVARCHAR2(50),
    REGION_EN_NAME NVARCHAR2(200),
    REGION_CN_NAME NVARCHAR2(200),
    OVERSEA_FLAG NVARCHAR2(1000),
    CURRENCY_CODE NVARCHAR2(1000),
    DOMESTIC_OR_OVERSEA_CODE NVARCHAR2(1000),
    DOMESTIC_OR_OVERSEA_CNAME NVARCHAR2(3000),
    DOMESTIC_OR_OVERSEA_ENAME NVARCHAR2(3000),
    DIMENSION_SUBCATEGORY_CODE NVARCHAR2(1000),
    DIMENSION_SUBCATEGORY_CN_NAME NVARCHAR2(2000),
    DIMENSION_SUBCATEGORY_EN_NAME NVARCHAR2(2000),
    BS_VALUE NUMERIC,
    BS_NUMERATOR_VALUE NUMERIC,
    BS_DENOMINATOR_VALUE NUMERIC,
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON TABLE DWK_BS_PS_COST_UNIT_PRICE_DCP_I IS 'ICT产业产品成本均价基线接口表';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_ID IS '基线ID';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_CODE IS '基线CODE';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_CN_NAME IS '基线中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_ATTR_GROUP_ID IS '基线属性组ID';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.TIME_WINDOW_CODE IS '统计时间窗编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.ATTR_GROUP_CODE IS '属性组编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.STAT_PERIOD_ID IS '统计时间';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.START_PERIOD_ID IS '开始时间';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.END_PERIOD_ID IS '结束时间';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV0_PROD_LIST_CODE IS '产品零级目录代码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV0_PROD_LIST_EN_NAME IS '产品零级目录英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV0_PROD_LIST_CN_NAME IS '产品零级目录中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV2_PROD_RND_TEAM_CODE IS '二级产品研发团队代码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV2_PROD_RD_TEAM_EN_NAME IS '二级产品研发团队英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LV2_PROD_RD_TEAM_CN_NAME IS '二级产品研发团队中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_GROUP_L2_EN_NAME IS '量纲分组L2英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_GROUP_L2_CN_NAME IS '量纲分组L2中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_GROUP_CODE_L2 IS '量纲分组编码L2';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.PRODUCT_DIMENSION_GROUP_CODE IS '量纲分组编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.PRODUCT_DIMENSION_GROUP_EN_NAME IS '量纲分组英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.PRODUCT_DIMENSION_GROUP IS '量纲分组';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.REGION_CODE IS '地区部编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.REGION_EN_NAME IS '地区部英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.REGION_CN_NAME IS '地区部中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.OVERSEA_FLAG IS '海外标志';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.CURRENCY_CODE IS '币种';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DOMESTIC_OR_OVERSEA_CODE IS 'ICT国内或海外编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DOMESTIC_OR_OVERSEA_CNAME IS 'ICT国内或海外中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DOMESTIC_OR_OVERSEA_ENAME IS 'ICT国内或海外英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_SUBCATEGORY_CODE IS '量纲子类编码';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_SUBCATEGORY_CN_NAME IS '量纲子类中文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.DIMENSION_SUBCATEGORY_EN_NAME IS '量纲子类英文名称';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_VALUE IS '基线值-集团成本单位平均价';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_NUMERATOR_VALUE IS '分子值-集团成本金额';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.BS_DENOMINATOR_VALUE IS '分母值-量纲业务量';
COMMENT ON COLUMN DWK_BS_PS_COST_UNIT_PRICE_DCP_I.LAST_UPDATE_DATE IS 'FOP最后更新日期';