/*量纲分析师导入结果表*/
DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T;
CREATE  TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T (
	PERIOD_ID NUMERIC,
	TARGET_CODE CHARACTER VARYING(50),
	TARGET_DESC CHARACTER VARYING(100),
	BG_CODE CHARACTER VARYING(50),
	BG_NAME CHARACTER VARYING(200),
	OVERSEA_CODE CHARACTER VARYING(50),
	OVERSEA_DESC CHARACTER VARYING(50),
	LV1_CODE CHARACTER VARYING(100),
	LV1_NAME CHARACTER VARYING(600),
	LV2_CODE CHARACTER VARYING(100),
	LV2_NAME CHARACTER VARYING(600),
	<PERSON><PERSON><PERSON>NCY CHARACTER VARYING(50),
	EQUIP_REV_AMT NUMERIC(38,10),
	MGP_RATIO NUMERIC(38,10),
	STATUS CHARACTER VARYING(50),
	REMARK CHARACTER VARYING(500),
	CREATED_BY BIGINT,
	CREATION_DATE TIMESTAMP WITHOUT TIME ZONE,
	LAST_UPDATED_BY BIGINT,
	LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
	DEL_FLAG CHARACTER VARYING(10)
)
WITH (ORIENTATION=COLUMN, COMPRESSION=LOW, COLVERSION=2.0, ENABLE_DELTA=FALSE)
DISTRIBUTE BY HASH(PERIOD_ID,TARGET_CODE)
;
COMMENT ON TABLE DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T IS '量纲分析师导入结果表';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.PERIOD_ID IS '预测会计期';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.TARGET_CODE IS '预测步长编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.TARGET_DESC IS '预测步长描述';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.BG_CODE IS 'BG编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.BG_NAME IS 'BG名称';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.OVERSEA_CODE IS '区域编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.OVERSEA_DESC IS '区域';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LV1_CODE IS '重量级团队LV1编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LV1_NAME IS '重量级团队LV1描述';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LV2_CODE IS '重量级团队LV2编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LV2_NAME IS '重量级团队LV2名称';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.CURRENCY IS '币种';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.EQUIP_REV_AMT IS '设备收入额';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.MGP_RATIO IS '制毛率';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.STATUS IS '状态（SUBMIT 提交、IMPORT导入）';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.REMARK IS '备注';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.CREATED_BY IS '创建人';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.CREATION_DATE IS '创建时间';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LAST_UPDATED_BY IS '修改人';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.LAST_UPDATE_DATE IS '修改时间';
COMMENT ON COLUMN DM_FOP_DIMENSION_GROUP_ANALYSTS_RESULT_T.DEL_FLAG IS '是否删除';