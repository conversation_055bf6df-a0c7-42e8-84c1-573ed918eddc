DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I;
CREATE TABLE FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I (
    ROW_ID BIGSERIAL NOT NULL PRIMARY KEY,
    ID NUMERIC,
    PERIOD NVARCHAR2(30),
    BG_CN NVARCHAR2(512),
    LV1_INDUSTRY_CATG_CODE NVARCHAR2(50),
    UOM NVARCHAR2(100),
    DEMISION_FLAG NVARCHAR2(10),
    NO_ATTR4 NVARCHAR2(500),
    DIMENSION_SUBCATEGORY_CN_NAME NVARCHAR2(2000),
    DIMENSION_SUBCATEGORY_EN_NAME NVARCHAR2(2000),
    CN_DIMENSION_GROUP_L2_CODE NVARCHAR2(50),
    CN_DIMENSION_GROUP_L2 NVARCHAR2(100),
    DW_LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
    MONTH NVARCHAR2(10),
    LV2_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    NO_ATTR2 NVARCHAR2(500),
    FREQUENCY_BAND NVARCHAR2(1000),
    DIMENSION_SUB_DETAIL_EN_NAME NVARCHAR2(2000),
    MEASURE_CODE NVARCHAR2(100),
    LV3_ORG_CN NVARCHAR2(512),
    LV0_INDUSTRY_CATG_CODE NVARCHAR2(50),
    LV2_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV3_INDUSTRY_CATG_CODE NVARCHAR2(50),
    DIMENSION_KEY NUMERIC,
    INDUSTRY_CATG_CODE NVARCHAR2(100),
    DIMENSION_TYPE NVARCHAR2(500),
    PRODUCT_DIMENSION_CODE NVARCHAR2(500),
    INDUSTRY_DIMENSION_EN_NAME NVARCHAR2(3000),
    DIMENSION_SUBCATEGORY_CODE NVARCHAR2(500),
    CN_DIMENSION_GROUP_L1_CODE NVARCHAR2(50),
    LV2_ORG_CN NVARCHAR2(512),
    LV0_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    ATTR1 NVARCHAR2(128),
    ATTR2 NVARCHAR2(128),
    ATTR4 NVARCHAR2(128),
    COUNTRY_MEASURE NVARCHAR2(10),
    PRODUCT_DIMENSION_EN_NAME NVARCHAR2(2000),
    INDUSTRY_DIMENSION_CN_NAME NVARCHAR2(3000),
    PERIOD_FLAG NVARCHAR2(65),
    LV1_ORG_CN NVARCHAR2(512),
    LV0_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV1_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV1_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    LV2_INDUSTRY_CATG_CODE NVARCHAR2(50),
    LV3_INDUSTRY_CATG_CN_NAME NVARCHAR2(600),
    LV3_INDUSTRY_CATG_EN_NAME NVARCHAR2(600),
    ATTR3 NVARCHAR2(128),
    NO_ATTR3 NVARCHAR2(500),
    NO_UOM NVARCHAR2(500),
    PRODUCT_DIMENSION_CN_NAME NVARCHAR2(2000),
    INDUSTRY_DIMENSION_CODE NVARCHAR2(600),
    DIMENSION_SUB_DETAIL_CODE NVARCHAR2(500),
    DIMENSION_SUB_DETAIL_CN_NAME NVARCHAR2(2000),
    QTY NUMERIC,
    CN_DIMENSION_GROUP_L1 NVARCHAR2(100),
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
)
WITH (ORIENTATION=ROW)
DISTRIBUTE BY HASH(ROW_ID);
COMMENT ON TABLE DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I IS 'ICT产业产品量纲SOP计划量接口表';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ROW_ID IS '主键ID';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ID IS 'SOP计划信息主键ID';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.PERIOD IS '期次';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.BG_CN IS 'BG名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV1_INDUSTRY_CATG_CODE IS '一级产业目录编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.UOM IS '单位';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DEMISION_FLAG IS '融合标识(Y/N)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.NO_ATTR4 IS '量纲子类明细编码_四级业务包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUBCATEGORY_CN_NAME IS '量纲子类中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUBCATEGORY_EN_NAME IS '量纲子类英文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.CN_DIMENSION_GROUP_L2_CODE IS '中国区CNBG量纲分组L2编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.CN_DIMENSION_GROUP_L2 IS '中国区CNBG量纲分组L2';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DW_LAST_UPDATE_DATE IS 'DW最后更新时间';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.MONTH IS '预测月份';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV2_INDUSTRY_CATG_EN_NAME IS '二级产业目录名称(英文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.NO_ATTR2 IS '量纲子类编码_二级计委包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.FREQUENCY_BAND IS '频段';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUB_DETAIL_EN_NAME IS '量纲子类明细英文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.MEASURE_CODE IS '维度';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV3_ORG_CN IS '三级产品研发团队中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV0_INDUSTRY_CATG_CODE IS '零级产业目录编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV2_INDUSTRY_CATG_CN_NAME IS '二级产业目录名称(中文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV3_INDUSTRY_CATG_CODE IS '三级产业目录编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_KEY IS '量纲KEY';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.INDUSTRY_CATG_CODE IS '量纲归属产业编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_TYPE IS '量纲类型';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.PRODUCT_DIMENSION_CODE IS '产品量纲编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.INDUSTRY_DIMENSION_EN_NAME IS '产业量纲英文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUBCATEGORY_CODE IS '量纲子类编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.CN_DIMENSION_GROUP_L1_CODE IS '中国区CNBG量纲分组L1编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV2_ORG_CN IS '二级产品研发团队中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV0_INDUSTRY_CATG_CN_NAME IS '零级产业目录名称(中文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ATTR1 IS '一级计委包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ATTR2 IS '二级计委包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ATTR4 IS '四级业务包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.COUNTRY_MEASURE IS '国内海外标识（国内、海外）';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.PRODUCT_DIMENSION_EN_NAME IS '产品量纲英文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.INDUSTRY_DIMENSION_CN_NAME IS '产业量纲中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.PERIOD_FLAG IS '期次标识';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV1_ORG_CN IS '一级产品研发团队中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV0_INDUSTRY_CATG_EN_NAME IS '零级产业目录名称(英文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV1_INDUSTRY_CATG_CN_NAME IS '一级产业目录名称(中文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV1_INDUSTRY_CATG_EN_NAME IS '一级产业目录名称(英文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV2_INDUSTRY_CATG_CODE IS '二级产业目录编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV3_INDUSTRY_CATG_CN_NAME IS '三级产业目录名称(中文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LV3_INDUSTRY_CATG_EN_NAME IS '三级产业目录名称(英文)';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.ATTR3 IS '三级计委包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.NO_ATTR3 IS '量纲子类编码_三级计委包';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.NO_UOM IS '量纲编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.PRODUCT_DIMENSION_CN_NAME IS '产品量纲中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.INDUSTRY_DIMENSION_CODE IS '产业量纲编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUB_DETAIL_CODE IS '量纲子类明细编码';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.DIMENSION_SUB_DETAIL_CN_NAME IS '量纲子类明细中文名称';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.QTY IS '计划量';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.CN_DIMENSION_GROUP_L1 IS '中国区CNBG量纲分组L1';
COMMENT ON COLUMN DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I.LAST_UPDATE_DATE IS 'FOP最后更新日期';