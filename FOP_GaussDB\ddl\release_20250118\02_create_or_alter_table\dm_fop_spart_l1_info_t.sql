-- ----------------------------
-- Table structure for dm_fop_spart_l1_info_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_spart_l1_info_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_spart_l1_info_t" (
  "period_id" numeric,
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "ship_qty" numeric(38,10),
  "spart_qty" numeric(38,10),
  "equip_rev_cons_before_amt" numeric(38,10),
  "equip_cost_cons_before_amt" numeric(38,10),
  "equip_rev_cons_after_amt" numeric(38,10),
  "equip_cost_cons_after_amt" numeric(38,10),
  "mgp_ratio" numeric(38,10),
  "mca_adjust_ratio" numeric(38,10),
  "mgp_adjust_ratio" numeric(38,10),
  "carryover_ratio" numeric(38,10),
  "unit_cost" numeric(38,10),
  "unit_price" numeric(38,10),
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."lv2_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."lv2_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."ship_qty" IS '发货量（历史）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."spart_qty" IS '收入量（历史）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."equip_rev_cons_before_amt" IS '设备收入额（对价前）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."equip_cost_cons_before_amt" IS '设备成本额（对价前）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."equip_rev_cons_after_amt" IS '设备收入额（对价后）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."equip_cost_cons_after_amt" IS '设备成本额（对价后）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."mgp_ratio" IS '制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."mca_adjust_ratio" IS 'MCA调整率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."mgp_adjust_ratio" IS '制毛调整率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."carryover_ratio" IS '结转率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."unit_cost" IS '单位成本';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."unit_price" IS '单位价格';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."articulation_flag" IS '勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_l1_info_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_spart_l1_info_t" IS '作业对象L1层级数据';

