-- ----------------------------
-- Table structure for dm_fop_snop_year_budget_sum_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t" (
  "month" numeric NOT NULL,
  "period" varchar(500) COLLATE "pg_catalog"."default",
  "item" varchar(500) COLLATE "pg_catalog"."default",
  "description" varchar(2000) COLLATE "pg_catalog"."default",
  "plan_quantity" numeric(38,10),
  "coa_no" varchar(500) COLLATE "pg_catalog"."default",
  "bg_code" varchar(100) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(1000) COLLATE "pg_catalog"."default",
  "busi_lv4" varchar(1000) COLLATE "pg_catalog"."default",
  "port_qty" numeric(38,10),
  "remark" text COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "version_code" varchar(100) COLLATE "pg_catalog"."default",
  "plan_unit_quantity" numeric,
  "bg_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "region_code" varchar(300) COLLATE "pg_catalog"."default",
  "region_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "region_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "rep_office_code" varchar(300) COLLATE "pg_catalog"."default",
  "rep_office_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "rep_office_en_name" varchar(1000) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."month" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."period" IS '年度计划期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."item" IS '物料编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."description" IS '物料描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."plan_quantity" IS 'SNOP月计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."coa_no" IS 'COA编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."bg_cn_name" IS 'BG中文名';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv0_prod_rnd_team_code" IS '重量级团队LV0编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv0_prod_rd_team_cn_name" IS '重量级团队LV0中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv0_prod_rd_team_en_name" IS '重量级团队LV0英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv1_prod_rnd_team_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv1_prod_rd_team_cn_name" IS '重量级团队LV1中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv2_prod_rnd_team_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv2_prod_rd_team_cn_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv2_prod_rd_team_en_name" IS '重量级团队LV2中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv3_prod_rnd_team_code" IS '重量级团队LV3编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv3_prod_rd_team_cn_name" IS '重量级团队LV3中文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."lst_lv3_prod_rd_team_en_name" IS '重量级团队LV3英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."plan_com_lv1" IS '一级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."plan_com_lv2" IS '二级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."plan_com_lv3" IS '三级计委包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."busi_lv4" IS '四级业务包';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."port_qty" IS '端口数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."del_flag" IS '是否删除';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."version_code" IS '版本编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."plan_unit_quantity" IS '计划单元计划量';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."bg_en_name" IS 'BG英文名称 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."region_code" IS '地区部编码  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."region_cn_name" IS '地区部中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."region_en_name" IS '地区部英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."rep_office_code" IS '代表处编码  ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."rep_office_cn_name" IS '代表处中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t"."rep_office_en_name" IS '代表处英文名称';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_snop_year_budget_sum_t" IS '产品线年度S&OP预算汇总表';

