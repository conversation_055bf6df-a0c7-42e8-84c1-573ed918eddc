CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UPSTREAM_BACKUP(OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
/***********************************************************************************************************************************************************************
创建时间：2025-07-23
创建人  ：周博孝
背景描述：基线数据备份函数，备份基线相关表数据
参数描述：
          参数一(X_SUCCESS_FLAG):返回状态 SUCCESS/FAIL
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UPSTREAM_BACKUP()
***********************************************************************************************************************************************************************/

DECLARE
    V_SP_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UPSTREAM_BACKUP()';
    V_TBL_NAME VARCHAR(100) := '量纲上游数据备份表';
    V_DML_ROW_COUNT NUMBER DEFAULT 0;
    V_SQL TEXT;

BEGIN
    X_SUCCESS_FLAG := 'SUCCESS';

    -- 开始记录日志
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,
        P_LOG_SP_NAME => V_SP_NAME,
        P_LOG_PARA_LIST => '',
        P_LOG_STEP_NUM  => 1,
        P_LOG_CAL_LOG_DESC => '基线数据备份开始运行',
        P_LOG_FORMULA_SQL_TXT => NULL,
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL
    );


    -- 动态SQL执行清空备份表和重新备份
    V_SQL := 'DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I_BACKUP;
    DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_BS_PS_BV_CARRYOVER_RATE_I_BACKUP;
    DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I_BACKUP;
    DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_COST_DCP_I_BACKUP;
    DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I_BACKUP;
    
    CREATE TABLE FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I_BACKUP AS SELECT * FROM FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I;
    CREATE TABLE FIN_DM_OPT_FOP.DWK_BS_PS_BV_CARRYOVER_RATE_I_BACKUP AS SELECT * FROM FIN_DM_OPT_FOP.DWK_BS_PS_BV_CARRYOVER_RATE_I;
    CREATE TABLE FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I_BACKUP AS SELECT * FROM FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I;
    CREATE TABLE FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_COST_DCP_I_BACKUP AS SELECT * FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_COST_DCP_I;
    CREATE TABLE FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I_BACKUP AS SELECT * FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I;';

    EXECUTE V_SQL;

    -- 写结束日志
    PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,
        P_LOG_SP_NAME => V_SP_NAME,
        P_LOG_PARA_LIST => '',
        P_LOG_STEP_NUM  => 2,
        P_LOG_CAL_LOG_DESC => '基线数据备份结束运行',
        P_LOG_FORMULA_SQL_TXT => NULL,
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL
    );

EXCEPTION
    WHEN OTHERS THEN
        X_SUCCESS_FLAG := 'FAIL';
        PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
            P_LOG_VERSION_ID => NULL,
            P_LOG_SP_NAME => V_SP_NAME,
            P_LOG_PARA_LIST => '',
            P_LOG_STEP_NUM  => 999,
            P_LOG_CAL_LOG_DESC => '基线数据备份执行异常',
            P_LOG_FORMULA_SQL_TXT => SQLERRM,
            P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
            P_LOG_ERRBUF => SQLSTATE
        );

END;
$BODY$
  LANGUAGE PLPGSQL VOLATILE
  COST 100;