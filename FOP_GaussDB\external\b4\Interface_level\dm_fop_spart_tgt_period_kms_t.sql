-- Step 2：数据提取

标签名称：H_Q_tmp     数据源：fin_dm_opt_fop_uat
select distinct
         version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , equip_rev_cons_before_amt
         , equip_cost_cons_before_amt
         , spart_qty
         , case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'06' then 'H1'
	            when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'12' then 'H2'
	       end as H1_H2	      
	     , case when period_id between substr(period_id,1,4)||'01' and substr(period_id,1,4)||'03' then 'Q1'
	            when period_id between substr(period_id,1,4)||'04' and substr(period_id,1,4)||'06' then 'Q2'
			    when period_id between substr(period_id,1,4)||'07' and substr(period_id,1,4)||'09' then 'Q3'
			    when period_id between substr(period_id,1,4)||'10' and substr(period_id,1,4)||'12' then 'Q4'
	       end as Q1_Q4								   
      from fin_dm_opt_fop.dm_fop_spart_data_his_kms_t
     where version_code = '${V_MAX_VERSION_CODE}' 
	 ;
	 
	 -- Step 3：SQL-Script
	 
	 cache lazy table spart_info_ytd_tmp
  as
   select distinct 
	       version_code
         , cast(period_id as int) as period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_rev_cons_before_amt
         , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_cost_cons_before_amt
         , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as spart_qty
		 , cast(period_id as int)||'YTD' as target_period										   
      from H_Q_tmp
union all

    select distinct 
	       version_code
         , cast(period_id as int) as period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_rev_cons_before_amt
         , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_cost_cons_before_amt
         , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as spart_qty
		 , substr(period_id,1,4) as target_period										   
      from H_Q_tmp
union all	  

    select distinct 
	       version_code
         , cast(period_id as int) as period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),H1_H2,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_rev_cons_before_amt
         , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),H1_H2,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_cost_cons_before_amt
         , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),H1_H2,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as spart_qty
		 , cast(substr(period_id,1,4)as int)||H1_H2 as target_period										   
      from H_Q_tmp
union all	  

    select distinct 
	       version_code
         , cast(period_id as int) as period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),Q1_Q4,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_rev_cons_before_amt
         , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),Q1_Q4,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as equip_cost_cons_before_amt
         , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),Q1_Q4,spart_code,bg_code,oversea_desc
                                                         ,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name,l2_name,l2_coefficient,currency
                                                   order by period_id) as spart_qty
		 , cast(substr(period_id,1,4) as int)||Q1_Q4 as target_period										   
      from H_Q_tmp	
      ;	  
	  
	cache lazy table l2_info_ytd_tmp
  as
   select distinct version_code
           , cast(period_id as int) as period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_rev_cons_before_amt
           , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_cost_cons_before_amt
           , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as spart_qty
		   , cast(period_id as int)||'YTD' as target_period										 
        from H_Q_tmp
union all

    select distinct version_code
           , cast(period_id as int) as period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_rev_cons_before_amt
           , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_cost_cons_before_amt
           , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as spart_qty
		   , cast(substr(period_id,1,4)as int) as target_period										 
        from H_Q_tmp
union all

    select distinct version_code
           , cast(period_id as int) as period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_rev_cons_before_amt
           , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_cost_cons_before_amt
           , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),H1_H2,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as spart_qty
		   , cast(substr(period_id,1,4)as int)||H1_H2 as target_period										 
        from H_Q_tmp
union all

    select distinct version_code
           , cast(period_id as int) as period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_rev_cons_before_amt
           , sum(equip_cost_cons_before_amt) over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as equip_cost_cons_before_amt
           , sum(spart_qty) over(partition by version_code,substr(period_id,1,4),Q1_Q4,bg_code,oversea_desc,lv1_prod_rnd_team_code
                                                              ,lv2_prod_rnd_team_code,l1_name,l2_name,currency
                                                     order by period_id) as spart_qty
		   , cast(substr(period_id,1,4)as int)||Q1_Q4 as target_period										 
        from H_Q_tmp		
		;
		
	cache lazy table all_temp
  as	
  select t1.version_code
         , t1.period_id
         , t1.spart_code
         , t1.spart_desc
         , t1.bg_code
         , t1.bg_cn_name
         , t1.bg_en_name
         , t1.oversea_desc
         , t1.lv1_prod_rnd_team_code
         , t1.lv1_prod_rd_team_cn_name
         , t1.lv1_prod_rd_team_en_name
         , t1.lv2_prod_rnd_team_code
         , t1.lv2_prod_rd_team_cn_name
         , t1.lv2_prod_rd_team_en_name
         , t1.l1_name
         , t1.l2_name
         , t1.l2_coefficient
         , t1.currency
         , t1.articulation_flag
         , t1.equip_rev_cons_before_amt
         , t1.equip_cost_cons_before_amt
         , t1.spart_qty
         , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
	  			       when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
	  			       else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt end) as rev_percent	  
	  	   , (case when nvl(t2.equip_cost_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
	  			       when nvl(t2.equip_cost_cons_before_amt,0) = 0 then -999999
	  			       else t1.equip_cost_cons_before_amt / t2.equip_cost_cons_before_amt end) as cost_percent	
	  	   , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 0
	  			       when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
	  			       else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt end) as mgp_ratio	
         , (case when nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
	               when nvl(t1.spart_qty,0) = 0 then -999999
	               else t1.equip_cost_cons_before_amt / t1.spart_qty end) as avg_cost	
	  	   , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
                 when nvl(t1.spart_qty,0) = 0 then -999999
                 else t1.equip_rev_cons_before_amt / t1.spart_qty end) as avg_price
         , t1.target_period
	  from spart_info_ytd_tmp t1
      left join l2_info_ytd_tmp t2
        on t1.version_code = t2.version_code
	  	 and t1.period_id = t2.period_id
	     and t1.bg_code = t2.bg_code
	     and t1.oversea_desc = t2.oversea_desc
	     and t1.lv1_prod_rnd_team_code = t2.lv1_prod_rnd_team_code
	     and t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code
	     and t1.l1_name = t2.l1_name
	     and t1.l2_name = t2.l2_name
	     and t1.currency = t2.currency
	     and t1.articulation_flag = t2.articulation_flag
		 and t1.target_period = t2.target_period
		 ;
		 
     select version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_amt
         , equip_cost_cons_before_amt
         , spart_qty
         , avg_cost
         , avg_price
         , rev_percent
         , cost_percent
         , mgp_ratio
         , articulation_flag
		 , target_period
	  	 , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    from all_temp
      ;
	  
	  	-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_tgt_period_kms_t      模式：TRUNCATE_TABLE


数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_tgt_period_his_kms_t  模式：DELETE
                                              删除条件：version_code = '${V_MAX_VERSION_CODE}'