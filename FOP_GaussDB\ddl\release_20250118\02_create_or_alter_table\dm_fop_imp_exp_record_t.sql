-- ----------------------------
-- Table structure for dm_fop_imp_exp_record_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_imp_exp_record_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_imp_exp_record_t" (
  "id" numeric,
  "file_name" varchar(200) COLLATE "pg_catalog"."default",
  "file_size" varchar(50) COLLATE "pg_catalog"."default",
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "exception_feedback" varchar(500) COLLATE "pg_catalog"."default",
  "record_num" numeric,
  "created_by" varchar(200) COLLATE "pg_catalog"."default",
  "creation_date" timestamp(6),
  "end_date" timestamp(6),
  "last_updated_by" varchar(200) COLLATE "pg_catalog"."default",
  "last_update_date" timestamp(6),
  "del_flag" varchar(2) COLLATE "pg_catalog"."default",
  "period_id" varchar(50) COLLATE "pg_catalog"."default",
  "file_source_key" varchar(100) COLLATE "pg_catalog"."default",
  "file_error_key" varchar(100) COLLATE "pg_catalog"."default",
  "page_module" varchar(200) COLLATE "pg_catalog"."default",
  "opt_type" varchar(50) COLLATE "pg_catalog"."default",
  "rec_sts" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."id" IS '主键               ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."file_name" IS '文件名称                 ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."file_size" IS '文件大小             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."status" IS '状态（Save：成功、Submit：失败）            ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."exception_feedback" IS '异常反馈              ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."record_num" IS '记录条数    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."created_by" IS '创建人               ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."creation_date" IS '创建时间             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."end_date" IS '结束时间    ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."last_updated_by" IS '修改人               ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."last_update_date" IS '修改时间             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."del_flag" IS '是否删除             ';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."period_id" IS '版本';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."file_source_key" IS '源文件';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."file_error_key" IS '错误文件';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."page_module" IS '页面模块';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."opt_type" IS '操作类型：导入IMP  导出EXP';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_imp_exp_record_t"."rec_sts" IS '导入导出状态（OK：成功、FAIL：失败）    ';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_imp_exp_record_t" IS '导出记录表';

