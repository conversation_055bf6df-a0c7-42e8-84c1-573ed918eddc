-- ----------------------------
-- Table structure for dm_fop_group_analysts_result_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_group_analysts_result_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_group_analysts_result_t" (
  "version_code" varchar(50) COLLATE "pg_catalog"."default",
  "target_code" varchar(50) COLLATE "pg_catalog"."default",
  "target_desc" varchar(100) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_amt" numeric(38,10),
  "mgp_ratio" numeric(38,10),
  "status" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."version_code" IS '版本（年月）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."target_code" IS '预测步长编码（Y 年度、M月度、Q 季度、H 半年度）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."target_desc" IS '预测步长描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."oversea_desc" IS '区域（包括全球、国内、海外）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."lv1_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."lv1_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."lv2_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."lv2_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."equip_rev_amt" IS '设备收入';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."mgp_ratio" IS '制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."status" IS '状态（SUBMIT 提交、IMPORT导入）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_group_analysts_result_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_group_analysts_result_t" IS '集团分析师导入结果表';

