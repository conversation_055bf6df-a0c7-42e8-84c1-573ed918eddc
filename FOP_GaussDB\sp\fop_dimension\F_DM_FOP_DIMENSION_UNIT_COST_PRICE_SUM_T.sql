CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING, OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲均本均价汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
		  参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T();
*/
 
 DECLARE
	V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T('''||P_VERSION_CODE||')';
	V_TBL_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T';
	V_VERSION_CODE VARCHAR(50);  -- 目标表的版本编码，格式：年月_001
	V_MAX_VERSION_CODE VARCHAR(50);  -- 版本表的最大版本编码，格式：年月_001
	V_VERSION_MONTH VARCHAR(50);  -- 目标表的当前版本年月，格式：年月
	V_BEGIN_MONTH VARCHAR(50);  -- 目标表的起始年月，格式：年月
	V_STEP_NUM   NUMERIC; --步骤号
	V_DML_ROW_COUNT  NUMBER DEFAULT 0 ;


BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';                                 --1表示成功
	

    SELECT MAX(VERSION_CODE) INTO V_MAX_VERSION_CODE
	 FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
	 WHERE STEP = 1
	 ;
	 
	  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '版本表最大版本编码：'||V_MAX_VERSION_CODE,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

	 -- 如果是传 VERSION_CODE 调函数取 传入的 P_VERSION_CODE ，如果是自动调度的 当版本表有当月版本，则当月版本+1，当版本表没有当月版本，则当前年月_001
	 IF P_VERSION_CODE IS NOT NULL 
	 THEN
	 SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
	 ELSEIF  P_VERSION_CODE IS  NULL AND  SUBSTR(V_MAX_VERSION_CODE,1,6) = TO_CHAR(CURRENT_DATE,'YYYYMM')
	 THEN  
	 SELECT SUBSTR(V_MAX_VERSION_CODE,1,9)||TO_CHAR(SUBSTR(V_MAX_VERSION_CODE,10)::NUMERIC+1) INTO V_VERSION_CODE ;
	 ELSE 
	 SELECT TO_CHAR(CURRENT_DATE,'YYYYMM')||'_001'  INTO V_VERSION_CODE ;
	 END IF 
	 ;

	   --往版本信息表记录本次版本号
	   INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T(
	          VERSION_CODE
             ,STEP
             ,REMARK
             ,CREATED_BY
             ,CREATION_DATE
             ,LAST_UPDATED_BY
             ,LAST_UPDATE_DATE
             ,DEL_FLAG
			 )
	   SELECT V_VERSION_CODE AS VERSION_CODE
             ,2 AS STEP
             , '版本生成' AS REMARK
          	 , -1 AS CREATED_BY
          	 , CURRENT_TIMESTAMP AS CREATION_DATE
          	 , -1 AS LAST_UPDATED_BY
          	 , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
          	 , 'N' AS DEL_FLAG
         ;
		
		
		-- 当前版本所在的年月
		 SELECT SUBSTR(V_VERSION_CODE,1,6) INTO V_VERSION_MONTH ;
		 
		 -- 取集成表PERIOD_ID<系统当前年月的数据
       SELECT TO_CHAR(ADD_MONTHS(TO_DATE(V_VERSION_MONTH,'YYYYMM'),-61),'YYYYMM')  INTO V_BEGIN_MONTH;

		  
	 --1.开始日志
  V_STEP_NUM := 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '盈利量纲均本均价汇总表'||V_TBL_NAME||'，版本编码:'||V_VERSION_CODE||'，版本年月:'||V_VERSION_MONTH||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
  
  
	
		DROP TABLE IF EXISTS UNIT_PRODUCT_TMP;
	     CREATE TEMPORARY TABLE  UNIT_PRODUCT_TMP
		          AS
		SELECT DISTINCT 
              LV1_PROD_RND_TEAM_CODE
              ,LV1_PROD_RD_TEAM_CN_NAME
              ,LV1_PROD_RD_TEAM_EN_NAME
              ,LV2_PROD_RND_TEAM_CODE
              ,LV2_PROD_RD_TEAM_CN_NAME
              ,LV2_PROD_RD_TEAM_EN_NAME
		FROM  DMDIM.DM_DIM_PRODUCT_D
		WHERE LV1_PROD_RND_TEAM_CODE IN ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网
										  ;
										  
		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '产品维表：UNIT_PRODUCT_TMP 目标维度的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
								  
		
		DROP TABLE IF EXISTS UNIT_COST_TMP;
	    CREATE TEMPORARY TABLE  UNIT_COST_TMP
		          AS 
		SELECT T1.ATTR_GROUP_CODE  
              ,T1.TIME_WINDOW_CODE		
              ,T1.STAT_PERIOD_ID                 
              ,T1.START_PERIOD_ID                
              ,T1.END_PERIOD_ID                  
              ,CASE WHEN ATTR_GROUP_CODE = '004' OR  ATTR_GROUP_CODE = '006'   OR  ATTR_GROUP_CODE = '008'  OR  ATTR_GROUP_CODE = '010' 
			        THEN 'PROD0002' 
					ELSE T1.LV0_PROD_LIST_CODE
                     END AS BG_CODE                  
              ,CASE WHEN ATTR_GROUP_CODE = '004' OR  ATTR_GROUP_CODE = '006'   OR  ATTR_GROUP_CODE = '008'  OR  ATTR_GROUP_CODE = '010' 
			        THEN 'ICT' 
					ELSE T1.LV0_PROD_LIST_CN_NAME     
					END AS BG_NAME  
       		  ,T2.LV1_PROD_RND_TEAM_CODE
              ,T2.LV1_PROD_RD_TEAM_CN_NAME
              ,T2.LV1_PROD_RD_TEAM_EN_NAME	  
              ,T1.LV2_PROD_RND_TEAM_CODE         
              ,T1.LV2_PROD_RD_TEAM_EN_NAME       
              ,T1.LV2_PROD_RD_TEAM_CN_NAME 
              ,CASE WHEN ATTR_GROUP_CODE = '011' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			         THEN T1.DIMENSION_GROUP_CODE_L2  
                     WHEN ATTR_GROUP_CODE IN ( '004','005','006','007')	
                     THEN NULL				
      			     ELSE T1.PRODUCT_DIMENSION_GROUP_CODE	
                     END AS DIMENSION_GROUP_CODE    --量纲分组-                                   
               ,CASE WHEN ATTR_GROUP_CODE = '011' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			         THEN T1.DIMENSION_GROUP_L2_CN_NAME   
                     WHEN ATTR_GROUP_CODE IN ( '004','005','006','007')	
                     THEN NULL						 
      			     ELSE T1.PRODUCT_DIMENSION_GROUP         
					 END AS DIMENSION_GROUP_CN_NAME  --量纲分组中文名                                      
               ,CASE WHEN ATTR_GROUP_CODE = '011' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			         THEN T1.DIMENSION_GROUP_L2_EN_NAME   
                     WHEN ATTR_GROUP_CODE IN ( '004','005','006','007')	
                     THEN NULL						 
      			     ELSE T1.PRODUCT_DIMENSION_GROUP_EN_NAME 
					 END AS DIMENSION_GROUP_EN_NAME   --量纲分组英文名-
              ,CASE WHEN ATTR_GROUP_CODE IN ( '004','005','006','007') 
			        THEN T1.DIMENSION_SUBCATEGORY_CODE
                    ELSE NULL
                    END AS	DIMENSION_SUBCATEGORY_CODE	--量纲子类		
              ,CASE WHEN ATTR_GROUP_CODE IN ( '004','005','006','007') 
			        THEN T1.DIMENSION_SUBCATEGORY_CN_NAME 
                    ELSE NULL
                    END AS	DIMENSION_SUBCATEGORY_CN_NAME	--量纲子类中文名 	
              ,CASE WHEN ATTR_GROUP_CODE IN ( '004','005','006','007') 
			        THEN T1.DIMENSION_SUBCATEGORY_EN_NAME 
                    ELSE NULL	
                    END AS	DIMENSION_SUBCATEGORY_EN_NAME	--量纲子类英文名 			
              ,CASE WHEN ATTR_GROUP_CODE = '004' OR  ATTR_GROUP_CODE = '005'   OR  ATTR_GROUP_CODE = '008'  OR  ATTR_GROUP_CODE = '009' 
			        THEN 'GH0001' 
					ELSE T1.DOMESTIC_OR_OVERSEA_CODE    
					END AS OVERSEA_CODE            --区域 
              ,CASE WHEN ATTR_GROUP_CODE = '004' OR  ATTR_GROUP_CODE = '005'   OR  ATTR_GROUP_CODE = '008'  OR  ATTR_GROUP_CODE = '009' 
			        THEN '全球' 
					ELSE T1.DOMESTIC_OR_OVERSEA_CNAME   
					END AS OVERSEA_CNAME           --区域中文名  	
              ,CASE WHEN ATTR_GROUP_CODE = '004' OR  ATTR_GROUP_CODE = '005'   OR  ATTR_GROUP_CODE = '008'  OR  ATTR_GROUP_CODE = '009' 
			        THEN 'GH0001' 
					ELSE T1.DOMESTIC_OR_OVERSEA_ENAME   
					END AS OVERSEA_ENAME          --区域英文名                         
              ,T1.CURRENCY_CODE    
              ,T1.BS_VALUE                       
              ,T1.BS_NUMERATOR_VALUE             
              ,T1.BS_DENOMINATOR_VALUE           
          FROM  FIN_DM_OPT_FOP.DWK_BS_PS_COST_UNIT_PRICE_DCP_I T1
		  JOIN  UNIT_PRODUCT_TMP T2 
		  ON T1.LV2_PROD_RND_TEAM_CODE = T2.LV2_PROD_RND_TEAM_CODE
          WHERE 1=1
		  AND T1.BS_CODE = 'BS_NPS_00000097'		  
          AND T1.ATTR_GROUP_CODE IN ( '004','005','006','007','008','009','010','011')	
		  AND TIME_WINDOW_CODE = 'YTD'	
		  AND CAST(T1.END_PERIOD_ID AS INT) >= CAST(V_BEGIN_MONTH AS INT)   -- 取近5年数据
		  AND CAST(T1.END_PERIOD_ID AS INT) < CAST(V_VERSION_MONTH AS INT)   -- 取集成表PERIOD_ID< 系统当前年月的数据
		  ;
		  
		   V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '均本来源表逻辑处理：UNIT_COST_TMP 数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

        DROP TABLE IF EXISTS UNIT_PRICE_TMP;
	    CREATE TEMPORARY TABLE  UNIT_PRICE_TMP
		          AS 
		SELECT                        
          T1.ATTR_GROUP_CODE  
         ,T1.TIME_WINDOW_CODE               
         ,T1.STAT_PERIOD_ID                 
         ,T1.START_PERIOD_ID                
         ,T1.END_PERIOD_ID      		 
         ,CASE WHEN ATTR_GROUP_CODE = '006' OR  ATTR_GROUP_CODE = '008'   OR  ATTR_GROUP_CODE = '014'  OR  ATTR_GROUP_CODE = '016' 
			   THEN 'PROD0002' 
			   ELSE T1.LV0_PROD_LIST_CODE        
			   END AS BG_CODE       
         ,CASE WHEN ATTR_GROUP_CODE = '006' OR  ATTR_GROUP_CODE = '008'   OR  ATTR_GROUP_CODE = '014'  OR  ATTR_GROUP_CODE = '016' 
			   THEN 'ICT' 
			   ELSE T1.LV0_PROD_LIST_CN_NAME     
			   END AS BG_NAME           
         ,T1.LV1_PROD_RND_TEAM_CODE         
         ,T1.LV1_PROD_RD_TEAM_EN_NAME       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME  
         ,CASE WHEN ATTR_GROUP_CODE = '017' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			   THEN T1.DIMENSION_GROUP_CODE_L2  
               WHEN ATTR_GROUP_CODE IN ( '006','007','008','009')	
               THEN NULL    			   
      		   ELSE T1.PRODUCT_DIMENSION_GROUP_CODE
               END AS DIMENSION_GROUP_CODE    --量纲分组                                    
         ,CASE WHEN ATTR_GROUP_CODE = '017' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			   THEN T1.DIMENSION_GROUP_L2_CN_NAME   
               WHEN ATTR_GROUP_CODE IN ( '006','007','008','009')	
               THEN NULL 			   
      		   ELSE T1.PRODUCT_DIMENSION_GROUP         
			    END AS DIMENSION_GROUP_CN_NAME    --量纲中文名                                  
         ,CASE WHEN ATTR_GROUP_CODE = '017' AND T1.LV0_PROD_LIST_CODE = 'PDCG901159' AND T1.DOMESTIC_OR_OVERSEA_CODE = 'GH0002' 
			   THEN T1.DIMENSION_GROUP_L2_EN_NAME 
               WHEN ATTR_GROUP_CODE IN ( '006','007','008','009')	
               THEN NULL 			   
      		   ELSE T1.PRODUCT_DIMENSION_GROUP_EN_NAME 
			   END AS DIMENSION_GROUP_EN_NAME 	  --量纲英文名   
         ,CASE WHEN ATTR_GROUP_CODE IN ( '006','007','008','009') 
			   THEN T1.DIMENSION_SUBCATEGORY_CODE  
               ELSE NULL
               END AS	DIMENSION_SUBCATEGORY_CODE	  --量纲子类    
         ,CASE WHEN ATTR_GROUP_CODE IN ( '006','007','008','009') 
			   THEN T1.DIMENSION_SUBCATEGORY_CN_NAME  
               ELSE NULL
               END AS DIMENSION_SUBCATEGORY_CN_NAME  --量纲子类中文名 
         ,CASE WHEN ATTR_GROUP_CODE IN ( '006','007','008','009') 
			   THEN T1.DIMENSION_SUBCATEGORY_EN_NAME  
               ELSE NULL
               END AS DIMENSION_SUBCATEGORY_EN_NAME --量纲子类英文名 
         ,CASE WHEN ATTR_GROUP_CODE = '006' OR  ATTR_GROUP_CODE = '007'   OR  ATTR_GROUP_CODE = '014'  OR  ATTR_GROUP_CODE = '015' 
			   THEN 'GH0001' 
			   ELSE T1.DOMESTIC_OR_OVERSEA_CODE        
			   END AS OVERSEA_CODE    --区域 
         ,CASE WHEN ATTR_GROUP_CODE = '006' OR  ATTR_GROUP_CODE = '007'   OR  ATTR_GROUP_CODE = '014'  OR  ATTR_GROUP_CODE = '015' 
			   THEN '全球' 
			   ELSE T1.DOMESTIC_OR_OVERSEA_CNAME       
			   END AS OVERSEA_CNAME   --区域中文名 
         ,CASE WHEN ATTR_GROUP_CODE = '006' OR  ATTR_GROUP_CODE = '007'   OR  ATTR_GROUP_CODE = '014'  OR  ATTR_GROUP_CODE = '015' 
			   THEN 'GH0001' 
			   ELSE T1.DOMESTIC_OR_OVERSEA_ENAME       
			   END AS OVERSEA_ENAME       --区域英文名            
         ,T1.CURRENCY_CODE                                                     
         ,T1.BS_VALUE                       
         ,T1.BS_DENOMINATOR_VALUE   --分母值    
         ,T1.BS_NUMERATOR_VALUE             		  
		FROM  FIN_DM_OPT_FOP.DWK_BS_PS_REV_UNIT_PRICE_I T1
		WHERE 1=1
		AND BS_CODE = 'BS_NPS_00000085'
		AND ATTR_GROUP_CODE IN ( '006','007','008','009','014','015','016','017')		
		AND TIME_WINDOW_CODE = 'YTD'	
		AND CAST(T1.END_PERIOD_ID AS INT) >= CAST(V_BEGIN_MONTH AS INT)   -- 取近5年数据
		  AND CAST(T1.END_PERIOD_ID AS INT) < CAST(V_VERSION_MONTH AS INT)   -- 取集成表PERIOD_ID< 系统当前年月的数据
		;
		
		  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '均价来源表逻辑处理：UNIT_PRICE_TMP 数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
  
         DROP TABLE IF EXISTS SCENARIOS_TMP;
	    CREATE TEMPORARY TABLE  SCENARIOS_TMP
		          AS 
        SELECT '量纲分组'  AS SCENARIOS
	     ,T1.TIME_WINDOW_CODE                                       
         ,T1.END_PERIOD_ID    AS PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,T1.OVERSEA_CNAME AS OVERSEA_DESC	 
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,CASE WHEN T1.CURRENCY_CODE = 'RMB' 
		       THEN 'CNY' 
			   ELSE T1.CURRENCY_CODE 
          END AS CURRENCY_CODE			   
         ,T1.BS_VALUE AS COST_UNIT_PRICE
         ,T2.BS_VALUE AS REV_UNIT_PRICE
		 ,T2.BS_DENOMINATOR_VALUE AS CARRYOVER_QTY
        FROM UNIT_COST_TMP T1	 
        JOIN UNIT_PRICE_TMP T2    
		ON  T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE                          
        AND T1.END_PERIOD_ID    = T2.END_PERIOD_ID      	 
        AND COALESCE(T1.BG_CODE                      ,'S1')    = COALESCE(T2.BG_CODE                      ,'S1')
		AND COALESCE(T1.BG_NAME                      ,'S1')    = COALESCE(T2.BG_NAME                      ,'S1')
        AND COALESCE(T1.OVERSEA_CODE                 ,'S1')    = COALESCE(T2.OVERSEA_CODE	              ,'S1')
		AND COALESCE(T1.OVERSEA_CNAME                ,'S1')    = COALESCE(T2.OVERSEA_CNAME	              ,'S1')
        AND COALESCE(T1.LV1_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV1_PROD_RND_TEAM_CODE       ,'S1')   
        AND COALESCE(T1.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')           
        AND COALESCE(T1.LV2_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV2_PROD_RND_TEAM_CODE       ,'S1')
		AND COALESCE(T1.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')
        AND COALESCE(T1.DIMENSION_GROUP_CODE         ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CODE         ,'S1')  
        AND COALESCE(T1.DIMENSION_GROUP_CN_NAME      ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CN_NAME      ,'S1')  		
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CODE   ,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CODE   ,'S1')       
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CN_NAME,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CN_NAME,'S1')  		
        AND COALESCE(T1.CURRENCY_CODE                ,'S1')    = COALESCE(T2.CURRENCY_CODE                ,'S1')
		WHERE T1.LV1_PROD_RND_TEAM_CODE IN ('134557','133277','137565')	   --光、计算、数据通信 
		AND T1.DIMENSION_SUBCATEGORY_CODE IS NULL
		AND T1.DIMENSION_GROUP_CODE IS NOT NULL
		UNION ALL
		 SELECT '量纲子类'  AS SCENARIOS
	     ,T1.TIME_WINDOW_CODE                                       
         ,T1.END_PERIOD_ID    AS PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,T1.OVERSEA_CNAME AS OVERSEA_DESC	 
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 	 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,CASE WHEN T1.CURRENCY_CODE = 'RMB' 
		       THEN 'CNY' 
			   ELSE T1.CURRENCY_CODE 
          END AS CURRENCY_CODE			   
         ,T1.BS_VALUE AS COST_UNIT_PRICE
         ,T2.BS_VALUE AS REV_UNIT_PRICE
		 ,T2.BS_DENOMINATOR_VALUE AS CARRYOVER_QTY
        FROM UNIT_COST_TMP T1	 
        JOIN UNIT_PRICE_TMP T2    
		ON  T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE                          
        AND T1.END_PERIOD_ID    = T2.END_PERIOD_ID      	 
        AND COALESCE(T1.BG_CODE                      ,'S1')    = COALESCE(T2.BG_CODE                      ,'S1')
		AND COALESCE(T1.BG_NAME                      ,'S1')    = COALESCE(T2.BG_NAME                      ,'S1')
        AND COALESCE(T1.OVERSEA_CODE                 ,'S1')    = COALESCE(T2.OVERSEA_CODE	              ,'S1')
		AND COALESCE(T1.OVERSEA_CNAME                ,'S1')    = COALESCE(T2.OVERSEA_CNAME	              ,'S1')
        AND COALESCE(T1.LV1_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV1_PROD_RND_TEAM_CODE       ,'S1')   
        AND COALESCE(T1.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')           
        AND COALESCE(T1.LV2_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV2_PROD_RND_TEAM_CODE       ,'S1')
		AND COALESCE(T1.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')
        AND COALESCE(T1.DIMENSION_GROUP_CODE         ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CODE         ,'S1')  
        AND COALESCE(T1.DIMENSION_GROUP_CN_NAME      ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CN_NAME      ,'S1')  		
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CODE   ,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CODE   ,'S1')       
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CN_NAME,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CN_NAME,'S1')  		
        AND COALESCE(T1.CURRENCY_CODE                ,'S1')    = COALESCE(T2.CURRENCY_CODE                ,'S1')
		WHERE T1.LV1_PROD_RND_TEAM_CODE IN ('100001','101775')	   --无线、数据存储 
		AND T1.DIMENSION_GROUP_CODE IS NULL
		AND T1.DIMENSION_SUBCATEGORY_CODE IS NOT NULL
		UNION ALL
		 SELECT 'LV2'  AS SCENARIOS
	     ,T1.TIME_WINDOW_CODE                                       
         ,T1.END_PERIOD_ID    AS PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,T1.OVERSEA_CNAME AS OVERSEA_DESC	 
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 	 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,CASE WHEN T1.CURRENCY_CODE = 'RMB' 
		       THEN 'CNY' 
			   ELSE T1.CURRENCY_CODE 
          END AS CURRENCY_CODE			   
         ,T1.BS_VALUE AS COST_UNIT_PRICE
         ,T2.BS_VALUE AS REV_UNIT_PRICE
		 ,T2.BS_DENOMINATOR_VALUE AS CARRYOVER_QTY
        FROM UNIT_COST_TMP T1	 
        JOIN UNIT_PRICE_TMP T2    
		ON  T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE                          
        AND T1.END_PERIOD_ID    = T2.END_PERIOD_ID      	 
        AND COALESCE(T1.BG_CODE                      ,'S1')    = COALESCE(T2.BG_CODE                      ,'S1')
	    AND COALESCE(T1.BG_NAME                      ,'S1')    = COALESCE(T2.BG_NAME                      ,'S1')
        AND COALESCE(T1.OVERSEA_CODE                 ,'S1')    = COALESCE(T2.OVERSEA_CODE	              ,'S1')
	    AND COALESCE(T1.OVERSEA_CNAME                ,'S1')    = COALESCE(T2.OVERSEA_CNAME	              ,'S1')
        AND COALESCE(T1.LV1_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV1_PROD_RND_TEAM_CODE       ,'S1')   
        AND COALESCE(T1.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV1_PROD_RD_TEAM_CN_NAME     ,'S1')           
        AND COALESCE(T1.LV2_PROD_RND_TEAM_CODE       ,'S1')    = COALESCE(T2.LV2_PROD_RND_TEAM_CODE       ,'S1')
	    AND COALESCE(T1.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')    = COALESCE(T2.LV2_PROD_RD_TEAM_CN_NAME     ,'S1')
        AND COALESCE(T1.DIMENSION_GROUP_CODE         ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CODE         ,'S1')  
        AND COALESCE(T1.DIMENSION_GROUP_CN_NAME      ,'S1')    = COALESCE(T2.DIMENSION_GROUP_CN_NAME      ,'S1')  		
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CODE   ,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CODE   ,'S1')       
        AND COALESCE(T1.DIMENSION_SUBCATEGORY_CN_NAME,'S1')    = COALESCE(T2.DIMENSION_SUBCATEGORY_CN_NAME,'S1')  		
        AND COALESCE(T1.CURRENCY_CODE                ,'S1')    = COALESCE(T2.CURRENCY_CODE                ,'S1')
		WHERE T1.LV1_PROD_RND_TEAM_CODE IN ('100011')	   -- 云核心网 
        AND T1.DIMENSION_SUBCATEGORY_CODE IS NULL
		AND T1.DIMENSION_GROUP_CODE IS NULL
		AND T1.LV2_PROD_RND_TEAM_CODE IS NOT NULL
		;
		
		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '场景判断：SCENARIOS_TMP 数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

        DROP TABLE IF EXISTS DIM_PRODUCTDIMENSION_TMP;
	    CREATE TEMPORARY TABLE  DIM_PRODUCTDIMENSION_TMP
		          AS 
		SELECT DISTINCT DD.PRODUCT_DIMENSION_GROUP_CODE AS DIMENSION_GROUP_CODE,                        
                        DD.PRODUCT_DIMENSION_GROUP AS DIMENSION_GROUP_CN_NAME,
						DD.PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME 
               FROM FIN_DM_OPT_FOP.DWR_DIM_PRODUCTDIMENSION_D DD
              WHERE DD.SCD_ACTIVE_IND = 1
                AND DD.DEL_FLAG = 'N'
				AND DD.DIMENSION_STATUS='PUBLISH'
				;
				
		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '量纲维表的量纲分组维度：DIM_PRODUCTDIMENSION_TMP 数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
				
		DROP TABLE IF EXISTS DIMENSION_GROUP_TMP;
	    CREATE TEMPORARY TABLE  DIMENSION_GROUP_TMP
		          AS
		SELECT T1.TIME_WINDOW_CODE                                       
         ,T1.PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,T1.OVERSEA_DESC
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 
         ,T1.SCENARIOS		 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,T1.CURRENCY_CODE			   
         ,T1.COST_UNIT_PRICE
         ,T1.REV_UNIT_PRICE
		 ,T1.CARRYOVER_QTY
        FROM SCENARIOS_TMP T1	
		JOIN DIM_PRODUCTDIMENSION_TMP T2
          ON T1.DIMENSION_GROUP_CODE = T2.DIMENSION_GROUP_CODE
         AND T1.DIMENSION_GROUP_CN_NAME	= T2.DIMENSION_GROUP_CN_NAME
		 WHERE T1.DIMENSION_GROUP_CODE IS NOT NULL
        UNION ALL 
      SELECT T1.TIME_WINDOW_CODE                                       
         ,T1.PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,T1.OVERSEA_DESC
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 
         ,T1.SCENARIOS		 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,T1.CURRENCY_CODE			   
         ,T1.COST_UNIT_PRICE
         ,T1.REV_UNIT_PRICE
		 ,T1.CARRYOVER_QTY
        FROM SCENARIOS_TMP T1	
        WHERE T1.DIMENSION_GROUP_CODE IS   NULL		 
		;
		
		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '关联量纲维表，解决一个CODE两个NAME的问题：DIMENSION_GROUP_TMP 数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		
		-- 删除最大版本数据
      DELETE FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T WHERE VERSION_CODE = V_VERSION_CODE;
	  
	  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T(	
	   VERSION_CODE     
      ,TIME_WINDOW_CODE
	  ,PERIOD_ID
      ,BG_CODE
      ,BG_NAME
	  ,OVERSEA_CODE
      ,OVERSEA_DESC
      ,LV1_PROD_RND_TEAM_CODE
      ,LV1_PROD_RD_TEAM_CN_NAME
      ,LV1_PROD_RD_TEAM_EN_NAME
      ,LV2_PROD_RND_TEAM_CODE
      ,LV2_PROD_RD_TEAM_CN_NAME
      ,LV2_PROD_RD_TEAM_EN_NAME
	  ,SCENARIOS
      ,DIMENSION_GROUP_CODE   
      ,DIMENSION_GROUP_CN_NAME        
      ,DIMENSION_GROUP_EN_NAME
      ,DIMENSION_SUBCATEGORY_CODE    
      ,DIMENSION_SUBCATEGORY_CN_NAME 
      ,DIMENSION_SUBCATEGORY_EN_NAME
      ,CURRENCY_CODE	  
      ,COST_UNIT_PRICE
      ,REV_UNIT_PRICE  
      ,CARRYOVER_QTY	  
      ,SOURCE_TABLE
      ,REMARK
      ,CREATED_BY
      ,CREATION_DATE
      ,LAST_UPDATED_BY
      ,LAST_UPDATE_DATE
      ,DEL_FLAG
	  )
	  SELECT V_VERSION_CODE   AS VERSION_CODE
	     ,T1.TIME_WINDOW_CODE                                       
         ,T1.PERIOD_ID     		 
         ,T1.BG_CODE       
         ,T1.BG_NAME 
		 ,T1.OVERSEA_CODE
         ,CASE WHEN T1.OVERSEA_CODE	= 'GH0002'  
		       THEN '国内' 
			   ELSE T1.OVERSEA_DESC
			   END AS OVERSEA_DESC
         ,T1.LV1_PROD_RND_TEAM_CODE                       
         ,T1.LV1_PROD_RD_TEAM_CN_NAME 
         ,T1.LV1_PROD_RD_TEAM_EN_NAME 		 
         ,T1.LV2_PROD_RND_TEAM_CODE         
         ,T1.LV2_PROD_RD_TEAM_CN_NAME       
         ,T1.LV2_PROD_RD_TEAM_EN_NAME 
         ,T1.SCENARIOS		 
         ,T1.DIMENSION_GROUP_CODE                                          
         ,T1.DIMENSION_GROUP_CN_NAME                                       
         ,T1.DIMENSION_GROUP_EN_NAME 	   
         ,T1.DIMENSION_SUBCATEGORY_CODE     
         ,T1.DIMENSION_SUBCATEGORY_CN_NAME  
         ,T1.DIMENSION_SUBCATEGORY_EN_NAME                     
         ,CASE WHEN T1.CURRENCY_CODE = 'RMB' 
		       THEN 'CNY' 
			   ELSE T1.CURRENCY_CODE 
          END AS CURRENCY_CODE			   
         ,T1.COST_UNIT_PRICE
         ,T1.REV_UNIT_PRICE
		 ,T1.CARRYOVER_QTY
		 ,'DWK_BS_PS_COST_UNIT_PRICE_DCP_I&DWK_BS_PS_REV_UNIT_PRICE_I' AS SOURCE_TABLE
         ,'' AS REMARK
 	     , -1 AS CREATED_BY
 	     , CURRENT_TIMESTAMP AS CREATION_DATE
 	     , -1 AS LAST_UPDATED_BY
 	     , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	     , 'N' AS DEL_FLAG	
        FROM DIMENSION_GROUP_TMP T1	
        WHERE OVERSEA_CODE NOT IN ('GH0004')	-- 只取 中国区 和 海外的数据，剔除其他的数据		        
		;
	
	V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量

      
		 --将版本信息表中的执行步骤改为：1 成功
		UPDATE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
		SET STEP = 1 
		WHERE VERSION_CODE = V_VERSION_CODE
		AND STEP = 2
		;
		
		
			-- 写结束日志
	     V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '生成的版本编码:'||V_VERSION_CODE||'结果表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	--收集统计信息
	ANALYSE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_UNIT_COST_PRICE_SUM_T;

--处理异常信息
	EXCEPTION
		WHEN OTHERS THEN
		
		 PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	X_SUCCESS_FLAG := 'FAIL';		        --2001表示失败
	
	
	 --将版本信息表中的执行步骤改为：2001 失败
		UPDATE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
		SET STEP = 2001 
		WHERE VERSION_CODE = V_VERSION_CODE
		AND STEP = 2
		;
	
 END;
 $BODY$
 LANGUAGE PLPGSQL VOLATILE
  COST 100

