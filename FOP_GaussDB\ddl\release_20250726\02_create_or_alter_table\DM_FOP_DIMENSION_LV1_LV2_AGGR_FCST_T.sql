/*LV1-LV2-设备收入与制毛率预测数(将聚合后的LV1和LV2预测表合成一张表给融合调优用)*/
DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T;
CREATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T(
 PERIOD_ID            NUMERIC,
 PHASE_DATE           VARCHAR(60),
 TARGET_PERIOD        VARCHAR(100),
 FCST_TYPE            VARCHAR(50),
 BG_CODE              VARCHAR(50),
 BG_NAME              VARCHAR(200),
 OVERSEA_CODE         VARCHAR(50),
 OVERSEA_DESC         VARCHAR(50),
 LV1_CODE             VARCHAR(100),
 LV1_NAME             VARCHAR(600),
 LV2_CODE             VARCHAR(100),
 LV2_NAME             VARCHAR(600),
 CURRENCY             VARCHAR(50),
 EQUIP_REV_AFTER_FCST NUMERIC(38,10),
 MGP_RATE_AFTER_FCST  NUMERIC(38,10),
 REMARK               VARCHAR(500),
 CREATED_BY           INT8,
 CREATION_DATE        TIMESTAMP,
 LAST_UPDATED_BY      INT8,
 LAST_UPDATE_DATE     TIMESTAMP,
 DEL_FLAG             VARCHAR(10)
) WITH (ORIENTATION = COLUMN,COMPRESSION = LOW, COLVERSION = 2.0, ENABLE_DELTA = FALSE)   
DISTRIBUTE BY HASH(PERIOD_ID,PHASE_DATE);

COMMENT ON TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T                       IS 'LV1-LV2-设备收入与制毛率预测数';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.PERIOD_ID            IS '会计期（年月）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.PHASE_DATE           IS 'SOP期次';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.TARGET_PERIOD        IS '预测时点';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.FCST_TYPE            IS '预测方法';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.BG_CODE              IS 'BG编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.BG_NAME              IS 'BG名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.OVERSEA_CODE         IS '区域编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.OVERSEA_DESC         IS '区域';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LV1_CODE             IS '重量级团队LV1编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LV1_NAME             IS '重量级团队LV1描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LV2_CODE             IS '重量级团队LV2编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LV2_NAME             IS '重量级团队LV2名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.CURRENCY             IS '币种';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.EQUIP_REV_AFTER_FCST IS '对价后设备收入预测_预测值';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.MGP_RATE_AFTER_FCST  IS '制毛率预测（对价后)_预测值';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.REMARK               IS '备注';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.CREATED_BY           IS '创建人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.CREATION_DATE        IS '创建时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LAST_UPDATED_BY      IS '修改人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.LAST_UPDATE_DATE     IS '修改时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV1_LV2_AGGR_FCST_T.DEL_FLAG             IS '是否删除';
