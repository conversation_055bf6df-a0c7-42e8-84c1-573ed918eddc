-- ----------------------------
-- Function structure for f_dm_ps_ai_forecast_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_ps_ai_forecast_t"("p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_ps_ai_forecast_t"(IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-5-30
创建人  ：qwx1110218
背景描述：ICT产业损益AI预测数_重量级团队LV2层级，上游数据从202306开始取
参数描述：参数一(p_period)：传入会计期(年月)
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_ps_ai_forecast_t(202306)
*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_dm_ps_ai_forecast_t('||p_period||')';
	v_tbl_name varchar(50) := 'fin_dm_opt_fop.dm_ps_ai_forecast_t';
	v_dml_row_count number default 0 ;
	v_max_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月_V1...VN
	v_version_code varchar(50);      -- 根据传入参数获取的版本编码，格式：当前年月_V1...VN


begin
	x_success_flag := '1';        --1表示成功

  -- 取最大版本
  select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t)
   group by substr(version_code,1,6)
  ;

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'ICT产业损益AI预测数_重量级团队LV2层级函数：'||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  if((p_period is not null or p_period <> '') and length(p_period) <> 6) then
   perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
       p_log_version_id => null,                 --版本
       p_log_sp_name => v_sp_name,    --sp名称
       p_log_para_list => '',--参数
       p_log_step_num  => 2,
       p_log_cal_log_desc => '传入参数：'||p_period||'，请传入正确的参数格式，参数格式：yyyymm ！',--日志描述
       p_log_formula_sql_txt => null,--错误信息
       p_log_row_count => v_dml_row_count,
       p_log_errbuf => null  --错误编码
     ) ;
   x_success_flag := 2001;
   return;
  
  -- 判断传入会计期不能小于202306 
  elseif((p_period is not null or p_period <> '') and p_period < 202306) then 
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数：'||p_period||'，传入参数不能小于202306，重新传入参数！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;
  
  -- 传入参数非空时，S&OP预测SUM成、宽表取传入年月的最大版本数据
  elseif (p_period is not null or p_period <> '') then
	  --判断m表是否存在传入会计期，然后delete数据
    if exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_t where period_id = p_period) then
		  --支持重跑，清除目标表传入会计期的数据
		  delete from fin_dm_opt_fop.dm_ps_ai_forecast_t where period_id = p_period;
    
      -- 通过传入参数取对应年月的最大版本
		  select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_version_code
        from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
       where substr(version_code,1,6) = p_period
       group by substr(version_code,1,6)
      ;
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数：'||p_period||'，根据传入参数年月取的最大版本：'||v_version_code,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    
		  --插入目标表数据
		  insert into fin_dm_opt_fop.dm_ps_ai_forecast_t(
		      period_id
        , phase_date
        , ver_lv1_code
        , ver_lv1_cn_name
        , ver_lv1_en_name
        , dste_scenario_lv1_code
        , dste_scenario_lv1_cn_name
        , dste_scenario_lv1_en_name
        , report_item_code
        , report_item_cn_name
        , report_item_en_name
        , report_item_fcst
        , report_item_fcst_conf
        , report_item_fcst_upper
        , report_item_fcst_lower
        , lv1_prod_rnd_team_code
        , lv1_prod_rd_team_cn_name
        , lv1_prod_rd_team_en_name
        , lv2_prod_rnd_team_code
        , lv2_prod_rd_team_cn_name
        , lv2_prod_rd_team_en_name
        , bg_code
        , bg_cn_name
        , bg_en_name
        , oversea_desc
        , target_period
        , fcst_type
        , currency
        , is_release_version_flag
        , remark
        , created_by
        , creation_date
        , last_updated_by
        , last_update_date
        , del_flag
		  )
		  -- 从S&OP预测SUM表取所有期次、版本发布标识
      with release_version_flag_tmp as(
      select distinct phase_date, oversea_desc, is_release_version_flag
        from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
       where version_code = v_version_code
      ),
      -- 从宽表取最大版本的LV1、LV2信息
      spart_detail_info_tmp as(
      select distinct bg_code
           , bg_name
           , bg_en_name
           , lv1_prod_rnd_team_code
           , lv1_prod_rd_team_cn_name
           , lv1_prod_rd_team_en_name
           , lv2_prod_rnd_team_code
           , lv2_prod_rd_team_cn_name
           , lv2_prod_rd_team_en_name
        from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
       where version_code = v_version_code
      ),
      -- 从维表取经营活动（预算、预测）、版本、经营报告报表项
      data_tree_p_c_node_tmp as(
      select distinct dim_code, node_code, node_cname, node_ename
        from dimmgt.dim_data_tree_p_c_node_i 
       where version_code = 'AF0001'
         and dim_code in('MEAM0002','MEAM0003','RPTI0001')   -- MEAM0002 经营活动  MEAM0003 版本  RPTI0001 经营报告报表项
         and node_code in('SNRT004','VR012','RPI_PL_91050','RPI_PL_10220')  -- SNRT004 预测  VR012 Auto版  RPI_PL_91050 设备收入   RPI_PL_10220 制造毛利率
      ),
      bg_tmp1 as(
      select distinct bg_code, bg_en_name from spart_detail_info_tmp
      ),
      bg_tmp as(
      select bg_code, bg_en_name
        from (select bg_code, bg_en_name, row_number() over(partition by bg_code order by bg_en_name) as rn from bg_tmp1 )t
       where rn = 1
      ),
      lv1_tmp1 as(
      select distinct lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name from spart_detail_info_tmp
      ),
      lv1_tmp as(
      select lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name
        from (
              select lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name
                   , row_number() over(partition by lv1_prod_rnd_team_code order by lv1_prod_rd_team_en_name) as rn
                from lv1_tmp1
             )t
       where rn = 1
      ),
      lv2_tmp1 as(
      select distinct lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name from spart_detail_info_tmp
      ),
      lv2_tmp as(
      select lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name
        from (
              select lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name
                   , row_number() over(partition by lv2_prod_rnd_team_code order by lv2_prod_rd_team_en_name) as rn
                from lv2_tmp1
             )t
       where rn = 1
      ),
      all_tmp as(
		  select t1.period_id
           , t1.phase_date
           , t4.node_code  as ver_lv1_code    -- 版本编码
           , t4.node_cname as ver_lv1_cn_name -- 版本中文名
           , t4.node_ename as ver_lv1_en_name -- 版本英文名
           , t5.node_code  as dste_scenario_lv1_code    -- 经营活动L1编码
           , t5.node_cname as dste_scenario_lv1_cn_name -- 经营活动L1中文名
           , t5.node_ename as dste_scenario_lv1_en_name -- 经营活动L1英文名
           , t6.node_code  as report_item_code    -- 报表项编码
           , t6.node_cname as report_item_cn_name -- 报表项中文名
           , t6.node_ename as report_item_en_name -- 报表项英文名
           , t1.equip_rev_after_fcst_conf
           , t1.equip_rev_after_fcst
           , t1.equip_rev_after_fcst_upper
           , t1.equip_rev_after_fcst_lower
           , t1.mgp_rate_after_fcst_conf
           , t1.mgp_rate_after_fcst
           , t1.mgp_rate_after_fcst_upper
           , t1.mgp_rate_after_fcst_lower
           , t1.lv1_code as lv1_prod_rnd_team_code
           , t1.lv1_name as lv1_prod_rd_team_cn_name
           , t3.lv1_prod_rd_team_en_name
           , t1.lv2_code as lv2_prod_rnd_team_code
           , t1.lv2_name as lv2_prod_rd_team_cn_name
           , t8.lv2_prod_rd_team_en_name
           , (case when t1.bg_code = 'GRP00001' then 'PROD0002' else t1.bg_code end) as bg_code
           , (case when t1.bg_name = '集团' then 'ICT' else t1.bg_name end) as bg_cn_name
           , (case when t1.bg_code = 'GRP00001' then 'ICT' else t9.bg_en_name end) as bg_en_name
           , t1.oversea_desc
           , t1.target_period
           , (case when t1.fcst_type in('时序法','年度法') then 'YTD法' else t1.fcst_type end) as fcst_type
           , t1.currency
           , t2.is_release_version_flag
           , t1.remark
           , t1.created_by
           , t1.creation_date
           , t1.last_updated_by
           , t1.last_update_date
           , t1.del_flag
	      from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_t t1
	      left join release_version_flag_tmp t2
	        on t1.phase_date = t2.phase_date
	       and t1.oversea_desc = t2.oversea_desc
	      left join lv1_tmp t3
	        on t1.lv1_code = t3.lv1_prod_rnd_team_code
	      left join lv2_tmp t8
	        on t1.lv2_code = t8.lv2_prod_rnd_team_code
	      left join bg_tmp t9
	        on t1.bg_code = t9.bg_code
	      left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'MEAM0003') t4
          on 1=1
        left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'MEAM0002') t5
          on 1=1
        left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'RPTI0001') t6
          on 1=1
       where t1.period_id = p_period   -- 取传入参数的年月数据
         and t1.fcst_type <> '年度平均法'
         and t1.l1_name is null
         and ((t1.phase_date is not null and substr(t1.phase_date,position('-' in t1.phase_date),1) <> '-') or substr(t1.target_period,1,4) = substr(t1.period_id,1,4))  -- 取没有'-'的期次以及target_period年份=period_id年份的期次
		  )
		  select t1.period_id
           , t1.phase_date
           , t1.ver_lv1_code    -- 版本编码
           , t1.ver_lv1_cn_name -- 版本中文名
           , t1.ver_lv1_en_name -- 版本英文名
           , t1.dste_scenario_lv1_code    -- 经营活动L1编码
           , t1.dste_scenario_lv1_cn_name -- 经营活动L1中文名
           , t1.dste_scenario_lv1_en_name -- 经营活动L1英文名
           , t1.report_item_code
           , t1.report_item_cn_name
           , t1.report_item_en_name
           , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst  -- 设备收入
                   when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst   -- 制造毛利率
              end) as report_item_fcst
           , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_conf
                   when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_conf
              end) as report_item_fcst_conf
           , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_upper
                   when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_upper
              end) as report_item_fcst_upper
           , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_lower
                   when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_lower
              end) as report_item_fcst_lower
           , t1.lv1_prod_rnd_team_code
           , t1.lv1_prod_rd_team_cn_name
           , t1.lv1_prod_rd_team_en_name
           , t1.lv2_prod_rnd_team_code
           , t1.lv2_prod_rd_team_cn_name
           , t1.lv2_prod_rd_team_en_name
           , t1.bg_code
           , t1.bg_cn_name
           , t1.bg_en_name
           , t1.oversea_desc
           , t1.target_period
           , t1.fcst_type
           , t1.currency
           , t1.is_release_version_flag
           , t1.remark
           , t1.created_by
           , t1.creation_date
           , t1.last_updated_by
           , t1.last_update_date
           , t1.del_flag
		    from all_tmp t1
		  ;

	     v_dml_row_count := sql%rowcount;	-- 收集数据量

       -- 写结束日志
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => '入到目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
       ) ;
     else
     
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入参数：'||p_period||'，来源表中没有传入参数的数据，需要重新传入参数，传参格式：yyyymm ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
       ) ;
      x_success_flag := 2001;
      return;
     
     end if;

  else
    -- 传入参数无值时，S&OP预测SUM成、宽表取最大版本数据
	  --判断来源表是否有当前年月数据，如果有则删除目标表对应数据
    if((select count(*) from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_t where period_id = substring(regexp_replace(current_date,'-',''),1,6)) > 0) then
		  --支持重跑，清除目标表要插入会计期的数据
		  delete from fin_dm_opt_fop.dm_ps_ai_forecast_t where period_id = substring(regexp_replace(current_date,'-',''),1,6);
      
      v_dml_row_count := sql%rowcount;   -- 收集数据量
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '系统：'||p_period||'，根据传入参数年月取的最大版本：'||v_version_code,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
		  --插入目标表数据
		insert into fin_dm_opt_fop.dm_ps_ai_forecast_t(
		    period_id
        , phase_date
        , ver_lv1_code
        , ver_lv1_cn_name
        , ver_lv1_en_name
        , dste_scenario_lv1_code
        , dste_scenario_lv1_cn_name
        , dste_scenario_lv1_en_name
        , report_item_code
        , report_item_cn_name
        , report_item_en_name
        , report_item_fcst
        , report_item_fcst_conf
        , report_item_fcst_upper
        , report_item_fcst_lower
        , lv1_prod_rnd_team_code
        , lv1_prod_rd_team_cn_name
        , lv1_prod_rd_team_en_name
        , lv2_prod_rnd_team_code
        , lv2_prod_rd_team_cn_name
        , lv2_prod_rd_team_en_name
        , bg_code
        , bg_cn_name
        , bg_en_name
        , oversea_desc
        , target_period
        , fcst_type
        , currency
        , is_release_version_flag
        , remark
        , created_by
        , creation_date
        , last_updated_by
        , last_update_date
        , del_flag
		)
		-- 从S&OP预测SUM表取所有期次、版本发布标识
    with release_version_flag_tmp as(
    select distinct phase_date, oversea_desc, is_release_version_flag
      from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
     where version_code = v_max_version_code
    ),
    -- 从宽表取最大版本的LV1、LV2信息
    spart_detail_info_tmp as(
    select distinct bg_code
         , bg_name
         , bg_en_name
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
      from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
     where version_code = v_max_version_code
    ),
    -- 从维表取经营活动（预算、预测）、版本、经营报告报表项
    data_tree_p_c_node_tmp as(
    select distinct dim_code, node_code, node_cname, node_ename
      from dimmgt.dim_data_tree_p_c_node_i
     where version_code = 'AF0001'
       and dim_code in('MEAM0002','MEAM0003','RPTI0001')   -- MEAM0002 经营活动  MEAM0003 版本  RPTI0001 经营报告报表项
       and node_code in('SNRT004','VR012','RPI_PL_91050','RPI_PL_10220')  -- SNRT004 预测  VR012 Auto版  RPI_PL_91050 设备收入   RPI_PL_10220 制造毛利率
    ),
    bg_tmp1 as(
    select distinct bg_code, bg_en_name from spart_detail_info_tmp
    ),
    bg_tmp as(
    select bg_code, bg_en_name
      from (select bg_code, bg_en_name, row_number() over(partition by bg_code order by bg_en_name) as rn from bg_tmp1 )t
     where rn = 1
    ),
    lv1_tmp1 as(
    select distinct lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name from spart_detail_info_tmp
    ),
    lv1_tmp as(
    select lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name
      from (
            select lv1_prod_rnd_team_code, lv1_prod_rd_team_en_name
                 , row_number() over(partition by lv1_prod_rnd_team_code order by lv1_prod_rd_team_en_name) as rn
              from lv1_tmp1
           )t
     where rn = 1
    ),
    lv2_tmp1 as(
    select distinct lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name from spart_detail_info_tmp
    ),
    lv2_tmp as(
    select lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name
      from (
            select lv2_prod_rnd_team_code, lv2_prod_rd_team_en_name
                 , row_number() over(partition by lv2_prod_rnd_team_code order by lv2_prod_rd_team_en_name) as rn
              from lv2_tmp1
           )t
     where rn = 1
    ),
    all_tmp as(
		select t1.period_id
         , t1.phase_date
         , t4.node_code  as ver_lv1_code    -- 版本编码
         , t4.node_cname as ver_lv1_cn_name -- 版本中文名
         , t4.node_ename as ver_lv1_en_name -- 版本英文名
         , t5.node_code  as dste_scenario_lv1_code    -- 经营活动L1编码
         , t5.node_cname as dste_scenario_lv1_cn_name -- 经营活动L1中文名
         , t5.node_ename as dste_scenario_lv1_en_name -- 经营活动L1英文名
         , t6.node_code  as report_item_code    -- 报表项编码
         , t6.node_cname as report_item_cn_name -- 报表项中文名
         , t6.node_ename as report_item_en_name -- 报表项英文名
         , t1.equip_rev_after_fcst_conf
         , t1.equip_rev_after_fcst
         , t1.equip_rev_after_fcst_upper
         , t1.equip_rev_after_fcst_lower
         , t1.mgp_rate_after_fcst_conf
         , t1.mgp_rate_after_fcst
         , t1.mgp_rate_after_fcst_upper
         , t1.mgp_rate_after_fcst_lower
         , t1.lv1_code as lv1_prod_rnd_team_code
         , t1.lv1_name as lv1_prod_rd_team_cn_name
         , t3.lv1_prod_rd_team_en_name
         , t1.lv2_code as lv2_prod_rnd_team_code
         , t1.lv2_name as lv2_prod_rd_team_cn_name
         , t8.lv2_prod_rd_team_en_name
         , (case when t1.bg_code = 'GRP00001' then 'PROD0002' else t1.bg_code end) as bg_code
         , (case when t1.bg_name = '集团' then 'ICT' else t1.bg_name end) as bg_cn_name
         , (case when t1.bg_code = 'GRP00001' then 'ICT' else t9.bg_en_name end) as bg_en_name
         , t1.oversea_desc
         , t1.target_period
         , (case when t1.fcst_type in('时序法','年度法') then 'YTD法' else t1.fcst_type end) as fcst_type
         , t1.currency
         , t2.is_release_version_flag
         , t1.remark
         , t1.created_by
         , t1.creation_date
         , t1.last_updated_by
         , t1.last_update_date
         , t1.del_flag
	    from fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_t t1  
	    left join release_version_flag_tmp t2
	      on t1.phase_date = t2.phase_date
	     and t1.oversea_desc = t2.oversea_desc
	    left join lv1_tmp t3
	      on t1.lv1_code = t3.lv1_prod_rnd_team_code
	    left join lv2_tmp t8
	      on t1.lv2_code = t8.lv2_prod_rnd_team_code
	    left join bg_tmp t9
	      on t1.bg_code = t9.bg_code
	    left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'MEAM0003') t4
        on 1=1
      left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'MEAM0002') t5
        on 1=1
      left join (select node_code, node_cname, node_ename from data_tree_p_c_node_tmp where dim_code = 'RPTI0001') t6
        on 1=1
     where t1.period_id = substr(regexp_replace(current_date,'-',''),1,6)   -- 取系统的当前年月
       and t1.fcst_type <> '年度平均法'
       and t1.l1_name is null
       and ((t1.phase_date is not null and substr(t1.phase_date,position('-' in t1.phase_date),1) <> '-') or substr(t1.target_period,1,4) = substr(t1.period_id,1,4))  -- 取没有'-'的期次以及target_period年份=period_id年份的期次
		)
		select t1.period_id
         , t1.phase_date
         , t1.ver_lv1_code    -- 版本编码
         , t1.ver_lv1_cn_name -- 版本中文名
         , t1.ver_lv1_en_name -- 版本英文名
         , t1.dste_scenario_lv1_code    -- 经营活动L1编码
         , t1.dste_scenario_lv1_cn_name -- 经营活动L1中文名
         , t1.dste_scenario_lv1_en_name -- 经营活动L1英文名
         , t1.report_item_code
         , t1.report_item_cn_name
         , t1.report_item_en_name
         , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst  -- 设备收入
                 when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst   -- 制造毛利率
            end) as report_item_fcst
         , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_conf
                 when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_conf
            end) as report_item_fcst_conf
         , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_upper
                 when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_upper
            end) as report_item_fcst_upper
         , (case when t1.report_item_code = 'RPI_PL_91050' then t1.equip_rev_after_fcst_lower
                 when t1.report_item_code = 'RPI_PL_10220' then t1.mgp_rate_after_fcst_lower
            end) as report_item_fcst_lower
         , t1.lv1_prod_rnd_team_code
         , t1.lv1_prod_rd_team_cn_name
         , t1.lv1_prod_rd_team_en_name
         , t1.lv2_prod_rnd_team_code
         , t1.lv2_prod_rd_team_cn_name
         , t1.lv2_prod_rd_team_en_name
         , t1.bg_code
         , t1.bg_cn_name
         , t1.bg_en_name
         , t1.oversea_desc
         , t1.target_period
         , t1.fcst_type
         , t1.currency
         , t1.is_release_version_flag
         , t1.remark
         , t1.created_by
         , t1.creation_date
         , t1.last_updated_by
         , t1.last_update_date
         , t1.del_flag
		  from all_tmp t1
		;

	  v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '当前会计期：'||substring(regexp_replace(current_date,'-',''),1,6)||'，入到目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    
    else
      -- 写结束日志
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '当前会计期：'||substring(regexp_replace(current_date,'-',''),1,6)||'，来源表中没有此月份的数据，结束运行！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
    x_success_flag := '2001';
    return;
    
	  end if;
  end if;

  exception
    when others then
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败

  --收集统计信息
  analyse fin_dm_opt_fop.dm_ps_ai_forecast_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

