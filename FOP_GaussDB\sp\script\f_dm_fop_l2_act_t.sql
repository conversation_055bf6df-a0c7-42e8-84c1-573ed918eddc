-- ----------------------------
-- Function structure for f_dm_fop_l2_act_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_l2_act_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_l2_act_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*********************************************************************************************************************************************************************
创建时间：2023-6-14
创建人  ：qwx1110218
背景描述：L2历史表，来源表：kr_cpf_l2_act_t 表的数据+dm_fop_spart_l2_tgt_period_his_t 表YTD的数据（此表中的version_code就是目标表的period_id，如果年月有多个版本则取年月的最大版本）。
参数描述：参数一(p_version_code)：版本编码，参数格式：202305_V1
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_l2_act_t()
          20230901 9月版新增月度YTD的数据：从YTD表取值

*********************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_l2_act_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_l2_act_t';
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_max_period_id    varchar(50);  -- 最大版本的最大会计期
	v_period_id        varchar(50);  -- 传入版本的最大会计期
	v_dml_row_count  number default 0 ;

begin
	set enable_force_vector_engine to on;
	x_success_flag := '1';

  -- 写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'L2历史表 '||v_tbl_name||' 开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  );
  
  -- 取来源表最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t where del_flag = 'N')
     and del_flag = 'N'
   group by substr(version_code,1,6)
	;
	
	-- 最大版本的最大会计期
	select max(period_id) as max_period_id into v_max_period_id  from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t where del_flag = 'N' and version_code = v_max_version_code;

  -- 传入参数有值，则取传入参数的版本数据
  if(p_version_code is not null or p_version_code <> '') then
    select max(period_id) as max_period_id into v_period_id  from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t where del_flag = 'N' and version_code = p_version_code;
    if not exists(select distinct period_id from fin_dm_opt_fop.kr_cpf_l2_act_t where period_id = v_period_id::numeric) then
    
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '传入版本：'||p_version_code||'，kr_cpf_l2_act_t 表没有传入版本中 '||v_period_id||' 会计期的数据，请重新传入版本 ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	    x_success_flag := '2001';
	    return;
    
    -- 删除目标表中已存在传入版本年月的数据
    else
      -- 清理目标表数据
      delete from fin_dm_opt_fop.dm_fop_l2_act_t where period_id = v_period_id::numeric;
    
      v_dml_row_count := sql%rowcount;  -- 收集数据量
    
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '删除目标表中 '||v_period_id||' 会计期的数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
      ) ;
      
      -- 数据入到目标表
      insert into fin_dm_opt_fop.dm_fop_l2_act_t(
             period_id                -- 会计期
           , target_period            -- 目标时点
           , bg_code                  -- BG编码
           , bg_name                  -- BG名称
           , oversea_desc             -- 区域
           , lv1_code                 -- 重量级团队LV1编码
           , lv1_name                 -- 重量级团队LV1描述
           , lv2_code                 -- 重量级团队LV2编码
           , lv2_name                 -- 重量级团队LV2名称
           , l1_name                  -- L1名称
           , l2_name                  -- L2名称
           , currency                 -- 币种
           , unit_price_act           -- 单位价格
           , unit_cost_act            -- 单位成本
           , mgp_rate_before_act      -- 对价前制毛率
           , rev_percent_act          -- 收入占比
           , carryover_amount_act     -- 结转量
           , equip_rev_before_act     -- 历史对价前设备收入额
           , equip_cost_before_act    -- 历史对价前设备成本额
           , ship_qty                 -- 发货量
           , source_table	            -- 来源表
           , remark                   -- 备注
           , created_by               -- 创建人
           , creation_date            -- 创建时间
           , last_updated_by          -- 修改人
           , last_update_date         -- 修改时间
           , del_flag                 -- 是否删除
      )
      -- 从 KR_l2_act 表取全量数据
      select period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , l2_name
           , currency
           , unit_price_act
           , unit_cost_act
           , mgp_rate_before_act
           , rev_percent_act
           , carryover_amount_act
           , equip_rev_before_act
           , equip_cost_before_act
           , ship_qty
           , 'fin_dm_opt_fop.kr_cpf_l2_act_t' as source_table
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
        from fin_dm_opt_fop.kr_cpf_l2_act_t
       where period_id = v_period_id::numeric  -- 取传入版本的最大会计期数据
       union all
       -- 取传入参数版本的月度数据
       select v_period_id::numeric as period_id
            , t1.target_period
            , t1.bg_code
            , t1.bg_name
            , t1.oversea_desc
            , t1.lv1_code
            , t1.lv1_name
            , t1.lv2_code
            , t1.lv2_name
            , t1.l1_name
            , t1.l2_name
            , t1.currency
            , t1.unit_price
            , t1.unit_cost
            , t1.mgp_ratio
            , t1.rev_percent
            , t1.spart_qty
            , t1.equip_rev_cons_before_amt
            , t1.equip_cost_cons_before_amt
            , t1.ship_qty
            , 'fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t' as source_table
            , t1.remark
            , t1.created_by
            , t1.creation_date
            , t1.last_updated_by
            , t1.last_update_date
            , t1.del_flag
         from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t
        where version_code = p_version_code
          and del_flag = 'N'
          and currency = 'CNY'
          and phase_date is null
          -- and oversea_desc = '全球'
          and upper(t1.target_period) like'%YTD%'
       ;
      
       v_dml_row_count := sql%rowcount;  -- 收集数据量
      
	      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
              p_log_version_id => null,                 --版本
              p_log_sp_name => v_sp_name,    --sp名称
              p_log_para_list => '',--参数
              p_log_step_num  => 3,
              p_log_cal_log_desc => '数据入到目标表 ，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
              p_log_formula_sql_txt => null,--错误信息
              p_log_row_count => v_dml_row_count,
              p_log_errbuf => null  --错误编码
        ) ;
    end if;  
      
  -- 传入参数没值，则取最大版本的数据 
  else
    -- 判断来源表是否有最大版本年月的数据，如没有，则不删除
    if not exists(select period_id from fin_dm_opt_fop.kr_cpf_l2_act_t_m) then
      
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => 'kr_cpf_l2_act_t_m 表没有数据，请检查数据是否已同步 ！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => 0,
            p_log_errbuf => null  --错误编码
          ) ;
	    x_success_flag := '2001';
	    return;
    
    -- 删除目标表中已存在传入版本年月的数据
    else
      -- 清理目标表数据
      delete from fin_dm_opt_fop.dm_fop_l2_act_t where period_id = (select distinct period_id from fin_dm_opt_fop.kr_cpf_l2_act_t_m);
  
      v_dml_row_count := sql%rowcount;  -- 收集数据量
      
	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '删除目标表中 '||v_max_period_id||' 版本的数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
      ) ;
    
      -- 数据入到目标表
      insert into fin_dm_opt_fop.dm_fop_l2_act_t(
             period_id                -- 会计期
           , target_period            -- 目标时点
           , bg_code                  -- BG编码
           , bg_name                  -- BG名称
           , oversea_desc             -- 区域
           , lv1_code                 -- 重量级团队LV1编码
           , lv1_name                 -- 重量级团队LV1描述
           , lv2_code                 -- 重量级团队LV2编码
           , lv2_name                 -- 重量级团队LV2名称
           , l1_name                  -- L1名称
           , l2_name                  -- L2名称
           , currency                 -- 币种
           , unit_price_act           -- 单位价格
           , unit_cost_act            -- 单位成本
           , mgp_rate_before_act      -- 对价前制毛率
           , rev_percent_act          -- 收入占比
           , carryover_amount_act     -- 结转量
           , equip_rev_before_act     -- 历史对价前设备收入额
           , equip_cost_before_act    -- 历史对价前设备成本额
           , ship_qty                 -- 发货量
           , source_table	            -- 来源表
           , remark                   -- 备注
           , created_by               -- 创建人
           , creation_date            -- 创建时间
           , last_updated_by          -- 修改人
           , last_update_date         -- 修改时间
           , del_flag                 -- 是否删除
      )
      -- 从 KR_l2_act 表取全量数据
      select period_id
           , target_period
           , bg_code
           , bg_name
           , oversea_desc
           , lv1_code
           , lv1_name
           , lv2_code
           , lv2_name
           , l1_name
           , l2_name
           , currency
           , unit_price_act
           , unit_cost_act
           , mgp_rate_before_act
           , rev_percent_act
           , carryover_amount_act
           , equip_rev_before_act
           , equip_cost_before_act
           , ship_qty
           , 'fin_dm_opt_fop.kr_cpf_l2_act_t' as source_table
           , remark
           , created_by
           , creation_date
           , last_updated_by
           , last_update_date
           , del_flag
        from fin_dm_opt_fop.kr_cpf_l2_act_t_m t1
      union all
      -- 取传入参数版本的月度数据
      select t2.period_id::numeric as period_id
           , t1.target_period
           , t1.bg_code
           , t1.bg_name
           , t1.oversea_desc
           , t1.lv1_code
           , t1.lv1_name
           , t1.lv2_code
           , t1.lv2_name
           , t1.l1_name
           , t1.l2_name
           , t1.currency
           , t1.unit_price
           , t1.unit_cost
           , t1.mgp_ratio
           , t1.rev_percent
           , t1.spart_qty
           , t1.equip_rev_cons_before_amt
           , t1.equip_cost_cons_before_amt
           , t1.ship_qty
           , 'fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t' as source_table
           , t1.remark
           , t1.created_by
           , t1.creation_date
           , t1.last_updated_by
           , t1.last_update_date
           , t1.del_flag
        from fin_dm_opt_fop.dm_fop_spart_l2_tgt_period_his_t t1
        left join (select distinct period_id from fin_dm_opt_fop.kr_cpf_l2_act_t_m) t2
          on 1=1
       where version_code = v_max_version_code
         and del_flag = 'N'
         and currency = 'CNY'
         and phase_date is null
         -- and oversea_desc = '全球'
         and upper(t1.target_period) like'%YTD%'
      ;
      
      v_dml_row_count := sql%rowcount;  -- 收集数据量
      
	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
              p_log_version_id => null,                 --版本
              p_log_sp_name => v_sp_name,    --sp名称
              p_log_para_list => '',--参数
              p_log_step_num  => 3,
              p_log_cal_log_desc => '数据入到目标表 ，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
              p_log_formula_sql_txt => null,--错误信息
              p_log_row_count => v_dml_row_count,
              p_log_errbuf => null  --错误编码
      ) ;
    
    end if;

  end if;
  
  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_l2_act_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

