-- 盈利量纲版本信息表
DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T;
CREATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T(
	VERSION_CODE     CHARACTER VARYING(30),
	STEP             INTEGER,
	REMARK           CHARACTER VARYING(500),
	CREATED_BY       BIGINT,
	CREATION_DATE    TIMESTAMP WITHOUT TIME ZONE,
	LAST_UPDATED_BY  BIGINT,
	LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
	DEL_FLAG         CHARACTER VARYING(10)
)WITH (ORIENTATION=COLUMN, COMPRESSION=LOW, COLVERSION=2.0, ENABLE_DELTA=FALSE)
DISTRIBUTE BY REPLICATION;

COMMENT ON TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T IS '盈利量纲版本信息表';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.VERSION_CODE   IS '版本编码（格式：YYYYMM-001、YYYYMM-002）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.STEP   IS '执行步骤（ 1、成功   2、执行中   2001、失败 ）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.REMARK   IS '备注';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.CREATED_BY   IS '创建人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.CREATION_DATE   IS '创建时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.LAST_UPDATED_BY   IS '修改人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.LAST_UPDATE_DATE   IS '修改时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T.DEL_FLAG   IS '是否删除';