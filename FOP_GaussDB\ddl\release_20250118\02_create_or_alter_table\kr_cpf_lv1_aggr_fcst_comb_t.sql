-- ----------------------------
-- Table structure for kr_cpf_lv1_aggr_fcst_comb_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t";
CREATE TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t" (
  "period_id" varchar(50) COLLATE "pg_catalog"."default",
  "target_period" varchar(100) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_name" varchar(600) COLLATE "pg_catalog"."default",
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "fcst_type" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_after_fcst" numeric(38,10),
  "mgp_rate_after_fcst" numeric(38,10),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "phase_date" varchar(60) COLLATE "pg_catalog"."default",
  "aggregate_flag" varchar(50) COLLATE "pg_catalog"."default",
  "combined_expert" varchar(500) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6)
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."target_period" IS '目标时点';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."bg_name" IS 'BG名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."oversea_desc" IS '区域';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."lv1_code" IS 'LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."lv1_name" IS 'LV1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."lv2_code" IS 'LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."lv2_name" IS 'LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."fcst_type" IS '预测类型';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."equip_rev_after_fcst" IS '对价后设备收入预测_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."mgp_rate_after_fcst" IS '制毛率预测（对价后)_预测值';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."del_flag" IS '是否删除标识';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."phase_date" IS '期次';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."aggregate_flag" IS '汇聚标志（无效，全都是全球的）';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."combined_expert" IS '融合分析师';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t"."last_update_date" IS '修改时间';
COMMENT ON TABLE "fin_dm_opt_fop"."kr_cpf_lv1_aggr_fcst_comb_t" IS 'AI融合预测结果表（保留历史版本）';

