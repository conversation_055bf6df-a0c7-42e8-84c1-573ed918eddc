-- ----------------------------
-- Function structure for f_dm_fop_spart_data_ytd_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_data_ytd_t"("p_version_code" varchar, "p_keystr" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_data_ytd_t"(IN "p_version_code" varchar, IN "p_keystr" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*********************************************************************************************************************************************************************
创建时间：2023-6-2
创建人  ：qwx1110218
背景描述：计算Spart粒度的年累计均本均价，来源表是 dm_fop_spart_data_t ，前台用于展示，不能对外提供。
参数描述：参数一(p_version_code)：版本编码，参数格式：202305_V1
		      参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_data_ytd_t()

*********************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_data_ytd_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_data_ytd_t';
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';

  -- 写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'Spart粒度数据表 '||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  );

  -- 取来源表最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_data_his_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_data_his_t where del_flag = 'N')
     and del_flag = 'N'
   group by substr(version_code,1,6)
	;


  -- 传入版本没值，取最大版本的数据
  if(p_version_code is null or p_version_code = '' ) then
    -- 删除目标表中最大版本数据
    delete fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t where version_code = v_max_version_code;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '删除 '||v_max_version_code||' 版本的数据量： '||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    );

    insert into fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t(
           version_code                     -- 版本编码
         , period_id                        -- 会计期
         , spart_code                       -- spart编码
         , spart_desc                       -- spart描述
         , bg_code                          -- BG编码
         , bg_cn_name                       -- BG中文名称
         , bg_en_name                       -- BG英文名称
         , oversea_desc                     -- 区域（全球/国内/海外）
         , lv1_prod_rnd_team_code           -- 重量级团队LV1编码
         , lv1_prod_rd_team_cn_name         -- 重量级团队LV1描述
         , lv1_prod_rd_team_en_name         -- 重量级团队LV1英文描述
         , lv2_prod_rnd_team_code           -- 重量级团队LV2编码
         , lv2_prod_rd_team_cn_name         -- 重量级团队LV2名称
         , lv2_prod_rd_team_en_name         -- 重量级团队LV2英文描述
         , l1_name                          -- L1名称
         , l2_name                          -- L2名称
         , l2_coefficient                   -- L2系数
         , currency                         -- 币种
         , equip_rev_cons_before_ytd        -- 设备收入本年累计(对价前)
         , equip_cost_cons_before_ytd       -- 设备成本本年累计(对价前)
         , spart_ytd                        -- 收入量（本年累计）
         , avg_cost_ytd                     -- 平均成本（本年累计）
         , avg_price_ytd                    -- 平均价格（本年累计）
         , rev_percent_ytd                  -- 收入占比（本年累计）
         , cost_percent_ytd                 -- 成本占比（本年累计）
         , mgp_ratio_ytd                    -- 制毛率（本年累计）
         , articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
         , remark                           -- 备注
         , created_by                       -- 创建人
         , creation_date                    -- 创建时间
         , last_updated_by                  -- 修改人
         , last_update_date                 -- 修改时间
         , del_flag                         -- 是否删除
    )
    with ytd_tmp as(
    select version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , sum(equip_rev_cons_before_amt) over(partition by version_code
                                                       , substr(period_id,1,4)
                                                       , spart_code
                                                       , bg_code
                                                       , oversea_desc
                                                       , lv1_prod_rnd_team_code
                                                       , lv2_prod_rnd_team_code
                                                       , l1_name
                                                       , l2_name
                                                       , l2_coefficient
                                                       , currency
                                                   order by period_id) as equip_rev_cons_before_ytd
         , sum(equip_cost_cons_before_amt) over(partition by version_code
                                                        , substr(period_id,1,4)
                                                        , spart_code
                                                        , bg_code
                                                        , oversea_desc
                                                        , lv1_prod_rnd_team_code
                                                        , lv2_prod_rnd_team_code
                                                        , l1_name
                                                        , l2_name
                                                        , l2_coefficient
                                                        , currency
                                                   order by period_id) as equip_cost_cons_before_ytd
         , sum(spart_qty) over(partition by version_code
                                       , substr(period_id,1,4)
                                       , spart_code
                                       , bg_code
                                       , oversea_desc
                                       , lv1_prod_rnd_team_code
                                       , lv2_prod_rnd_team_code
                                       , l1_name
                                       , l2_name
                                       , l2_coefficient
                                       , currency
                                   order by period_id) as spart_ytd
      from fin_dm_opt_fop.dm_fop_spart_data_his_t
     where version_code = v_max_version_code
     group by version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , equip_rev_cons_before_amt
         , equip_cost_cons_before_amt
         , spart_qty
    ),
    -- 按spart 粒度收敛
    spart_info_ytd_tmp as(
    select version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , articulation_flag
         , equip_rev_cons_before_ytd
         , equip_cost_cons_before_ytd
         , spart_ytd
      from ytd_tmp
    ),
    -- 按L2_name 收敛
    l2_info_ytd_tmp as(
    select distinct version_code
           , period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code
                                                         , substr(period_id,1,4)
                                                         , bg_code
                                                         , oversea_desc
                                                         , lv1_prod_rnd_team_code
                                                         , lv2_prod_rnd_team_code
                                                         , l1_name
                                                         , l2_name
                                                         , currency
                                                     order by period_id) as equip_rev_cons_before_ytd
           , sum(equip_cost_cons_before_amt) over(partition by version_code
                                                          , substr(period_id,1,4)
                                                          , bg_code
                                                          , oversea_desc
                                                          , lv1_prod_rnd_team_code
                                                          , lv2_prod_rnd_team_code
                                                          , l1_name
                                                          , l2_name
                                                          , currency
                                                     order by period_id) as equip_cost_cons_before_ytd
           , sum(spart_qty) over(partition by version_code
                                         , substr(period_id,1,4)
                                         , bg_code
                                         , oversea_desc
                                         , lv1_prod_rnd_team_code
                                         , lv2_prod_rnd_team_code
                                         , l1_name
                                         , l2_name
                                         , currency
                                     order by period_id) as spart_ytd
        from fin_dm_opt_fop.dm_fop_spart_data_his_t
       where version_code = v_max_version_code  -- 取最大版本数据
    ),
    --计算平均价格、平均成本、收入占比、制毛率
	  all_temp as(
    select t1.version_code
         , t1.period_id
         , t1.spart_code
         , t1.spart_desc
         , t1.bg_code
         , t1.bg_cn_name
         , t1.bg_en_name
         , t1.oversea_desc
         , t1.lv1_prod_rnd_team_code
         , t1.lv1_prod_rd_team_cn_name
         , t1.lv1_prod_rd_team_en_name
         , t1.lv2_prod_rnd_team_code
         , t1.lv2_prod_rd_team_cn_name
         , t1.lv2_prod_rd_team_en_name
         , t1.l1_name
         , t1.l2_name
         , t1.l2_coefficient
         , t1.currency
         , t1.articulation_flag
         , t1.equip_rev_cons_before_ytd
         , t1.equip_cost_cons_before_ytd
         , t1.spart_ytd
         , (case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
	  			       when nvl(t2.equip_rev_cons_before_ytd,0) = 0 then -999999
	  			       else t1.equip_rev_cons_before_ytd / t2.equip_rev_cons_before_ytd end) as rev_percent_ytd	  -- 收入占比（本年累计） =  Spart金额/L2对价前收入本年累计
	  	   , (case when nvl(t2.equip_cost_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
	  			       when nvl(t2.equip_cost_cons_before_ytd,0) = 0 then -999999
	  			       else t1.equip_cost_cons_before_ytd / t2.equip_cost_cons_before_ytd end) as cost_percent_ytd	-- 成本占比（本年累计） =  Spart金额/L2对价前成本本年累计
	  	   , (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then 0
	  			       when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then -999999
	  			       else 1 - t1.equip_cost_cons_before_ytd / t1.equip_rev_cons_before_ytd end) as mgp_ratio_ytd	-- 制毛率（本年累计） = 1 - Spart粒度对价前成本金额/ Spart粒度对价前收入本年累计
         , (case when nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
	               when nvl(t1.spart_ytd,0) = 0 then -999999
	               else t1.equip_cost_cons_before_ytd / t1.spart_ytd end) as avg_cost_ytd	-- 平均成本（本年累计） = Spart粒度对价前成本金额/Spart粒度收入数量/L2系数
	  	   , (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
                 when nvl(t1.spart_ytd,0) = 0 then -999999
                 else t1.equip_rev_cons_before_ytd / t1.spart_ytd end) as avg_price_ytd	-- 平均价格（本年累计） = Spart粒度对价前收入金额/Spart粒度收入数量/L2系数
      from spart_info_ytd_tmp t1
      left join l2_info_ytd_tmp t2
        on t1.version_code = t2.version_code
	  	 and t1.period_id = t2.period_id
	     and t1.bg_code = t2.bg_code
	     and t1.oversea_desc = t2.oversea_desc
	     and t1.lv1_prod_rnd_team_code = t2.lv1_prod_rnd_team_code--	重量级团队lv1编码
	     and t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code--	重量级团队lv2编码
	     and t1.l1_name = t2.l1_name
	     and t1.l2_name = t2.l2_name
	     and t1.currency = t2.currency--币种
	     and t1.articulation_flag = t2.articulation_flag
    )
    select version_code
         , period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_ytd
         , equip_cost_cons_before_ytd
         , spart_ytd
         , avg_cost_ytd
         , avg_price_ytd
         , rev_percent_ytd
         , cost_percent_ytd
         , mgp_ratio_ytd
         , articulation_flag
	  	   , '' as remark
	       , -1 as created_by
	       , current_timestamp as creation_date
	       , -1 as last_updated_by
	       , current_timestamp as last_update_date
	       , 'N' as del_flag
      from all_temp
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '最大版本编码： '||v_max_version_code||'，入到目标表 dm_fop_spart_data_his_ytd_t 的数据量： '||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    );

    -- 清理目标表数据（此目标表只保留最新版本数据）
    truncate table fin_dm_opt_fop.dm_fop_spart_data_ytd_t;

    insert into fin_dm_opt_fop.dm_fop_spart_data_ytd_t(
           period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_ytd
         , equip_cost_cons_before_ytd
         , spart_ytd
         , avg_cost_ytd
         , avg_price_ytd
         , rev_percent_ytd
         , cost_percent_ytd
         , mgp_ratio_ytd
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_ytd
         , GS_ENCRYPT(equip_cost_cons_before_ytd,p_keystr,'AES128', 'CBC', 'SHA256') as equip_cost_cons_before_ytd
         , spart_ytd
         , GS_ENCRYPT(avg_cost_ytd,p_keystr,'AES128', 'CBC', 'SHA256') as avg_cost_ytd
         , avg_price_ytd
         , rev_percent_ytd
         , cost_percent_ytd
         , mgp_ratio_ytd
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t
     where version_code = v_max_version_code
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '最大版本编码： '||v_max_version_code||'，数据入到目标表 dm_fop_spart_data_ytd_t 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    );

  else
    -- 传入参数有值，判断来源表中是否有传入版本数据，如有则删除目标表中对应版本数据
    if((select count(*) from fin_dm_opt_fop.dm_fop_spart_data_his_t where version_code = p_version_code) > 0) then
	    delete fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t where version_code = p_version_code;

	    v_dml_row_count := sql%rowcount;	-- 收集数据量

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '删除 '||p_version_code||' 版本的数据量： '||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
      );

      insert into fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t(
             version_code                     -- 版本编码
           , period_id                        -- 会计期
           , spart_code                       -- spart编码
           , spart_desc                       -- spart描述
           , bg_code                          -- BG编码
           , bg_cn_name                       -- BG中文名称
           , bg_en_name                       -- BG英文名称
           , oversea_desc                     -- 区域（全球/国内/海外）
           , lv1_prod_rnd_team_code           -- 重量级团队LV1编码
           , lv1_prod_rd_team_cn_name         -- 重量级团队LV1描述
           , lv1_prod_rd_team_en_name         -- 重量级团队LV1英文描述
           , lv2_prod_rnd_team_code           -- 重量级团队LV2编码
           , lv2_prod_rd_team_cn_name         -- 重量级团队LV2名称
           , lv2_prod_rd_team_en_name         -- 重量级团队LV2英文描述
           , l1_name                          -- L1名称
           , l2_name                          -- L2名称
           , l2_coefficient                   -- L2系数
           , currency                         -- 币种
           , equip_rev_cons_before_ytd        -- 设备收入本年累计(对价前)
           , equip_cost_cons_before_ytd       -- 设备成本本年累计(对价前)
           , spart_ytd                        -- 收入量（本年累计）
           , avg_cost_ytd                     -- 平均成本（本年累计）
           , avg_price_ytd                    -- 平均价格（本年累计）
           , rev_percent_ytd                  -- 收入占比（本年累计）
           , cost_percent_ytd                 -- 成本占比（本年累计）
           , mgp_ratio_ytd                    -- 制毛率（本年累计）
           , articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
           , remark                           -- 备注
           , created_by                       -- 创建人
           , creation_date                    -- 创建时间
           , last_updated_by                  -- 修改人
           , last_update_date                 -- 修改时间
           , del_flag                         -- 是否删除
      )
      with ytd_tmp as(
      select version_code
           , period_id
           , spart_code
           , spart_desc
           , bg_code
           , bg_cn_name
           , bg_en_name
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv1_prod_rd_team_cn_name
           , lv1_prod_rd_team_en_name
           , lv2_prod_rnd_team_code
           , lv2_prod_rd_team_cn_name
           , lv2_prod_rd_team_en_name
           , l1_name
           , l2_name
           , l2_coefficient
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code
                                                         , substr(period_id,1,4)
                                                         , spart_code
                                                         , bg_code
                                                         , oversea_desc
                                                         , lv1_prod_rnd_team_code
                                                         , lv2_prod_rnd_team_code
                                                         , l1_name
                                                         , l2_name
                                                         , l2_coefficient
                                                         , currency
                                                     order by period_id) as equip_rev_cons_before_ytd
           , sum(equip_cost_cons_before_amt) over(partition by version_code
                                                          , substr(period_id,1,4)
                                                          , spart_code
                                                          , bg_code
                                                          , oversea_desc
                                                          , lv1_prod_rnd_team_code
                                                          , lv2_prod_rnd_team_code
                                                          , l1_name
                                                          , l2_name
                                                          , l2_coefficient
                                                          , currency
                                                     order by period_id) as equip_cost_cons_before_ytd
           , sum(spart_qty) over(partition by version_code
                                         , substr(period_id,1,4)
                                         , spart_code
                                         , bg_code
                                         , oversea_desc
                                         , lv1_prod_rnd_team_code
                                         , lv2_prod_rnd_team_code
                                         , l1_name
                                         , l2_name
                                         , l2_coefficient
                                         , currency
                                     order by period_id) as spart_ytd
        from fin_dm_opt_fop.dm_fop_spart_data_his_t
       where version_code = p_version_code  -- 取传入会计期数据
       group by version_code
           , period_id
           , spart_code
           , spart_desc
           , bg_code
           , bg_cn_name
           , bg_en_name
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv1_prod_rd_team_cn_name
           , lv1_prod_rd_team_en_name
           , lv2_prod_rnd_team_code
           , lv2_prod_rd_team_cn_name
           , lv2_prod_rd_team_en_name
           , l1_name
           , l2_name
           , l2_coefficient
           , currency
           , articulation_flag
           , equip_rev_cons_before_amt
           , equip_cost_cons_before_amt
           , spart_qty
      ),
      -- 按spart 粒度收敛
      spart_info_ytd_tmp as(
      select version_code
           , period_id
           , spart_code
           , spart_desc
           , bg_code
           , bg_cn_name
           , bg_en_name
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv1_prod_rd_team_cn_name
           , lv1_prod_rd_team_en_name
           , lv2_prod_rnd_team_code
           , lv2_prod_rd_team_cn_name
           , lv2_prod_rd_team_en_name
           , l1_name
           , l2_name
           , l2_coefficient
           , currency
           , articulation_flag
           , equip_rev_cons_before_ytd
           , equip_cost_cons_before_ytd
           , spart_ytd
        from ytd_tmp
      ),
      -- 按L2_name 收敛
      l2_info_ytd_tmp as(
      select distinct version_code
           , period_id
           , bg_code
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv2_prod_rnd_team_code
           , l1_name
           , l2_name
           , currency
           , articulation_flag
           , sum(equip_rev_cons_before_amt) over(partition by version_code
                                                         , substr(period_id,1,4)
                                                         , bg_code
                                                         , oversea_desc
                                                         , lv1_prod_rnd_team_code
                                                         , lv2_prod_rnd_team_code
                                                         , l1_name
                                                         , l2_name
                                                         , currency
                                                     order by period_id) as equip_rev_cons_before_ytd
           , sum(equip_cost_cons_before_amt) over(partition by version_code
                                                          , substr(period_id,1,4)
                                                          , bg_code
                                                          , oversea_desc
                                                          , lv1_prod_rnd_team_code
                                                          , lv2_prod_rnd_team_code
                                                          , l1_name
                                                          , l2_name
                                                          , currency
                                                     order by period_id) as equip_cost_cons_before_ytd
           , sum(spart_qty) over(partition by version_code
                                         , substr(period_id,1,4)
                                         , bg_code
                                         , oversea_desc
                                         , lv1_prod_rnd_team_code
                                         , lv2_prod_rnd_team_code
                                         , l1_name
                                         , l2_name
                                         , currency
                                     order by period_id) as spart_ytd
        from fin_dm_opt_fop.dm_fop_spart_data_his_t
       where version_code = p_version_code  -- 取传入会计期数据
      ),
      --计算平均价格、平均成本、收入占比、制毛率
	    all_temp as(
      select t1.version_code
           , t1.period_id
           , t1.spart_code
           , t1.spart_desc
           , t1.bg_code
           , t1.bg_cn_name
           , t1.bg_en_name
           , t1.oversea_desc
           , t1.lv1_prod_rnd_team_code
           , t1.lv1_prod_rd_team_cn_name
           , t1.lv1_prod_rd_team_en_name
           , t1.lv2_prod_rnd_team_code
           , t1.lv2_prod_rd_team_cn_name
           , t1.lv2_prod_rd_team_en_name
           , t1.l1_name
           , t1.l2_name
           , t1.l2_coefficient
           , t1.currency
           , t1.articulation_flag
           , t1.equip_rev_cons_before_ytd
           , t1.equip_cost_cons_before_ytd
           , t1.spart_ytd
           , (case when nvl(t2.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
	    			       when nvl(t2.equip_rev_cons_before_ytd,0) = 0 then -999999
	    			       else t1.equip_rev_cons_before_ytd / t2.equip_rev_cons_before_ytd end) as rev_percent_ytd	  -- 收入占比（本年累计） =  Spart金额/L2对价前收入本年累计
	    	   , (case when nvl(t2.equip_cost_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
	    			       when nvl(t2.equip_cost_cons_before_ytd,0) = 0 then -999999
	    			       else t1.equip_cost_cons_before_ytd / t2.equip_cost_cons_before_ytd end) as cost_percent_ytd	-- 成本占比（本年累计） =  Spart金额/L2对价前成本本年累计
	    	   , (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) = 0 then 0
	    			       when nvl(t1.equip_rev_cons_before_ytd,0) = 0 and nvl(t1.equip_cost_cons_before_ytd,0) <> 0 then -999999
	    			       else 1 - t1.equip_cost_cons_before_ytd / t1.equip_rev_cons_before_ytd end) as mgp_ratio_ytd	-- 制毛率（本年累计） = 1 - Spart粒度对价前成本金额/ Spart粒度对价前收入本年累计
           , (case when nvl(t1.equip_cost_cons_before_ytd,0) = 0 then null
	                 when nvl(t1.spart_ytd,0) = 0 then -999999
	                 else t1.equip_cost_cons_before_ytd / t1.spart_ytd end) as avg_cost_ytd	-- 平均成本（本年累计） = Spart粒度对价前成本金额/Spart粒度收入数量/L2系数
	    	   , (case when nvl(t1.equip_rev_cons_before_ytd,0) = 0 then null
                   when nvl(t1.spart_ytd,0) = 0 then -999999
                   else t1.equip_rev_cons_before_ytd / t1.spart_ytd end) as avg_price_ytd	-- 平均价格（本年累计） = Spart粒度对价前收入金额/Spart粒度收入数量/L2系数
        from spart_info_ytd_tmp t1
        left join l2_info_ytd_tmp t2
          on t1.version_code = t2.version_code
	    	 and t1.period_id = t2.period_id
	       and t1.bg_code = t2.bg_code
	       and t1.oversea_desc = t2.oversea_desc
	       and t1.lv1_prod_rnd_team_code = t2.lv1_prod_rnd_team_code--	重量级团队lv1编码
	       and t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code--	重量级团队lv2编码
	       and t1.l1_name = t2.l1_name
	       and t1.l2_name = t2.l2_name
	       and t1.currency = t2.currency--币种
	       and t1.articulation_flag = t2.articulation_flag
      )
      select version_code
           , period_id
           , spart_code
           , spart_desc
           , bg_code
           , bg_cn_name
           , bg_en_name
           , oversea_desc
           , lv1_prod_rnd_team_code
           , lv1_prod_rd_team_cn_name
           , lv1_prod_rd_team_en_name
           , lv2_prod_rnd_team_code
           , lv2_prod_rd_team_cn_name
           , lv2_prod_rd_team_en_name
           , l1_name
           , l2_name
           , l2_coefficient
           , currency
           , equip_rev_cons_before_ytd
           , equip_cost_cons_before_ytd
           , spart_ytd
           , avg_cost_ytd
           , avg_price_ytd
           , rev_percent_ytd
           , cost_percent_ytd
           , mgp_ratio_ytd
           , articulation_flag
	    	   , '' as remark
	         , -1 as created_by
	         , current_timestamp as creation_date
	         , -1 as last_updated_by
	         , current_timestamp as last_update_date
	         , 'N' as del_flag
        from all_temp
      ;

      v_dml_row_count := sql%rowcount;	-- 收集数据量

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => '传入版本编码： '||p_version_code||'，数据入到目标表 dm_fop_spart_data_his_ytd_t 的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
      );

      -- 清理目标表数据（此目标表只保留最新版本数据）
    truncate table fin_dm_opt_fop.dm_fop_spart_data_ytd_t;

    insert into fin_dm_opt_fop.dm_fop_spart_data_ytd_t(
           period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_ytd
         , equip_cost_cons_before_ytd
         , spart_ytd
         , avg_cost_ytd
         , avg_price_ytd
         , rev_percent_ytd
         , cost_percent_ytd
         , mgp_ratio_ytd
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_ytd
         , GS_ENCRYPT(equip_cost_cons_before_ytd,p_keystr,'AES128', 'CBC', 'SHA256') as equip_cost_cons_before_ytd
         , spart_ytd
         , GS_ENCRYPT(avg_cost_ytd,p_keystr,'AES128', 'CBC', 'SHA256') as avg_cost_ytd
         , avg_price_ytd
         , rev_percent_ytd
         , cost_percent_ytd
         , mgp_ratio_ytd
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t
     where version_code = p_version_code
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '传入版本编码： '||p_version_code||'，数据入到目标表 dm_fop_spart_data_ytd_t 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    );

    else

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入版本编码： '||p_version_code||'，来源表中没有此版本的数据，请重新传入，版本格式：yyyymm_V1...VN',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => null,
          p_log_errbuf => null  --错误编码
      );

      x_success_flag := 2001;
      return;

    end if;

  end if;

  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.p_dm_pf_capture_exception(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_data_his_ytd_t;
	analyse fin_dm_opt_fop.dm_fop_spart_data_ytd_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

