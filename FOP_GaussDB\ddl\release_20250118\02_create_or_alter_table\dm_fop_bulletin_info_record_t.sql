-- ----------------------------
-- Table structure for dm_fop_bulletin_info_record_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t" (
  "app_id" varchar(500) COLLATE "pg_catalog"."default",
  "sub_app_name" varchar(500) COLLATE "pg_catalog"."default",
  "bulletin_batch_no" varchar(500) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "status" varchar(20) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."app_id" IS '应用id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."sub_app_name" IS '子应该名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."bulletin_batch_no" IS '公告批次号';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."user_id" IS '用户id';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."status" IS '状态 Y|N';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_bulletin_info_record_t" IS '公告信息阅读记录表';

