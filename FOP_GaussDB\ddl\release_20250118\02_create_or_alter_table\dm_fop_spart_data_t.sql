-- ----------------------------
-- Table structure for dm_fop_spart_data_t
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_spart_data_t";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_spart_data_t" (
  "period_id" numeric,
  "spart_code" varchar(150) COLLATE "pg_catalog"."default",
  "spart_desc" varchar(2000) COLLATE "pg_catalog"."default",
  "bg_code" varchar(50) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(200) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(200) COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(50) COLLATE "pg_catalog"."default",
  "lv1_prod_rnd_team_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rnd_team_code" varchar(100) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "l1_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_name" varchar(100) COLLATE "pg_catalog"."default",
  "l2_coefficient" numeric(38,6),
  "currency" varchar(50) COLLATE "pg_catalog"."default",
  "equip_rev_cons_before_amt" numeric(38,10),
  "equip_cost_cons_before_amt" varchar(5000) COLLATE "pg_catalog"."default",
  "spart_qty" numeric(38,10),
  "avg_cost" varchar(5000) COLLATE "pg_catalog"."default",
  "avg_price" numeric(38,10),
  "rev_percent" numeric(38,10),
  "cost_percent" numeric(38,10),
  "mgp_ratio" numeric(38,10),
  "articulation_flag" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."spart_code" IS 'spart编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."spart_desc" IS 'spart描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."bg_code" IS 'BG编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."bg_cn_name" IS 'BG中文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."bg_en_name" IS 'BG英文名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."oversea_desc" IS '区域（全球/国内/海外）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv1_prod_rnd_team_code" IS '重量级团队LV1编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv1_prod_rd_team_cn_name" IS '重量级团队LV1描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv1_prod_rd_team_en_name" IS '重量级团队LV1英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv2_prod_rnd_team_code" IS '重量级团队LV2编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv2_prod_rd_team_cn_name" IS '重量级团队LV2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."lv2_prod_rd_team_en_name" IS '重量级团队LV2英文描述';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."l1_name" IS 'L1名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."l2_name" IS 'L2名称';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."l2_coefficient" IS 'L2系数';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."currency" IS '币种';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."equip_rev_cons_before_amt" IS '设备收入额(对价前)';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."equip_cost_cons_before_amt" IS '设备成本额(对价前)';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."spart_qty" IS '收入量（历史）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."avg_cost" IS '平均成本';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."avg_price" IS '平均价格';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."rev_percent" IS '收入占比';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."cost_percent" IS '成本占比';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."mgp_ratio" IS '制毛率';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."articulation_flag" IS '勾稽方法标签（SCENO1、场景一 SCENO2、场景二 SCENO3、场景三）';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."remark" IS '备注';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."created_by" IS '创建人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."creation_date" IS '创建时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."last_updated_by" IS '修改人';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."last_update_date" IS '修改时间';
COMMENT ON COLUMN "fin_dm_opt_fop"."dm_fop_spart_data_t"."del_flag" IS '是否删除';
COMMENT ON TABLE "fin_dm_opt_fop"."dm_fop_spart_data_t" IS 'Spart粒度数据表（只保留最大版本数据）';

