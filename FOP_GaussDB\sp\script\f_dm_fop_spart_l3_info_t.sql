-- ----------------------------
-- Function structure for f_dm_fop_spart_l3_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l3_info_t"("p_version_code" varchar, "p_period_begin" int4, "p_period_end" int4, "p_year" int4, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l3_info_t"(IN "p_version_code" varchar=NULL::character varying, IN "p_period_begin" int4=NULL::integer, IN "p_period_end" int4=NULL::integer, IN "p_year" int4=NULL::integer, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*********************************************************************************************************************************************************************
创建时间：2023-12-5
创建人  ：qwx1110218
背景描述：SPART明细+L2~L3/L1~L3系数+type类型 表按照逻辑加工入到作业对象L3层级，全量抽取，支持删除重跑
参数描述：参数一(p_version_code)：版本编码，参数格式：202207_V1
		      参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l3_info_t()

*********************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l3_info_t('||p_version_code||','||p_period_begin||','||p_period_end||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l3_info_t';
	v_version_code varchar(50);  -- 版本编码
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_phase_date       varchar(50);
	v_dml_row_count  number default 0 ;
	v_dml_row_count2  number default 0 ;

begin
  set enable_force_vector_engine to on;
	v_phase_date := 'SNULL';
	x_success_flag := '1';

  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L3层级数据'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 spart_detail_info_tmp 临时表
  drop table if exists spart_detail_info_tmp;
	create temporary table spart_detail_info_tmp(
	      version_code                   varchar(50)        -- 版本编码
	   	, period_id                      numeric            -- 会计期
			, phase_date                     varchar(60)        -- 会计期次
			, bg_code                        varchar(50)        -- BG编码
			, bg_name                        varchar(200)       -- BG名称
			, oversea_desc                   varchar(20)        -- 区域描述
			, lv1_code                       varchar(50)        -- 重量级团队lv1编码
			, lv1_name                       varchar(600)       -- 重量级团队lv1名称
			, lv2_code                       varchar(50)        -- 重量级团队lv2编码
			, lv2_name                       varchar(600)       -- 重量级团队lv2名称
			, l1_name                        varchar(200)       -- l1名称
			, l2_name                        varchar(200)       -- l2名称
			, l3_name                        varchar(200)       -- l3名称
			, l1_coefficient                 numeric(38,10)     -- l1系数
			, l2_coefficient                 numeric(38,10)     -- l2系数
			, l3_coefficient                 numeric(38,10)     -- l3系数
			, currency                       varchar(50)        -- 币种
			, equip_rev_cons_before_amt      numeric(38,10)     -- 设备收入额（对价前）
			, equip_cost_cons_before_amt     numeric(38,10)     -- 设备成本额（对价前）
			, equip_rev_cons_after_amt       numeric(38,10)     -- 设备收入额（对价后）
			, equip_cost_cons_after_amt      numeric(38,10)     -- 设备成本金额（对价后）
			, plan_qty                       numeric(38,10)     -- 发货量（SNOP）
			, ship_qty                       numeric(38,10)     -- 发货量（历史）
			, spart_qty                      numeric(38,10)     -- 收入量（历史）
			, articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			, source_table                   varchar(100)       -- 来源表
	)on commit preserve rows distribute by /*replication --*/hash(period_id,lv1_code,lv2_code,l1_name)
	;

	-- 创建 l2_all_phase_info_tmp 临时表
  drop table if exists l2_all_phase_info_tmp;
	create temporary table l2_all_phase_info_tmp(
	      version_code                   varchar(50)        -- 版本编码
	    , period_id                      numeric            -- 会计期
			, phase_date                     varchar(60)        -- 会计期次
			, bg_code                        varchar(50)        -- BG编码
			, bg_name                        varchar(200)       -- BG名称
			, oversea_desc                   varchar(20)        -- 海外标志
			, lv1_code                       varchar(50)        -- 重量级团队lv1编码
			, lv1_name                       varchar(600)       -- 重量级团队lv1名称
			, lv2_code                       varchar(50)        -- 重量级团队lv2编码
			, lv2_name                       varchar(600)       -- 重量级团队lv2名称
			, l1_name                        varchar(200)       -- l1名称
			, l2_name                        varchar(200)       -- l2名称
			, l3_name                        varchar(200)       -- l3名称
			, l3_coefficient                 numeric(38,10)     -- l3系数
			, currency                       varchar(50)        -- 币种
			, articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;

	-- 创建 bg_name_temp 临时表
  drop table if exists bg_name_temp;
	create temporary table bg_name_temp(
	     version_code   varchar(50)                 -- 版本编码
	   , period_id      numeric                     -- 会计期
     , phase_date     varchar(50)
     , bg_code        varchar(50)                 -- bg编码
     , bg_name        varchar(100)                -- bg名称
     , oversea_desc   varchar(50)                 -- 区域描述
     , lv1_code       varchar(50)                 -- 重量级团队lv1编码
     , lv1_name       varchar(100)                -- 重量级团队lv1描述
     , lv2_code       varchar(50)                 -- 重量级团队lv2编码
     , lv2_name       varchar(100)                -- 重量级团队lv2名称
     , l1_name        varchar(200)                -- l1名称
     , l2_name        varchar(200)                -- l2名称
     , l3_name        varchar(200)                -- l3名称
     , l2_coefficient numeric(38,10)              -- l2系数
     , l3_coefficient numeric(38,10)              -- l3系数
     , currency     varchar(10)
     , articulation_flag varchar(50)
     , equip_rev_cons_before_amt  numeric(38,10)  -- 收入金额(对价前)
     , equip_cost_cons_before_amt numeric(38,10)  -- 成本金额(对价前)
     , equip_rev_cons_after_amt   numeric(38,10)  -- 设备收入金额(对价后)
     , equip_cost_cons_after_amt  numeric(38,10)  -- 设备成本金额(对价后)
     , plan_qty  numeric(38,10)                   -- 发货量（snop）
     , ship_qty  numeric(38,10)                   -- 发货量（历史）
     , spart_qty numeric(38,10)                   -- 收入量（历史）
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;

	-- 创建 l2_temp 临时表
  drop table if exists l2_temp;
	create temporary table l2_temp(
       version_code   varchar(50)      -- 版本编码
     , period_id      numeric          --	会计期
     , phase_date     varchar(50)
     , bg_code        varchar(50)      --	bg编码
     , bg_name        varchar(100)     --	bg名称
     , oversea_desc   varchar(50)      -- 区域描述
     , lv1_code       varchar(50)      --	重量级团队lv1编码
     , lv2_code       varchar(50)      --	重量级团队lv2编码
     , l1_name        varchar(500)     --	l1名称
     , l2_name        varchar(500)     --	l2名称
     , currency            varchar(10)             -- 币种
     , articulation_flag   varchar(50)
     , equip_rev_cons_before_amt  numeric(38,10)   -- l1对价前收入金额
     , plan_qty            numeric(38,10)          -- 发货量（snop）
     , ship_qty            numeric(38,10)          -- 发货量（历史）
     , spart_qty           numeric(38,10)          -- 收入量（历史）
	)on commit preserve rows distribute by /*replication --*/hash(period_id,l1_name,l2_name)
	;

	-- 创建 l3_temp 临时表
  drop table if exists l3_temp;
	create temporary table l3_temp(
       version_code  varchar(50)   -- 版本编码
     , period_id     numeric       --	会计期
     , phase_date    varchar(50)
     , bg_code       varchar(50)   --	bg编码
     , bg_name       varchar(100)  --	bg名称
     , oversea_desc  varchar(50)   -- 区域描述
     , lv1_code      varchar(50)   -- 重量级团队lv1编码
     , lv1_name      varchar(200)  -- 重量级团队lv1描述
     , lv2_code      varchar(50)   -- 重量级团队lv2编码
     , lv2_name      varchar(200)  -- 重量级团队lv2名称
     , l1_name       varchar(200)  --	l1名称
     , l2_name       varchar(200)  -- l2名称
     , l3_name       varchar(200)  -- l3名称
     , l3_coefficient numeric(38,10) -- l3系数
     , currency  varchar(10)
     , articulation_flag  varchar(50)
     , equip_rev_cons_before_amt  numeric(38,10)  -- 收入金额(对价前)
     , equip_cost_cons_before_amt numeric(38,10)  -- 成本金额(对价前)
     , equip_rev_cons_after_amt   numeric(38,10)  -- 设备收入金额(对价后)
     , equip_cost_cons_after_amt  numeric(38,10)  -- 设备成本金额(对价后)
     , plan_qty  numeric(38,10)  --	发货量（snop）
     , ship_qty  numeric(38,10)  --	发货量（历史）
     , spart_qty numeric(38,10)  -- 收入量（历史）
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;

	-- 创建 all_temp 临时表
  drop table if exists all_temp;
	create temporary table all_temp(
       version_code      varchar(50)    -- 版本编码
     , period_id				 numeric        -- 会计期
     , phase_date        varchar(50)
     , bg_code           varchar(50)    -- bg编码
     , bg_name           varchar(100)   -- bg名称
     , oversea_desc      varchar(50)    -- 区域
     , lv1_code          varchar(50)    -- 重量级团队lv1编码
     , lv1_name					 varchar(200)   -- 重量级团队lv1描述
     , lv2_code					 varchar(50)    -- 重量级团队lv2编码
     , lv2_name					 varchar(200)	  -- 重量级团队lv2名称
     , l1_name					 varchar(200)	  -- l1名称
     , l2_name					 varchar(200)	  -- l2名称
     , l3_name					 varchar(200)	  -- l3名称
     , l3_coefficient    numeric(38,16) -- l3系数
     , currency					 varchar(10)	  -- 币种
     , equip_rev_cons_before_amt		numeric(38,10)	-- 设备收入额(对价前)
     , equip_cost_cons_before_amt	  numeric(38,10)	-- 设备成本额(对价前)
     , plan_qty	   numeric(38,10)	     -- 发货量（snop）
     , ship_qty    numeric(38,10)	     -- 发货量（历史）
     , spart_qty   numeric(38,10)	     -- 收入量（历史）
     , unit_cost   numeric(38,10)	     -- 单位成本
     , unit_price  numeric(38,10)	     -- 单位价格
     , rev_percent numeric(38,10)      -- 收入占比
     , mgp_ratio   numeric(38,10)	     -- 制毛率
     , articulation_flag  varchar(50)  -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
     , del_ind            varchar(10)
  )on commit preserve rows distribute by /*replication --*/ hash(period_id,phase_date,l1_name)
  ;

	-- 取来源表最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_detail_info_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_info_t where del_flag = 'N')
     and del_flag = 'N'
   group by substr(version_code,1,6)
	;
	
	-- 取来源表最大版本的S&OP预算期次年月
	if exists(select phase_date from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = v_max_version_code and source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t' and del_flag = 'N') then
  select min(substr(phase_date,position('-' in phase_date)+1)) into v_phase_date
    from fin_dm_opt_fop.dm_fop_spart_detail_info_t
   where version_code = v_max_version_code
     and source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
     and del_flag = 'N'
	;
  end if;

  -- 传入参数一非空，其它传入参数都空
	if((p_version_code is not null or p_version_code <> '') and p_period_begin is null and p_period_end is null  /*and p_year is null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数二、传入参数三不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数一、二非空，其它传入参数都空
	elseif((p_version_code is not null or p_version_code <> '') and p_period_begin is not null and p_period_end is null  /*and p_year is null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数三不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数二为空，其它传入参数都非空
	elseif((p_version_code is not null or p_version_code <> '') and p_period_begin is null and p_period_end is not null  /*and p_year is not null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数二不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数三为空，其它传入参数都非空
	elseif((p_version_code is not null or p_version_code <> '') and p_period_begin is not null and p_period_end is null  /*and p_year is not null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数三不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数一为空，其它传入参数都非空
	elseif((p_version_code is null or p_version_code = '') and p_period_begin is not null and p_period_end is not null  /*and p_year is not null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数一不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数一、二为空，其它传入参数都非空
	elseif((p_version_code is null or p_version_code = '') and p_period_begin is null and p_period_end is not null  /*and p_year is not null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数一、传入参数二不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数二有值，其它传入参数都空
	elseif((p_version_code is null or p_version_code = '') and p_period_begin is not null and p_period_end is null  /*and p_year is null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数一、传入参数三不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 传入参数三有值，其它传入参数都空
	elseif((p_version_code is null or p_version_code = '') and p_period_begin is null and p_period_end is not null  /*and p_year is null*/) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入参数一、传入参数二不能为空，请重新传入正确格式的参数，对应的格式分别为：yyyymm_V1...N，yyyymmdd，yyyymmdd，yyyy ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

  -- 判断传入的开始日期是否大于等于结束日期
  elseif(p_period_begin >= p_period_end) then
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入参数一：'||p_version_code||'，传入参数二：'||p_period_begin||'，传入参数三：'||p_period_end||'，传入参数四：'||p_year||'，传入的开始日期不能大于等于结束日期，请重新传入正确格式的参数！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    x_success_flag := 2001;
    return;

	-- 传入版本没值，取最大版本的数据
	elseif((p_version_code is null or p_version_code = '') and p_period_begin is null and p_period_end is null  and p_year is null ) then
	  -- 判断来源表中是否有最大版本，如有，需要删除目标表中最大版本数据
	  delete fin_dm_opt_fop.dm_fop_spart_l3_info_his_t where version_code = v_max_version_code;

    -- S&OP预算需要根据期次判断是取当年还是取下一年数据，所以S&OP逻辑需要单独取
    -- 传入版本空值，S&OP预算需要根据系统当前年月判断：如果<10月取期次年份的全年数据
    if(v_phase_date <> 'SNULL' and substr(v_phase_date,5,2) < 10) then
      insert into spart_detail_info_tmp(
	        version_code                     -- 版本编码
	     	, period_id                        -- 会计期
	  		, phase_date                       -- 会计期次
	  		, bg_code                          -- BG编码
	  		, bg_name                          -- BG名称
	  		, oversea_desc                     -- 区域描述
	  		, lv1_code                         -- 重量级团队lv1编码
	  		, lv1_name                         -- 重量级团队lv1名称
	  		, lv2_code                         -- 重量级团队lv2编码
	  		, lv2_name                         -- 重量级团队lv2名称
	  		, l1_name                          -- l1名称
	  		, l2_name                          -- l2名称
	  		, l3_name                          -- l3名称
	  		, l1_coefficient                   -- l1系数
	  		, l2_coefficient                   -- l2系数
        , l3_coefficient                   -- l3系数
        , currency                         -- 币种
	  		, equip_rev_cons_before_amt        -- 设备收入额（对价前）
	  		, equip_cost_cons_before_amt       -- 设备成本额（对价前）
	  		, equip_rev_cons_after_amt         -- 设备收入额（对价后）
	  		, equip_cost_cons_after_amt        -- 设备成本 金额 对价后
	  		, plan_qty                         -- 发货量（SNOP）
	  		, ship_qty                         -- 发货量（历史）
	  		, spart_qty                        -- 收入量（历史）
	  		, articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  		, source_table 
	    )
	    select t.version_code  -- 版本编码
	         , t.period_id     --	会计期
	    		 , t.phase_date    -- 会计期次
	    		 , t.bg_code       --	bg编码
	    		 , t.bg_name       --	bg名称
	  		   , (case when t.oversea_flag = 'Y' then '海外'
		               when t.oversea_flag = 'N' then '国内'
		          end)                       as oversea_desc  -- 区域描述
	    		 , t.lv1_prod_rnd_team_code    as lv1_code      -- 重量级团队lv1编码
	    		 , t.lv1_prod_rd_team_cn_name  as lv1_name      -- 重量级团队lv1描述
	    		 , t.lv2_prod_rnd_team_code    as lv2_code      -- 重量级团队lv2编码
	    		 , t.lv2_prod_rd_team_cn_name  as lv2_name      -- 重量级团队lv2名称
	    		 , t.l1_name   --	l1名称
	    		 , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)    as l2_name         -- l2名称
	    		 , t.l3_name   --	l3名称
	    		 , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
	    		 , round(t.l2_coefficient,6)        as l2_coefficient  -- l2系数
	    		 , round(t.l3_coefficient,6)        as l3_coefficient  -- l3系数
	    		 , 'CNY'                            as currency
	    		 , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt  -- 收入金额(对价前)
	    		 , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt -- 成本金额(对价前)
	    		 , sum(nvl(t.equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
	    		 , sum(nvl(t.equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
	    		 , sum(nvl(t.snop_plan_quantity,0)) as plan_qty  --	发货量（SNOP）
	    		 , sum(nvl(t.ship_qty,0))           as ship_qty  --	发货量（历史）
	    		 , sum(nvl(t.spart_qty,0))          as spart_qty --	收入量（历史）
	    		 , articulation_flag
	    		 , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_max_version_code  -- 取最大版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	       and substr(t.period_id,1,4) = substr(v_phase_date,1,4)  -- 取期次年份的全年数据，取最大版本的所有期次
	       --and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- to_char(to_date(substr(v_max_version_code,1,6),'yyyymm') - interval'1 month','yyyymm') -- 取最大版本年月的上月期次数据
	       and t.del_flag = 'N'
       group by t.version_code
	         , t.period_id
	    		 , t.phase_date
	    		 , t.bg_code
	    		 , t.bg_name
	  		   , (case when t.oversea_flag = 'Y' then '海外'
		               when t.oversea_flag = 'N' then '国内'
		          end)
	    		 , t.lv1_prod_rnd_team_code
	    		 , t.lv1_prod_rd_team_cn_name
	    		 , t.lv2_prod_rnd_team_code
	    		 , t.lv2_prod_rd_team_cn_name
	    		 , t.l1_name
	    		 , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
	    		 , t.l3_name
	    		 , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
	    		 , round(t.l2_coefficient,6)
	    		 , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    union all
	    select t.version_code  -- 版本编码
	         , t.period_id     --	会计期
	    		 , t.phase_date    -- 会计期次
	    		 , t.bg_code       --	bg编码
	    		 , t.bg_name       --	bg名称
	  		   , (case when t.oversea_flag = 'Y' then '海外'
		               when t.oversea_flag = 'N' then '国内'
		          end)                       as oversea_desc  -- 区域描述
	    		 , t.lv1_prod_rnd_team_code    as lv1_code      -- 重量级团队lv1编码
	    		 , t.lv1_prod_rd_team_cn_name  as lv1_name      -- 重量级团队lv1描述
	    		 , t.lv2_prod_rnd_team_code    as lv2_code      -- 重量级团队lv2编码
	    		 , t.lv2_prod_rd_team_cn_name  as lv2_name      -- 重量级团队lv2名称
	    		 , t.l1_name   --	l1名称
	    		 , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)    as l2_name         -- l2名称
	    		 , t.l3_name   --	l3名称
	    		 , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
	    		 , round(t.l2_coefficient,6)                                                      as l2_coefficient  -- l2系数
	    		 , round(t.l3_coefficient,6)                                                      as l3_coefficient  -- l3系数
	    		 , 'USD'                            as currency
	    		 , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt  -- 收入金额(对价前)
	    		 , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt -- 成本金额(对价前)
	    		 , sum(nvl(t.equip_rev_usd_amt,0))  as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
	    		 , sum(nvl(t.equip_cost_usd_amt,0)) as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
	    		 , sum(nvl(t.snop_plan_quantity,0)) as plan_qty  --	发货量（SNOP）
	    		 , sum(nvl(t.ship_qty,0))           as ship_qty  --	发货量（历史）
	    		 , sum(nvl(t.spart_qty,0))          as spart_qty --	收入量（历史）
	    		 , articulation_flag
	    		 , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_max_version_code  -- 取最大版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	       and substr(t.period_id,1,4) = substr(v_phase_date,1,4)  -- 取期次年份的全年数据，取最大版本的所有期次
	       --and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- to_char(to_date(substr(v_max_version_code,1,6),'yyyymm') - interval'1 month','yyyymm') -- 取最大版本年月的上月期次数据
       group by t.version_code
	         , t.period_id
	    		 , t.phase_date
	    		 , t.bg_code
	    		 , t.bg_name
	  		   , (case when t.oversea_flag = 'Y' then '海外'
		               when t.oversea_flag = 'N' then '国内'
		          end)
	    		 , t.lv1_prod_rnd_team_code
	    		 , t.lv1_prod_rd_team_cn_name
	    		 , t.lv2_prod_rnd_team_code
	    		 , t.lv2_prod_rd_team_cn_name
	    		 , t.l1_name
	    		 , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
	    		 , t.l3_name
	    		 , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
	    		 , round(t.l2_coefficient,6)
	    		 , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    ;
	    
	    v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，S&OP预算数据（期次：取最大版本年月的上月期次数据，会计期：取最大版本年份的全年数据）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    
    -- 如果>=10月取期次年份下一年的全年
    elseif(v_phase_date <> 'SNULL' and substr(v_phase_date,5,2) >= 10) then
      insert into spart_detail_info_tmp(
	           version_code                     -- 版本编码
           , period_id                        -- 会计期
           , phase_date                       -- 会计期次
           , bg_code                          -- BG编码
           , bg_name                          -- BG名称
           , oversea_desc                     -- 区域描述
           , lv1_code                         -- 重量级团队lv1编码
           , lv1_name                         -- 重量级团队lv1名称
           , lv2_code                         -- 重量级团队lv2编码
           , lv2_name                         -- 重量级团队lv2名称
           , l1_name                          -- l1名称
           , l2_name                          -- l2名称
           , l3_name                          -- l3名称
           , l1_coefficient                   -- l1系数
           , l2_coefficient                   -- l2系数
           , l3_coefficient                   -- l3系数
           , currency                         -- 币种
           , equip_rev_cons_before_amt        -- 设备收入额（对价前）
           , equip_cost_cons_before_amt       -- 设备成本额（对价前）
           , equip_rev_cons_after_amt         -- 设备收入额（对价后）
           , equip_cost_cons_after_amt        -- 设备成本 金额 对价后
           , plan_qty                         -- 发货量（SNOP）
           , ship_qty                         -- 发货量（历史）
           , spart_qty                        -- 收入量（历史）
           , articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
           , source_table
	    )
	    select t.version_code   -- 版本编码
           , t.period_id      -- 会计期
           , t.phase_date     -- 会计期次
           , t.bg_code        -- bg编码
           , t.bg_name        -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)                       as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code    as lv1_code      --	重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name  as lv1_name      --	重量级团队lv1描述
           , t.lv2_prod_rnd_team_code    as lv2_code      --	重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name  as lv2_name      --	重量级团队lv2名称
           , t.l1_name   -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)    as l2_name  -- l2名称
           , t.l3_name   -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6)           as l2_coefficient               -- l2系数
           , round(t.l3_coefficient,6)           as l3_coefficient               -- l3系数
           , 'CNY'                               as currency
           , sum(nvl(t.rmb_revenue,0))           as equip_rev_cons_before_amt    -- 收入金额(对价前)
           , sum(nvl(t.rmb_cost,0))              as equip_cost_cons_before_amt   -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_rmb_amt,0))     as equip_rev_cons_after_amt     -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_rmb_amt,0))    as equip_cost_cons_after_amt    -- 设备成本金额(对价后)
           , sum(nvl(t.snop_plan_quantity,0))    as plan_qty                     -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))              as ship_qty                     --	发货量（历史）
           , sum(nvl(t.spart_qty,0))             as spart_qty                    --	收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_max_version_code  -- 取最大版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	       and substr(t.period_id,1,4) = substr(v_phase_date,1,4)+1  -- 取期次年份+1的全年数据，取最大版本的所有期次
	       --and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- to_char(to_date(substr(v_max_version_code,1,6),'yyyymm') - interval'1 month','yyyymm') -- 取最大版本年月的上月期次数据
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    union all
	    select t.version_code   -- 版本编码
           , t.period_id      -- 会计期
           , t.phase_date     -- 会计期次
           , t.bg_code        -- bg编码
           , t.bg_name        -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)                       as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code    as lv1_code      --	重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name  as lv1_name      --	重量级团队lv1描述
           , t.lv2_prod_rnd_team_code    as lv2_code      --	重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name  as lv2_name      --	重量级团队lv2名称
           , t.l1_name   -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)    as l2_name  -- l2名称
           , t.l3_name   -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6)           as l2_coefficient               -- l2系数
           , round(t.l3_coefficient,6)           as l3_coefficient               -- l3系数
           , 'USD'                               as currency
           , sum(nvl(t.usd_revenue,0))           as equip_rev_cons_before_amt    -- 收入金额(对价前)
           , sum(nvl(t.usd_cost,0))              as equip_cost_cons_before_amt   -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_usd_amt,0))     as equip_rev_cons_after_amt     -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_usd_amt,0))    as equip_cost_cons_after_amt    -- 设备成本金额(对价后)
           , sum(nvl(t.snop_plan_quantity,0))    as plan_qty                     -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))              as ship_qty                     --	发货量（历史）
           , sum(nvl(t.spart_qty,0))             as spart_qty                    --	收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_max_version_code  -- 取最大版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	       and substr(t.period_id,1,4) = substr(v_phase_date,1,4)+1  -- 取期次年份+1的全年数据，取最大版本的所有期次
	       --and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- to_char(to_date(substr(v_max_version_code,1,6),'yyyymm') - interval'1 month','yyyymm') -- 取最大版本年月的上月期次数据
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    ;
	    
	    v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，S&OP预算数据（期次：取最大版本年月的上月期次数据，会计期：取最大版本年份+1的全年数据）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    end if;


	  --处理币种字段，拆分为人民币和美元
	  insert into spart_detail_info_tmp(
	        version_code                     -- 版本编码
	     	, period_id                        -- 会计期
	  		, phase_date                       -- 会计期次
	  		, bg_code                          -- BG编码
	  		, bg_name                          -- BG名称
	  		, oversea_desc                     -- 区域描述
	  		, lv1_code                         -- 重量级团队lv1编码
	  		, lv1_name                         -- 重量级团队lv1名称
	  		, lv2_code                         -- 重量级团队lv2编码
	  		, lv2_name                         -- 重量级团队lv2名称
	  		, l1_name                          -- l1名称
	  		, l2_name                          -- l2名称
	  		, l3_name                          -- l3名称
	  		, l1_coefficient                   -- l1系数
	  		, l2_coefficient                   -- l2系数
	  		, l3_coefficient                   -- l3系数
	  		, currency                         -- 币种
	  		, equip_rev_cons_before_amt        -- 设备收入额（对价前）
	  		, equip_cost_cons_before_amt       -- 设备成本额（对价前）
	  		, equip_rev_cons_after_amt         -- 设备收入额（对价后）
	  		, equip_cost_cons_after_amt        -- 设备成本 金额 对价后
	  		, plan_qty                         -- 发货量（SNOP）
	  		, ship_qty                         -- 发货量（历史）
	  		, spart_qty                        -- 收入量（历史）
	  		, articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	  		, source_table
	  )
	  select t.version_code    -- 版本编码
         , t.period_id   --	会计期
         , t.phase_date  --会计期次
         , t.bg_code     -- bg编码
         , t.bg_name     -- bg名称
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
            end)                      as oversea_desc   -- 区域描述
         , t.lv1_prod_rnd_team_code   as lv1_code       -- 重量级团队lv1编码
         , t.lv1_prod_rd_team_cn_name as lv1_name       -- 重量级团队lv1描述
         , t.lv2_prod_rnd_team_code   as lv2_code       -- 重量级团队lv2编码
         , t.lv2_prod_rd_team_cn_name as lv2_name       -- 重量级团队lv2名称
         , t.l1_name    -- l1名称
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end ) as l2_name   -- l2名称
         , t.l3_name    -- l3名称
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
         , round(t.l2_coefficient,6) as l2_coefficient  -- l2系数
         , round(t.l3_coefficient,6) as l3_coefficient  -- l3系数
         , 'CNY'                     as currency
         , sum(nvl(t.rmb_revenue,0))                                 as equip_rev_cons_before_amt   -- 收入金额(对价前)
         , sum(nvl(t.rmb_cost,0))                                    as equip_cost_cons_before_amt  -- 成本金额(对价前)
         , sum(nvl(t.equip_rev_rmb_amt,0))                           as equip_rev_cons_after_amt    -- 设备收入金额(对价后)
         , sum(nvl(t.equip_cost_rmb_amt,0))                          as equip_cost_cons_after_amt   -- 设备成本金额(对价后)
         , sum(nvl(t.snop_quantity,0) + nvl(t.snop_plan_quantity,0)) as plan_qty                    -- 发货量（SNOP）
         , sum(nvl(t.ship_qty,0))                                    as ship_qty                    -- 发货量（历史）
         , sum(nvl(t.spart_qty,0))                                   as spart_qty                   -- 收入量（历史）
         , articulation_flag
         , t.source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = v_max_version_code  -- 取最大版本数据
	     and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	     and t.del_flag = 'N'
     group by t.version_code
         , t.period_id
         , t.phase_date
         , t.bg_code
         , t.bg_name
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
            end)
         , t.lv1_prod_rnd_team_code
         , t.lv1_prod_rd_team_cn_name
         , t.lv2_prod_rnd_team_code
         , t.lv2_prod_rd_team_cn_name
         , t.l1_name
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end )
         , t.l3_name
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
         , round(t.l2_coefficient,6)
         , round(t.l3_coefficient,6)
	  		 , t.articulation_flag
	  		 , t.source_table
    union all
	  select t.version_code    -- 版本编码
         , t.period_id   --	会计期
         , t.phase_date  --会计期次
         , t.bg_code     -- bg编码
         , t.bg_name     -- bg名称
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
            end)                      as oversea_desc   -- 区域描述
         , t.lv1_prod_rnd_team_code   as lv1_code       -- 重量级团队lv1编码
         , t.lv1_prod_rd_team_cn_name as lv1_name       -- 重量级团队lv1描述
         , t.lv2_prod_rnd_team_code   as lv2_code       -- 重量级团队lv2编码
         , t.lv2_prod_rd_team_cn_name as lv2_name       -- 重量级团队lv2名称
         , t.l1_name    -- l1名称
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end ) as l2_name   -- l2名称
         , t.l3_name    -- l3名称
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
         , round(t.l2_coefficient,6) as l2_coefficient  -- l2系数
         , round(t.l3_coefficient,6) as l3_coefficient  -- l3系数
         , 'USD'                     as currency
         , sum(nvl(t.usd_revenue,0))                                 as equip_rev_cons_before_amt   -- 收入金额(对价前)
         , sum(nvl(t.usd_cost,0))                                    as equip_cost_cons_before_amt  -- 成本金额(对价前)
         , sum(nvl(t.equip_rev_usd_amt,0))                           as equip_rev_cons_after_amt    -- 设备收入金额(对价后)
         , sum(nvl(t.equip_cost_usd_amt,0))                          as equip_cost_cons_after_amt   -- 设备成本金额(对价后)
         , sum(nvl(t.snop_quantity,0) + nvl(t.snop_plan_quantity,0)) as plan_qty                    -- 发货量（SNOP）
         , sum(nvl(t.ship_qty,0))                                    as ship_qty                    -- 发货量（历史）
         , sum(nvl(t.spart_qty,0))                                   as spart_qty                   -- 收入量（历史）
         , articulation_flag
         , t.source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = v_max_version_code  -- 取最大版本数据
	     and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	     and t.del_flag = 'N'
     group by t.version_code
         , t.period_id
         , t.phase_date
         , t.bg_code
         , t.bg_name
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
            end)
         , t.lv1_prod_rnd_team_code
         , t.lv1_prod_rd_team_cn_name
         , t.lv2_prod_rnd_team_code
         , t.lv2_prod_rd_team_cn_name
         , t.l1_name
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end )
         , t.l3_name
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
         , round(t.l2_coefficient,6)
         , round(t.l3_coefficient,6)
	  		 , t.articulation_flag
	  		 , t.source_table
	  union all
	  -- S&OP预测的取数逻辑与传入起始日期参数有关，所以逻辑需要单独取
	  -- 传入参数为空时，期次取系统当前年月的6号至上月6号
	  select t.version_code    -- 版本编码
         , t.period_id       -- 会计期
         , t.phase_date      -- 会计期次
         , t.bg_code         -- bg编码
         , t.bg_name         --	bg名称
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
                 when t.oversea_flag = 'G' then '全球'
                 else t.oversea_flag
            end)                      as oversea_desc -- 区域描述
         , t.lv1_prod_rnd_team_code   as lv1_code     -- 重量级团队lv1编码
         , t.lv1_prod_rd_team_cn_name as lv1_name     -- 重量级团队lv1描述
         , t.lv2_prod_rnd_team_code   as lv2_code     -- 重量级团队lv2编码
         , t.lv2_prod_rd_team_cn_name as lv2_name     -- 重量级团队lv2名称
         , t.l1_name  -- l1名称
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
         , t.l3_name  -- l3名称
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
         , round(t.l2_coefficient,6) as l2_coefficient -- l2系数
         , round(t.l3_coefficient,6) as l3_coefficient --	l3系数
         , 'CNY'                     as currency
         , sum(nvl(t.rmb_revenue,0))         as equip_rev_cons_before_amt  -- 收入金额(对价前)
         , sum(nvl(t.rmb_cost,0))            as equip_cost_cons_before_amt -- 成本金额(对价前)
         , sum(nvl(t.equip_rev_rmb_amt,0))   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
         , sum(nvl(t.equip_cost_rmb_amt,0))  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
         , sum(nvl(t.plan_unit_quantity,0))  as plan_qty                   -- 发货量（SNOP）
         , sum(nvl(t.ship_qty,0))            as ship_qty                   -- 发货量（历史）
         , sum(nvl(t.spart_qty,0))           as spart_qty                  -- 收入量（历史）
         , articulation_flag
         , t.source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = v_max_version_code  -- 取最大版本数据
	     and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'  -- S&OP预测
	     and t.phase_date >= to_char(add_months(current_date,-1),'yyyymm')||'06'   --上个月6号
	     and t.phase_date < to_char(current_date,'yyyymm')||'06'     --这个月6号
	     and substring(t.period_id,1,4) = to_char(current_date,'yyyy')        -- 取系统年份数据
	     and t.del_flag = 'N'
     group by t.version_code
         , t.period_id
         , t.phase_date
         , t.bg_code
         , t.bg_name
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
                 when t.oversea_flag = 'G' then '全球'
                 else t.oversea_flag
            end)
         , t.lv1_prod_rnd_team_code
         , t.lv1_prod_rd_team_cn_name
         , t.lv2_prod_rnd_team_code
         , t.lv2_prod_rd_team_cn_name
         , t.l1_name
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
         , t.l3_name
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
         , round(t.l2_coefficient,6)
         , round(t.l3_coefficient,6)
	  		 , t.articulation_flag
	  		 , t.source_table
    union all
	  select t.version_code    -- 版本编码
         , t.period_id       -- 会计期
         , t.phase_date      -- 会计期次
         , t.bg_code         -- bg编码
         , t.bg_name         --	bg名称
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
                 when t.oversea_flag = 'G' then '全球'
                 else t.oversea_flag
            end)                      as oversea_desc -- 区域描述
         , t.lv1_prod_rnd_team_code   as lv1_code     -- 重量级团队lv1编码
         , t.lv1_prod_rd_team_cn_name as lv1_name     -- 重量级团队lv1描述
         , t.lv2_prod_rnd_team_code   as lv2_code     -- 重量级团队lv2编码
         , t.lv2_prod_rd_team_cn_name as lv2_name     -- 重量级团队lv2名称
         , t.l1_name  -- l1名称
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
         , t.l3_name  -- l3名称
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
         , round(t.l2_coefficient,6) as l2_coefficient -- l2系数
         , round(t.l3_coefficient,6) as l3_coefficient --	l3系数
         , 'CNY'                     as currency
         , sum(nvl(t.rmb_revenue,0))         as equip_rev_cons_before_amt  -- 收入金额(对价前)
         , sum(nvl(t.rmb_cost,0))            as equip_cost_cons_before_amt -- 成本金额(对价前)
         , sum(nvl(t.equip_rev_rmb_amt,0))   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
         , sum(nvl(t.equip_cost_rmb_amt,0))  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
         , sum(nvl(t.plan_unit_quantity,0))  as plan_qty                   -- 发货量（SNOP）
         , sum(nvl(t.ship_qty,0))            as ship_qty                   -- 发货量（历史）
         , sum(nvl(t.spart_qty,0))           as spart_qty                  -- 收入量（历史）
         , articulation_flag
         , t.source_table
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = v_max_version_code  -- 取最大版本数据
	     and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'  -- S&OP预测
	     and t.phase_date >= to_char(add_months(current_date,-1),'yyyymm')||'06'   --上个月6号
	     and t.phase_date < to_char(current_date,'yyyymm')||'06'     --这个月6号
	     and substring(t.period_id,1,4) = to_char(current_date,'yyyy')        -- 取系统年份数据
	     and t.del_flag = 'N'
     group by t.version_code
         , t.period_id
         , t.phase_date
         , t.bg_code
         , t.bg_name
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
                 when t.oversea_flag = 'G' then '全球'
                 else t.oversea_flag
            end)
         , t.lv1_prod_rnd_team_code
         , t.lv1_prod_rd_team_cn_name
         , t.lv2_prod_rnd_team_code
         , t.lv2_prod_rd_team_cn_name
         , t.l1_name
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
         , t.l3_name
         , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
         , round(t.l2_coefficient,6)
         , round(t.l3_coefficient,6)
	  		 , t.articulation_flag
	  		 , t.source_table
	  ;

	  v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，非S&OP数据及S&OP预测数据（期次：系统当前年月6号至上月6号，会计期：系统当前年）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	elseif((p_version_code is not null or p_version_code <> '') and p_period_begin is not null and p_period_end is not null and p_period_begin < p_period_end) then
	  -- 需要根据传入版本判断来源表中是否有对应版本数据，如有，则删除目标表中对应的版本数据
	  if exists(select distinct version_code from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code and del_flag = 'N') then
	    select distinct version_code into v_version_code from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code and del_flag = 'N';
	    delete fin_dm_opt_fop.dm_fop_spart_l3_info_his_t where version_code = v_version_code;
      
      -- 取来源表最大版本的S&OP预算期次年月
	    if exists(select phase_date from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = v_version_code and source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t' and del_flag = 'N') then
      select min(substr(phase_date,position('-' in phase_date)+1)) into v_phase_date
        from fin_dm_opt_fop.dm_fop_spart_detail_info_t
       where version_code = v_version_code
         and source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
         and del_flag = 'N'
	    ;
     end if;
      
      -- 传入版本有值，S&OP预算需要根据系统当前年月判断：如果<10月取期次年份的全年数据
      if(v_phase_date <> 'SNULL' and substr(v_phase_date,5,2) < 10) then
        insert into spart_detail_info_tmp(
	          version_code                     -- 版本编码
	       	, period_id                        -- 会计期
	    		, phase_date                       -- 会计期次
	    		, bg_code                          -- BG编码
	    		, bg_name                          -- BG名称
	    		, oversea_desc                     -- 区域描述
	    		, lv1_code                         -- 重量级团队lv1编码
	    		, lv1_name                         -- 重量级团队lv1名称
	    		, lv2_code                         -- 重量级团队lv2编码
	    		, lv2_name                         -- 重量级团队lv2名称
	    		, l1_name                          -- l1名称
	    		, l2_name                          -- l2名称
	    		, l3_name                          -- l3名称
	    		, l1_coefficient                   -- l1系数
	    		, l2_coefficient                   -- l2系数
	    		, l3_coefficient                   -- l3系数
	    		, currency                         -- 币种
	    		, equip_rev_cons_before_amt        -- 设备收入额（对价前）
	    		, equip_cost_cons_before_amt       -- 设备成本额（对价前）
	    		, equip_rev_cons_after_amt         -- 设备收入额（对价后）
	    		, equip_cost_cons_after_amt        -- 设备成本 金额 对价后
	    		, plan_qty                         -- 发货量（SNOP）
	    		, ship_qty                         -- 发货量（历史）
	    		, spart_qty                        -- 收入量（历史）
	    		, articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		, source_table
	      )
	      select t.version_code   -- 版本编码
             , t.period_id      -- 会计期
             , t.phase_date     -- 会计期次
             , t.bg_code        -- bg编码
             , t.bg_name        -- bg名称
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)                      as oversea_desc  -- 区域描述
             , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
             , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
             , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
             , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
             , t.l1_name   --	l1名称
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end ) as l2_name   -- l2名称
             , t.l3_name   --	l3名称
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient   -- l1系数
             , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
             , round(t.l3_coefficient,6) as l3_coefficient   -- l3系数
             , 'CNY'                     as currency
             , sum(nvl(t.rmb_revenue,0))         as equip_rev_cons_before_amt  -- 收入金额(对价前)
             , sum(nvl(t.rmb_cost,0))            as equip_cost_cons_before_amt -- 成本金额(对价前)
             , sum(nvl(t.equip_rev_rmb_amt,0))   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
             , sum(nvl(t.equip_cost_rmb_amt,0))  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
             , sum(nvl(t.snop_plan_quantity,0))  as plan_qty                   -- 发货量（SNOP）
             , sum(nvl(t.ship_qty,0))            as ship_qty                   -- 发货量（历史）
             , sum(nvl(t.spart_qty,0))           as spart_qty                  -- 收入量（历史）
             , articulation_flag
             , t.source_table
	        from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	       where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	         and upper(t.industry_type) = 'TGT'
	         and t.version_code = p_version_code  -- 取传入版本数据
	         and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	         and substr(t.period_id,1,4) = substr(v_phase_date,1,4)  -- 取期次年份的全年数据，取传入版本的所有期次
	         --and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- substr(p_period_begin,1,6) -- 期次取传入开始日期年月的数据
	         and t.del_flag = 'N'
         group by t.version_code
             , t.period_id
             , t.phase_date
             , t.bg_code
             , t.bg_name
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)
             , t.lv1_prod_rnd_team_code
             , t.lv1_prod_rd_team_cn_name
             , t.lv2_prod_rnd_team_code
             , t.lv2_prod_rd_team_cn_name
             , t.l1_name
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end )
             , t.l3_name
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
             , round(t.l2_coefficient,6)
             , round(t.l3_coefficient,6)
	      		 , t.articulation_flag
	      		 , t.source_table
	      union all
	      select t.version_code   -- 版本编码
             , t.period_id      -- 会计期
             , t.phase_date     -- 会计期次
             , t.bg_code        -- bg编码
             , t.bg_name        -- bg名称
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)                      as oversea_desc  -- 区域描述
             , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
             , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
             , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
             , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
             , t.l1_name   --	l1名称
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end ) as l2_name   -- l2名称
             , t.l3_name   --	l3名称
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient   -- l1系数
             , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
             , round(t.l3_coefficient,6) as l3_coefficient   -- l3系数
             , 'USD'                     as currency
             , sum(nvl(t.usd_revenue,0))         as equip_rev_cons_before_amt  -- 收入金额(对价前)
             , sum(nvl(t.usd_cost,0))            as equip_cost_cons_before_amt -- 成本金额(对价前)
             , sum(nvl(t.equip_rev_usd_amt,0))   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
             , sum(nvl(t.equip_cost_usd_amt,0))  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
             , sum(nvl(t.snop_plan_quantity,0))  as plan_qty                   -- 发货量（SNOP）
             , sum(nvl(t.ship_qty,0))            as ship_qty                   -- 发货量（历史）
             , sum(nvl(t.spart_qty,0))           as spart_qty                  -- 收入量（历史）
             , articulation_flag
             , t.source_table
	        from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	       where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	         and upper(t.industry_type) = 'TGT'
	         and t.version_code = p_version_code  -- 取传入版本数据
	         and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	         and substr(t.period_id,1,4) = substr(v_phase_date,1,4)  -- 取期次年份的全年数据，取传入版本的所有期次
	         -- and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- substr(p_period_begin,1,6) -- 期次取传入开始日期年月的数据
	         and t.del_flag = 'N'
         group by t.version_code
             , t.period_id
             , t.phase_date
             , t.bg_code
             , t.bg_name
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)
             , t.lv1_prod_rnd_team_code
             , t.lv1_prod_rd_team_cn_name
             , t.lv2_prod_rnd_team_code
             , t.lv2_prod_rd_team_cn_name
             , t.l1_name
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end )
             , t.l3_name
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
             , round(t.l2_coefficient,6)
             , round(t.l3_coefficient,6)
	      		 , t.articulation_flag
	      		 , t.source_table
	      ;
	      
	      v_dml_row_count := sql%rowcount;  -- 收集数据量

	      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入版本：'||p_version_code||'，传入开始日期：'||p_period_begin||'，传入结束日期：'||p_period_end||'，S&OP预算数据（期次：期次取传入开始日期年月，会计期：取传入开始日期年份的全年）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        );
	      
      -- 如果>=10月取期次年份下一年的全年
      elseif(v_phase_date <> 'SNULL' and substr(v_phase_date,5,2) >= 10) then
        insert into spart_detail_info_tmp(
	          version_code                     -- 版本编码
	       	, period_id                        -- 会计期
	    		, phase_date                       -- 会计期次
	    		, bg_code                          -- BG编码
	    		, bg_name                          -- BG名称
	    		, oversea_desc                     -- 区域描述
	    		, lv1_code                         -- 重量级团队lv1编码
	    		, lv1_name                         -- 重量级团队lv1名称
	    		, lv2_code                         -- 重量级团队lv2编码
	    		, lv2_name                         -- 重量级团队lv2名称
	    		, l1_name                          -- l1名称
	    		, l2_name                          -- l2名称
	    		, l3_name                          -- l3名称
	    		, l1_coefficient                   -- l1系数
	    		, l2_coefficient                   -- l2系数
	    		, l3_coefficient                   -- l3系数
	    		, currency                         -- 币种
	    		, equip_rev_cons_before_amt        -- 设备收入额（对价前）
	    		, equip_cost_cons_before_amt       -- 设备成本额（对价前）
	    		, equip_rev_cons_after_amt         -- 设备收入额（对价后）
	    		, equip_cost_cons_after_amt        -- 设备成本 金额 对价后
	    		, plan_qty                         -- 发货量（SNOP）
	    		, ship_qty                         -- 发货量（历史）
	    		, spart_qty                        -- 收入量（历史）
	    		, articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		, source_table
	      )
	      select t.version_code    -- 版本编码
             , t.period_id       -- 会计期
             , t.phase_date      -- 会计期次
             , t.bg_code         -- bg编码
             , t.bg_name         -- bg名称
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)                      as oversea_desc  -- 区域描述
             , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
             , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
             , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
             , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
             , t.l1_name   -- l1名称
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
             , t.l3_name   -- l3名称
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient 	-- l1系数
             , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
             , round(t.l3_coefficient,6) as l3_coefficient   -- l3系数
             , 'CNY'                     as currency
             , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt  -- 收入金额(对价前)
             , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt -- 成本金额(对价前)
             , sum(nvl(t.equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
             , sum(nvl(t.equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
             , sum(nvl(t.snop_plan_quantity,0)) as plan_qty                   -- 发货量（SNOP）
             , sum(nvl(t.ship_qty,0))           as ship_qty                   -- 发货量（历史）
             , sum(nvl(t.spart_qty,0))          as spart_qty                  -- 收入量（历史）
             , articulation_flag
             , t.source_table
	        from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	       where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	         and upper(t.industry_type) = 'TGT'
	         and t.version_code = p_version_code  -- 取传入版本数据
	         and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	         and substr(t.period_id,1,4) = substr(v_phase_date,1,4)+1  -- 取期次年份+1的全年数据，取传入版本的所有期次
	         -- and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- substr(p_period_begin,1,6) -- 期次取传入开始日期年月的数据
	         and t.del_flag = 'N'
         group by t.version_code
             , t.period_id
             , t.phase_date
             , t.bg_code
             , t.bg_name
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)
             , t.lv1_prod_rnd_team_code
             , t.lv1_prod_rd_team_cn_name
             , t.lv2_prod_rnd_team_code
             , t.lv2_prod_rd_team_cn_name
             , t.l1_name
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
             , t.l3_name
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
             , round(t.l2_coefficient,6)
             , round(t.l3_coefficient,6)
	      		 , t.articulation_flag
	      		 , t.source_table
	      union all
	      select t.version_code    -- 版本编码
             , t.period_id       -- 会计期
             , t.phase_date      -- 会计期次
             , t.bg_code         -- bg编码
             , t.bg_name         -- bg名称
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)                      as oversea_desc  -- 区域描述
             , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
             , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
             , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
             , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
             , t.l1_name   -- l1名称
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
             , t.l3_name   -- l3名称
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient 	-- l1系数
             , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
             , round(t.l3_coefficient,6) as l3_coefficient   -- l3系数
             , 'USD'                     as currency
             , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt  -- 收入金额(对价前)
             , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt -- 成本金额(对价前)
             , sum(nvl(t.equip_rev_usd_amt,0))  as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
             , sum(nvl(t.equip_cost_usd_amt,0)) as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
             , sum(nvl(t.snop_plan_quantity,0)) as plan_qty                   -- 发货量（SNOP）
             , sum(nvl(t.ship_qty,0))           as ship_qty                   -- 发货量（历史）
             , sum(nvl(t.spart_qty,0))          as spart_qty                  -- 收入量（历史）
             , articulation_flag
             , t.source_table
	        from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	       where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	         and upper(t.industry_type) = 'TGT'
	         and t.version_code = p_version_code  -- 取传入版本数据
	         and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  -- S&OP预算
	         and substr(t.period_id,1,4) = substr(v_phase_date,1,4)+1  -- 取期次年份+1的全年数据，取传入版本的所有期次
	         -- and substr(t.phase_date,position('-' in t.phase_date)+1) = v_phase_date -- substr(p_period_begin,1,6) -- 期次取传入开始日期年月的数据
	         and t.del_flag = 'N'
         group by t.version_code
             , t.period_id
             , t.phase_date
             , t.bg_code
             , t.bg_name
             , (case when t.oversea_flag = 'Y' then '海外'
                     when t.oversea_flag = 'N' then '国内'
                end)
             , t.lv1_prod_rnd_team_code
             , t.lv1_prod_rd_team_cn_name
             , t.lv2_prod_rnd_team_code
             , t.lv2_prod_rd_team_cn_name
             , t.l1_name
             , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
             , t.l3_name
             , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
             , round(t.l2_coefficient,6)
             , round(t.l3_coefficient,6)
	      		 , t.articulation_flag
	      		 , t.source_table
	      ;
	      
	      v_dml_row_count := sql%rowcount;  -- 收集数据量

	      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入版本：'||p_version_code||'，传入开始日期：'||p_period_begin||'，传入结束日期：'||p_period_end||'，S&OP预算数据（期次：期次取传入开始日期年月，会计期：取传入开始日期年份+1的全年）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        );

      end if;

	    --处理币种字段，拆分为人民币和美元
	    insert into spart_detail_info_tmp(
	          version_code                     -- 版本编码
	       	, period_id                        -- 会计期
	    		, phase_date                       -- 会计期次
	    		, bg_code                          -- BG编码
	    		, bg_name                          -- BG名称
	    		, oversea_desc                     -- 区域描述
	    		, lv1_code                         -- 重量级团队lv1编码
	    		, lv1_name                         -- 重量级团队lv1名称
	    		, lv2_code                         -- 重量级团队lv2编码
	    		, lv2_name                         -- 重量级团队lv2名称
	    		, l1_name                          -- l1名称
	    		, l2_name                          -- l2名称
	    		, l3_name                          -- l3名称
	    		, l1_coefficient                   -- l1系数
	    		, l2_coefficient                   -- l2系数
	    		, l3_coefficient                   -- l3系数
	    		, currency                         -- 币种
	    		, equip_rev_cons_before_amt        -- 设备收入额（对价前）
	    		, equip_cost_cons_before_amt       -- 设备成本额（对价前）
	    		, equip_rev_cons_after_amt         -- 设备收入额（对价后）
	    		, equip_cost_cons_after_amt        -- 设备成本 金额 对价后
	    		, plan_qty                         -- 发货量（SNOP）
	    		, ship_qty                         -- 发货量（历史）
	    		, spart_qty                        -- 收入量（历史）
	    		, articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	    		, source_table
	    )
	    select t.version_code     -- 版本编码
           , t.period_id        -- 会计期
           , t.phase_date       -- 会计期次
           , t.bg_code          -- bg编码
           , t.bg_name          -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)                      as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code   as lv1_code -- 重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name as lv1_name -- 重量级团队lv1描述
           , t.lv2_prod_rnd_team_code   as lv2_code -- 重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name as lv2_name -- 重量级团队lv2名称
           , t.l1_name    -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
           , t.l3_name    -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6)        as l2_coefficient   -- l2系数
           , round(t.l3_coefficient,6)        as l3_coefficient   -- l3系数
           , 'CNY'                            as currency
           , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
           , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt  -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt -- 设备成本金额(对价后)
           , sum(nvl(t.snop_quantity,0) + nvl(t.snop_plan_quantity,0)) as plan_qty  -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))           as ship_qty  -- 发货量（历史）
           , sum(nvl(t.spart_qty,0))          as spart_qty -- 收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_version_code  -- 取传入版本数据
	       and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
      union all
	    select t.version_code     -- 版本编码
           , t.period_id        -- 会计期
           , t.phase_date       -- 会计期次
           , t.bg_code          -- bg编码
           , t.bg_name          -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)                      as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code   as lv1_code -- 重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name as lv1_name -- 重量级团队lv1描述
           , t.lv2_prod_rnd_team_code   as lv2_code -- 重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name as lv2_name -- 重量级团队lv2名称
           , t.l1_name    -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
           , t.l3_name    -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
           , round(t.l3_coefficient,6) as l3_coefficient   -- l3系数
           , 'USD' as currency
           , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
           , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_usd_amt,0))  as equip_rev_cons_after_amt  -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_usd_amt,0)) as equip_cost_cons_after_amt -- 设备成本金额(对价后)
           , sum(nvl(t.snop_quantity,0) + nvl(t.snop_plan_quantity,0)) as plan_qty  -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))  as ship_qty  -- 发货量（历史）
           , sum(nvl(t.spart_qty,0)) as spart_qty -- 收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_version_code  -- 取传入版本数据
	       and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    union all
	    -- S&OP预测的取数逻辑与传入起始日期参数有关，所以逻辑需要单独取
	    -- 传入参数有值时，期次取传入起始日期参数
	    select t.version_code    -- 版本编码
           , t.period_id       -- 会计期
           , t.phase_date      -- 会计期次
           , t.bg_code         -- bg编码
           , t.bg_name         -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
                   when t.oversea_flag = 'G' then '全球'
                   else t.oversea_flag
              end)                      as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
           , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
           , t.l1_name   -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
           , t.l3_name   -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6) as l2_coefficient  -- l2系数
           , round(t.l3_coefficient,6) as l3_coefficient  -- l3系数
           , 'CNY' as currency
           , sum(nvl(t.rmb_revenue,0)) as equip_rev_cons_before_amt  -- 收入金额(对价前)
           , sum(nvl(t.rmb_cost,0))    as equip_cost_cons_before_amt -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt  -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt -- 设备成本金额(对价后)
           , sum(nvl(t.plan_unit_quantity,0)) as plan_qty -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))           as ship_qty -- 发货量（历史）
           , sum(nvl(t.spart_qty,0))          as spart_qty --收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_version_code  -- 取传入版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'  -- S&OP预测
	       and t.phase_date >= p_period_begin
	       and t.phase_date < p_period_end
	       and substring(t.period_id,1,4) = nvl(p_year,substr(p_period_end,1,4))
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
                   when t.oversea_flag = 'G' then '全球'
                   else t.oversea_flag
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
      union all
	    select t.version_code    -- 版本编码
           , t.period_id       -- 会计期
           , t.phase_date      -- 会计期次
           , t.bg_code         -- bg编码
           , t.bg_name         -- bg名称
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
                   when t.oversea_flag = 'G' then '全球'
                   else t.oversea_flag
              end)                      as oversea_desc  -- 区域描述
           , t.lv1_prod_rnd_team_code   as lv1_code      -- 重量级团队lv1编码
           , t.lv1_prod_rd_team_cn_name as lv1_name      -- 重量级团队lv1描述
           , t.lv2_prod_rnd_team_code   as lv2_code      -- 重量级团队lv2编码
           , t.lv2_prod_rd_team_cn_name as lv2_name      -- 重量级团队lv2名称
           , t.l1_name   -- l1名称
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
           , t.l3_name   -- l3名称
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end) as l1_coefficient  -- l1系数
           , round(t.l2_coefficient,6) as l2_coefficient  -- l2系数
           , round(t.l3_coefficient,6) as l3_coefficient  -- l3系数
           , 'USD' as currency
           , sum(nvl(t.usd_revenue,0)) as equip_rev_cons_before_amt  -- 收入金额(对价前)
           , sum(nvl(t.usd_cost,0))    as equip_cost_cons_before_amt -- 成本金额(对价前)
           , sum(nvl(t.equip_rev_usd_amt,0))  as equip_rev_cons_after_amt  -- 设备收入金额(对价后)
           , sum(nvl(t.equip_cost_usd_amt,0)) as equip_cost_cons_after_amt -- 设备成本金额(对价后)
           , sum(nvl(t.plan_unit_quantity,0)) as plan_qty -- 发货量（SNOP）
           , sum(nvl(t.ship_qty,0))           as ship_qty -- 发货量（历史）
           , sum(nvl(t.spart_qty,0))          as spart_qty --收入量（历史）
           , articulation_flag
           , t.source_table
	      from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	     where t.articulation_flag in ('SCENO1','SCENO2','SCENO3') -- 勾稽方法标签
	       and upper(t.industry_type) = 'TGT'
	       and t.version_code = v_version_code  -- 取传入版本数据
	       and t.source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'  -- S&OP预测
	       and t.phase_date >= p_period_begin
	       and t.phase_date < p_period_end
	       and substring(t.period_id,1,4) = nvl(p_year,substr(p_period_end,1,4))
	       and t.del_flag = 'N'
       group by t.version_code
           , t.period_id
           , t.phase_date
           , t.bg_code
           , t.bg_name
           , (case when t.oversea_flag = 'Y' then '海外'
                   when t.oversea_flag = 'N' then '国内'
                   when t.oversea_flag = 'G' then '全球'
                   else t.oversea_flag
              end)
           , t.lv1_prod_rnd_team_code
           , t.lv1_prod_rd_team_cn_name
           , t.lv2_prod_rnd_team_code
           , t.lv2_prod_rd_team_cn_name
           , t.l1_name
           , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end)
           , t.l3_name
           , (case when t.l1_coefficient is null then 0 else round(t.l1_coefficient,6) end)
           , round(t.l2_coefficient,6)
           , round(t.l3_coefficient,6)
	    		 , t.articulation_flag
	    		 , t.source_table
	    ;

	    v_dml_row_count := sql%rowcount;  -- 收集数据量

	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '传入版本：'||v_version_code||'，传入开始日期：'||p_period_begin||'，传入结束日期：'||p_period_end||'，非S&OP数据及S&OP预测数据（期次：传入开始日期、传入结束日期，会计期：传入结束日期年）入到 spart_detail_info_tmp 临时表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	  end if;
  end if;

	insert into bg_name_temp(
	       version_code
	     , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l2_coefficient
       , l3_coefficient
       , currency
       , articulation_flag
       , equip_rev_cons_before_amt
       , equip_cost_cons_before_amt
       , equip_rev_cons_after_amt
       , equip_cost_cons_after_amt
       , plan_qty
       , ship_qty
       , spart_qty
  )
	--区域描述打上全球的标签
	--202309版本区域增加'国内'、'海外'
	with oversea_desc_temp as (
	select version_code   -- 版本编码
       , period_id      -- 会计期
       , phase_date
       , bg_code        -- bg编码
       , bg_name        -- bg名称
       , oversea_desc   -- 区域描述
       , lv1_code       -- 重量级团队lv1编码
       , lv1_name       -- 重量级团队lv1描述
       , lv2_code       -- 重量级团队lv2编码
       , lv2_name       -- 重量级团队lv2名称
       , l1_name        -- l1名称
       , l2_name        -- l2名称
       , l3_name        -- l3名称
       , l2_coefficient -- l2系数
       , l3_coefficient -- l3系数
       , currency
       , articulation_flag
       , sum(equip_rev_cons_before_amt)       as equip_rev_cons_before_amt  -- 收入金额(对价前)
       , sum(equip_cost_cons_before_amt)      as equip_cost_cons_before_amt -- 成本金额(对价前)
       , sum(equip_rev_cons_after_amt)        as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
       , sum(equip_cost_cons_after_amt)       as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
       , sum(nvl(l3_coefficient,0)*plan_qty)  as plan_qty                   -- 发货量（SNOP）
       , sum(nvl(l3_coefficient,0)*ship_qty)  as ship_qty                   -- 发货量（历史）
       , sum(nvl(l3_coefficient,0)*spart_qty) as spart_qty                  -- 收入量（历史）
	  from spart_detail_info_tmp
   where oversea_desc is not null    -- 国内、海外需要排除空值
   group by version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l2_coefficient
       , l3_coefficient
       , currency
       , articulation_flag
  union all
  select version_code    -- 版本编码
       , period_id       -- 会计期
       , phase_date
       , bg_code         -- bg编码
       , bg_name         -- bg名称
       , '全球' as oversea_desc  -- 区域描述
       , lv1_code        -- 重量级团队lv1编码
       , lv1_name        -- 重量级团队lv1描述
       , lv2_code        -- 重量级团队lv2编码
       , lv2_name        -- 重量级团队lv2名称
       , l1_name         -- l1名称
       , l2_name         -- l2名称
       , l3_name         -- l3名称
       , l2_coefficient  -- l2系数
       , l3_coefficient  -- l3系数
       , currency
       , articulation_flag
       , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt  -- 收入金额(对价前)
       , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt -- 成本金额(对价前)
       , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
       , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
       , sum(nvl(l3_coefficient,0)*plan_qty ) as plan_qty--	发货量（SNOP）
       , sum(nvl(l3_coefficient,0)*ship_qty ) as ship_qty--	发货量（历史）
       , sum(nvl(l3_coefficient,0)*spart_qty) as spart_qty--收入量（历史）
	  from spart_detail_info_tmp
	 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t')  -- 排除S&OP表的国内、海外、全球数据
   group by version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l2_coefficient
       , l3_coefficient
       , currency
       , articulation_flag
	)
	-- bg名称和bg编码打上集团的标签
	select version_code  -- 版本编码
       , period_id     -- 会计期
       , phase_date
       , 'PROD0002' as bg_code  -- bg编码
       , 'ICT'      as bg_name  -- bg名称
       , oversea_desc   -- 区域描述
       , lv1_code       -- 重量级团队lv1编码
       , lv1_name       -- 重量级团队lv1描述
       , lv2_code       -- 重量级团队lv2编码
       , lv2_name       -- 重量级团队lv2名称
       , l1_name        -- l1名称
       , l2_name        -- l2名称
       , l3_name        -- l3名称
       , l2_coefficient -- l2系数
       , l3_coefficient -- l3系数
       , currency
       , articulation_flag
       , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt  -- 收入金额(对价前)
       , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt -- 成本金额(对价前)
       , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
       , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
       , sum(plan_qty ) as plan_qty   -- 发货量（snop）
       , sum(ship_qty ) as ship_qty   -- 发货量（历史）
       , sum(spart_qty) as spart_qty  -- 收入量（历史）
	  from oversea_desc_temp
  group by version_code
       , period_id
       , phase_date
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l2_coefficient
       , l3_coefficient
       , currency
       , articulation_flag
  union all
  select version_code  -- 版本编码
       , period_id     -- 会计期
       , phase_date
       , bg_code       -- bg编码
       , bg_name       -- bg名称
       , oversea_desc  -- 区域描述
       , lv1_code      -- 重量级团队lv1编码
       , lv1_name      -- 重量级团队lv1描述
       , lv2_code      -- 重量级团队lv2编码
       , lv2_name      -- 重量级团队lv2名称
       , l1_name         -- l1名称
       , l2_name         -- l2名称
       , l3_name         -- l3名称
       , l2_coefficient  -- l2系数
       , l3_coefficient  -- l3系数
       , currency
       , articulation_flag
       , equip_rev_cons_before_amt  -- 收入金额(对价前)
       , equip_cost_cons_before_amt -- 成本金额(对价前)
       , equip_rev_cons_after_amt   -- 设备收入金额(对价后)
       , equip_cost_cons_after_amt  -- 设备成本金额(对价后)
       , plan_qty    -- 发货量（snop）
       , ship_qty    -- 发货量（历史）
       , spart_qty   -- 收入量（历史）
	  from oversea_desc_temp
  ;
  
  v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'bg_name_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 按照l3层级收敛
  insert into l3_temp(
	       version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l3_coefficient
       , currency
       , articulation_flag
       , equip_rev_cons_before_amt
       , equip_cost_cons_before_amt
       , equip_rev_cons_after_amt
       , equip_cost_cons_after_amt
       , plan_qty
       , ship_qty
       , spart_qty
  )
	select version_code  -- 版本编码
       , period_id     -- 会计期
       , phase_date
       , bg_code       -- bg编码
       , bg_name       -- bg名称
       , oversea_desc  -- 区域描述
       , lv1_code      -- 重量级团队lv1编码
       , lv1_name      -- 重量级团队lv1描述
       , lv2_code      -- 重量级团队lv2编码
       , lv2_name      -- 重量级团队lv2名称
       , l1_name       -- l1名称
       , l2_name       -- l2名称
       , l3_name       -- l3名称
       , l3_coefficient -- l3系数
       , currency
       , articulation_flag
       , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt  -- 收入金额(对价前)
       , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt -- 成本金额(对价前)
       , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt   -- 设备收入金额(对价后)
       , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt  -- 设备成本金额(对价后)
       , sum(plan_qty)  as plan_qty   -- 发货量（snop）
       , sum(ship_qty)  as ship_qty   -- 发货量（历史）
       , sum(spart_qty) as spart_qty  -- 收入量（历史）
	  from bg_name_temp
   group by version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l3_coefficient
       , currency
       , articulation_flag
	;

	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'l3_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	-- 按照l2层级收敛
	-- 场景1对应的L1系数有2种：0或0.33333，其中0.33333的是射频模块的标识
	insert into l2_temp(
	       version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv2_code
       , l1_name
       , l2_name
       , currency
       , articulation_flag
       , equip_rev_cons_before_amt
       , plan_qty
       , ship_qty
       , spart_qty
	)
	select version_code   -- 版本编码
       , period_id      -- 会计期
       , phase_date
       , bg_code        -- bg编码
       , bg_name        -- bg名称
       , oversea_desc   -- 区域描述
       , lv1_code       -- 重量级团队lv1编码
       , lv2_code       -- 重量级团队lv2编码
       , l1_name        -- l1名称
       , l2_name        -- l2名称
       , currency       -- 币种
       , articulation_flag
       , sum(nvl(t1.equip_rev_cons_before_amt,0)) as equip_rev_cons_before_amt  -- l1对价前收入金额
       , sum(case when articulation_flag = 'SCENO1' then t1.plan_qty/3  -- S&OP的系数是从计委包取的，而计委包没有L1系数，计委包场景1的L1系数默认1/3
         	        else null
          	 end) as plan_qty    -- 发货量（snop）
       , sum(case when articulation_flag = 'SCENO1' then nvl(t1.l2_coefficient,0)*t1.ship_qty
                  else null
             end) as ship_qty    -- 发货量（历史）
       , sum(case when articulation_flag = 'SCENO1' then nvl(t1.l2_coefficient,0)*t1.spart_qty
                  else null
             end) as spart_qty   -- 收入量（历史）
	  from bg_name_temp t1
   group by version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv2_code
       , l1_name
       , l2_name
       , currency
       , articulation_flag
	;
	
	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => 'l2层级收敛临时表 l2_temp 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  
  -- 计算单位成本、单位价格
	insert into all_temp(
         version_code  -- 版本编码
       , period_id	   -- 会计期
       , phase_date
       , bg_code	     -- bg编码
       , bg_name	     -- bg名称
       , oversea_desc	 -- 区域
       , lv1_code	     -- 重量级团队lv1编码
       , lv1_name	     -- 重量级团队lv1描述
       , lv2_code	     -- 重量级团队lv2编码
       , lv2_name	     -- 重量级团队lv2名称
       , l1_name	     -- l1名称
       , l2_name	     -- l2名称
       , l3_name	     -- l3名称
       , l3_coefficient -- l3系数
       , currency	     -- 币种
       , equip_rev_cons_before_amt	  -- 设备收入额(对价前)
       , equip_cost_cons_before_amt	  -- 设备成本额(对价前)
       , plan_qty	           -- 发货量（snop）
       , ship_qty	           -- 发货量（历史）
       , spart_qty	         -- 收入量（历史）
       , unit_cost	         -- 单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)
       , unit_price	         -- 单位价格 = l2对价前收入金额/l2收入数量
       , rev_percent	       -- 收入占比 = l2对价前收入金额/l1对价前收入金额
       , mgp_ratio	         -- 制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
       , articulation_flag   -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
       , del_ind
  )
	--计算收入占比、制毛率
	with all_temp_01 as(
	select t1.version_code               -- 版本编码
       , t1.period_id		               -- 会计期
       , t1.phase_date
       , t1.bg_code			               -- bg编码
       , t1.bg_name			               -- bg名称
       , t1.oversea_desc               -- 区域
       , t1.lv1_code			             -- 重量级团队lv1编码
       , t1.lv1_name			             -- 重量级团队lv1描述
       , t1.lv2_code			             -- 重量级团队lv2编码
       , t1.lv2_name			             -- 重量级团队lv2名称
       , t2.l1_name			               -- l1名称
       , t1.l2_name			               -- l2名称
       , t1.l3_name			               -- l3名称
       , t1.l3_coefficient             -- l3系数
       , t1.currency			             -- 币种
       , t1.equip_rev_cons_before_amt  -- 设备收入额(对价前)
       , t1.equip_cost_cons_before_amt -- 设备成本额(对价前)
       , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in('软件','其他') then (nvl(t2.plan_qty,0)*3) else t1.plan_qty end)   as plan_qty		-- 发货量（snop）
       , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in('软件','其他') then (nvl(t2.ship_qty,0)*3) else t1.ship_qty end)   as ship_qty 	-- 发货量（历史）
       , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in('软件','其他') then (nvl(t2.spart_qty,0)*3) else t1.spart_qty end) as spart_qty 	-- 收入量（历史）
       , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				       when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
				       else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt
				  end) as rev_percent	   -- 收入占比 = l3对价前收入金额/l2对价前收入金额
			 , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 0
				       when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
				       else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt
				  end) as mgp_ratio	     -- 制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
			 , t1.articulation_flag    -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			 , (case when t1.phase_date is not null and t1.plan_qty = 0 then 'D' else '' end) as del_ind   -- phase_date有值且plan_qty为0的是要剔除的
	  from l3_temp t1
    left join l2_temp t2
	    on t1.version_code = t2.version_code
	   and t1.period_id = t2.period_id--会计期
	   and t1.bg_code = t2.bg_code--bg编码
	   and t1.oversea_desc = t2.oversea_desc--区域描述
	   and t1.lv1_code = t2.lv1_code--	重量级团队lv1编码
	   and t1.lv2_code = t2.lv2_code--	重量级团队lv2编码
	   and t1.currency = t2.currency--币种
	   and t1.l1_name = t2.l1_name
	   and t1.l2_name = t2.l2_name
	   and t1.articulation_flag = t2.articulation_flag
	   and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
	 where t1.l2_name is not null or t1.l2_name <> ''
	)
	select t1.version_code     -- 版本编码
       , t1.period_id		     -- 会计期
       , t1.phase_date
       , t1.bg_code			     -- bg编码
       , t1.bg_name			     -- bg名称
       , t1.oversea_desc     -- 区域
       , t1.lv1_code			   -- 重量级团队lv1编码
       , t1.lv1_name			   -- 重量级团队lv1描述
       , t1.lv2_code			   -- 重量级团队lv2编码
       , t1.lv2_name			   -- 重量级团队lv2名称
       , t1.l1_name			     -- l1名称
       , t1.l2_name			     -- l2名称
       , t1.l3_name			     -- l3名称
       , t1.l3_coefficient   -- l3系数
       , t1.currency			   -- 币种
       , t1.equip_rev_cons_before_amt	 -- 设备收入额(对价前)
       , t1.equip_cost_cons_before_amt -- 设备成本额(对价前)
       , t1.plan_qty		               -- 发货量（snop）
       , t1.ship_qty 	                 -- 发货量（历史）
       , t1.spart_qty 	               -- 收入量（历史）
       , (case when nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				       when nvl(t1.spart_qty,0) = 0 then -999999
				       else t1.equip_cost_cons_before_amt / t1.spart_qty
				  end) as unit_cost	    -- 单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)
			 , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				       when nvl(t1.spart_qty,0) = 0 then -999999
				       else t1.equip_rev_cons_before_amt / t1.spart_qty
				  end) as unit_price	  -- 单位价格 = l2对价前收入金额/l2收入数量
			 , t1.rev_percent	        -- 收入占比 = l2对价前收入金额/l1对价前收入金额
			 , t1.mgp_ratio	          -- 制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
			 , t1.articulation_flag   -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			 , del_ind
  from all_temp_01 t1
	;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => 'all_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  
  -- 需要造的数据入到临时表
  insert into l2_all_phase_info_tmp(
	      version_code
	    , period_id                    -- 会计期
	 		, phase_date                   -- 会计期次
	 		, bg_code                      -- BG编码
	 		, bg_name                      -- BG名称
	 		, oversea_desc                 -- 海外标志
	 		, lv1_code                     -- 重量级团队lv1编码
	 		, lv1_name                     -- 重量级团队lv1名称
	 		, lv2_code                     -- 重量级团队lv2编码
	 		, lv2_name                     -- 重量级团队lv2名称
	 		, l1_name                      -- l1名称
	 		, l2_name                      -- l2名称
	 		, l3_name                      -- l3名称
	 		, l3_coefficient               -- l3系数
	 		, currency                     -- 币种
	 		, articulation_flag            -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	 )
	-- L2期次造数逻辑（知识表示需要补全所有 l2_name 的期次数据，金额、数量，赋值0；系数给空值）
	-- 取期次对应会计期的全量数据
	with phase_info_tmp as(
  select distinct version_code, period_id, phase_date
    from all_temp
	 where nvl(del_ind,'SNULL') <> 'D'   -- phase_date有值且plan_qty为0的是要剔除的
		 and (nvl(equip_rev_cons_before_amt,0) <> 0
		      or nvl(equip_cost_cons_before_amt,0) <> 0
		      or nvl(plan_qty,0) <> 0
		      or nvl(spart_qty,0) <> 0
		      or nvl(ship_qty,0) <> 0
		     )
  ),
  -- 已经存在的期次对应会计期数据
  l2_info_tmp as(
  select distinct version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l3_coefficient
       , currency
       , articulation_flag
    from all_temp
	 where nvl(del_ind,'SNULL') <> 'D'   -- phase_date有值且plan_qty为0的是要剔除的
		 and (nvl(equip_rev_cons_before_amt,0) <> 0
		      or nvl(equip_cost_cons_before_amt,0) <> 0
		      or nvl(plan_qty,0) <> 0
		      or nvl(spart_qty,0) <> 0
		      or nvl(ship_qty,0) <> 0
		     )
  ),
  other_info_tmp as(
  select distinct version_code
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l3_coefficient
       , currency
       , articulation_flag
    from all_temp
	 where nvl(del_ind,'SNULL') <> 'D'   -- phase_date有值且plan_qty为0的是要剔除的
		 and (nvl(equip_rev_cons_before_amt,0) <> 0
		      or nvl(equip_cost_cons_before_amt,0) <> 0
		      or nvl(plan_qty,0) <> 0
		      or nvl(spart_qty,0) <> 0
		      or nvl(ship_qty,0) <> 0
		     )
  ),
  l2_all_info_tmp as(
  select t1.version_code
       , t1.period_id
       , t1.phase_date
       , t3.bg_code
       , t3.bg_name
       , t3.oversea_desc
       , t3.lv1_code
       , t3.lv1_name
       , t3.lv2_code
       , t3.lv2_name
       , t3.l1_name
       , t3.l2_name
       , t3.l3_name
       , t3.l3_coefficient
       , t3.currency
       , t3.articulation_flag
    from phase_info_tmp t1
    left join other_info_tmp t3
      on 1=1
  )
  select t5.version_code
       , t5.period_id
       , t5.phase_date
       , t5.bg_code
       , t5.bg_name
       , t5.oversea_desc
       , t5.lv1_code
       , t5.lv1_name
       , t5.lv2_code
       , t5.lv2_name
       , t5.l1_name
       , t5.l2_name
       , t5.l3_name
	 		 , t5.l3_coefficient
       , t5.currency
       , t5.articulation_flag
    from l2_all_info_tmp t5
    left join l2_info_tmp t2
      on t5.version_code       = t2.version_code
     and t5.period_id          = t2.period_id
     and nvl(t5.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
     and t5.bg_code            = t2.bg_code
     and t5.bg_name            = t2.bg_name
     and t5.oversea_desc       = t2.oversea_desc
     and t5.lv1_code           = t2.lv1_code
     and t5.lv1_name           = t2.lv1_name
     and t5.lv2_code           = t2.lv2_code
     and t5.lv2_name           = t2.lv2_name
     and t5.l1_name            = t2.l1_name
     and t5.l2_name            = t2.l2_name
     and t5.l3_name            = t2.l3_name
     and t5.l3_coefficient     = t2.l3_coefficient
     and t5.currency           = t2.currency
     and t5.articulation_flag  = t2.articulation_flag
   where t2.period_id is null
     and t5.phase_date is not null   -- 只取期次不为空的
  ;

  v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => 'l2_all_phase_info_tmp 临时表（需要造的数据）的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


  insert into fin_dm_opt_fop.dm_fop_spart_l3_info_his_t (
			   version_code                -- 版本编码
       , period_id                   -- 会计期
       , phase_date
       , bg_code                     -- bg编码
       , bg_name                     -- bg名称
       , oversea_desc                -- 区域描述
       , lv1_code                    -- lv1编码
       , lv1_name                    -- lv1名称
       , lv2_code                    -- lv2编码
       , lv2_name                    -- lv2名称
       , l1_name                     -- l1名称
       , l2_name                     -- l2名称
       , l3_name                     -- l3名称
       , l3_coefficient              -- l3系数
       , currency                    -- 币种
       , equip_rev_cons_before_amt   -- 设备收入金额（对价前）
       , equip_cost_cons_before_amt  -- 设备成本金额（对价前）
       , plan_qty
       , spart_qty
       , ship_qty
       , unit_cost                   -- 单位成本
       , unit_price                  -- 单位价格
       , rev_percent                 -- 收入占比l
       , mgp_ratio
       , articulation_flag
       , remark
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
  )
	with spart_l2_info_tmp as(
	select version_code               -- 版本编码
       , period_id                  -- 会计期
       , phase_date
       , bg_code                    -- bg编码
       , bg_name                    -- bg名称
       , oversea_desc               -- 区域描述
       , lv1_code                   -- lv1编码
       , lv1_name                   -- lv1名称
       , lv2_code                   -- lv2编码
       , lv2_name                   -- lv2名称
       , l1_name                    -- l1名称
       , l2_name                    -- l2名称
       , l3_name                    -- l3名称
       , l3_coefficient             -- l3系数
       , currency                   -- 币种
       , equip_rev_cons_before_amt  -- 设备收入金额（对价前）
       , equip_cost_cons_before_amt -- 设备成本金额（对价前）
       , plan_qty
       , spart_qty
       , ship_qty
       , unit_cost                  -- 单位成本
       , unit_price                 -- 单位价格
       , rev_percent                -- 收入占比
       , mgp_ratio
       , articulation_flag
	  from all_temp
	 where nvl(del_ind,'SNULL') <> 'D'   -- phase_date有值且plan_qty为0的是要剔除的
		 and (nvl(equip_rev_cons_before_amt,0) <> 0
			    or nvl(equip_cost_cons_before_amt,0) <> 0
			    or nvl(plan_qty,0) <> 0
			    or nvl(spart_qty,0) <> 0
			    or nvl(ship_qty,0) <> 0
		     )
	union all
	-- l2_name缺失期次的数据入到目标表
  select t1.version_code
       , t1.period_id                     -- 会计期
       , t1.phase_date
       , t1.bg_code                       -- bg编码
       , t1.bg_name                       -- bg名称
       , t1.oversea_desc                  -- 区域描述
       , t1.lv1_code                      -- lv1编码
       , t1.lv1_name                      -- lv1名称
       , t1.lv2_code                      -- lv2编码
       , t1.lv2_name                      -- lv2名称
       , t1.l1_name                       -- l1名称
       , t1.l2_name                       -- l2名称
       , t1.l3_name                       -- l3名称
       , t1.l3_coefficient                -- l3系数
       , t1.currency                      -- 币种
       , 0 as equip_rev_cons_before_amt   -- 设备收入金额（对价前）
       , 0 as equip_cost_cons_before_amt  -- 设备成本金额（对价前）
       , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in('软件','其他') then (nvl(t2.plan_qty,0)*3) else 0 end) as plan_qty		--发货量（snop）
       , 0 as spart_qty
       , 0 as ship_qty
       , null as unit_cost                -- 单位成本
       , null as unit_price               -- 单位价格
       , null as rev_percent              -- 收入占比
       , 0 as mgp_ratio
       , t1.articulation_flag
    from l2_all_phase_info_tmp t1
    left join l2_temp t2
	    on t1.period_id = t2.period_id--会计期
	   and t1.bg_code = t2.bg_code--bg编码
	   and t1.oversea_desc = t2.oversea_desc--区域描述
	   and t1.lv1_code = t2.lv1_code--	重量级团队lv1编码
	   and t1.lv2_code = t2.lv2_code--	重量级团队lv2编码
	   and t1.currency = t2.currency--币种
	   and t1.l1_name = t2.l1_name
	   and t1.l2_name = t2.l2_name
	   and t1.articulation_flag = t2.articulation_flag
	   and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
	)
	select version_code
       , period_id         -- 会计期
       , phase_date
       , bg_code           -- bg编码
       , bg_name           -- bg名称
       , oversea_desc      -- 区域描述
       , lv1_code          -- lv1编码
       , lv1_name          -- lv1名称
       , lv2_code          -- lv2编码
       , lv2_name          -- lv2名称
       , l1_name           -- l1名称
       , l2_name           -- l2名称
       , l3_name           -- l3名称
       , l3_coefficient    -- l3系数
       , currency          -- 币种
       , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt   -- 设备收入金额（对价前）
       , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt  -- 设备成本金额（对价前）
       , sum(plan_qty)    as plan_qty
       , sum(spart_qty)   as spart_qty
       , sum(ship_qty)    as ship_qty
       , sum(unit_cost)   as unit_cost     -- 单位成本
       , sum(unit_price)  as unit_price    -- 单位价格
       , sum(rev_percent) as rev_percent   -- 收入占比
       , sum(mgp_ratio)   as mgp_ratio
       , articulation_flag
       , '' as remark
       , -1 as created_by
       , current_timestamp as creation_date
       , -1 as last_updated_by
       , current_timestamp as last_update_date
       , 'N' as del_flag
	  from spart_l2_info_tmp
	 group by version_code
       , period_id
       , phase_date
       , bg_code
       , bg_name
       , oversea_desc
       , lv1_code
       , lv1_name
       , lv2_code
       , lv2_name
       , l1_name
       , l2_name
       , l3_name
       , l3_coefficient
       , currency
			 , articulation_flag
	;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc => 'dm_fop_spart_l3_info_his_t 作业对象L3层级历史表数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


	-- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_l3_info_t;

	-- 数据入到接口表
	if(p_version_code is null or p_version_code = '') then
	  insert into fin_dm_opt_fop.dm_fop_spart_l3_info_t(
           period_id                    -- 会计期
         , phase_date
         , bg_code                      -- bg编码
         , bg_name                      -- bg名称
         , oversea_desc                 -- 区域描述
         , lv1_code                     -- lv1编码
         , lv1_name                     -- lv1名称
         , lv2_code                     -- lv2编码
         , lv2_name                     -- lv2名称
         , l1_name                      -- l1名称
         , l2_name                      -- l2名称
         , l3_name                      -- l3名称
         , l3_coefficient               -- l3系数
         , currency                     -- 币种
         , equip_rev_cons_before_amt    -- 设备收入金额（对价前）
         , equip_cost_cons_before_amt   -- 设备成本金额（对价前）
         , plan_qty
         , spart_qty
         , ship_qty
         , unit_cost                    -- 单位成本
         , unit_price                   -- 单位价格
         , rev_percent                  -- 收入占比
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
		)
	  select period_id                    -- 会计期
         , phase_date
         , bg_code                      -- bg编码
         , bg_name                      -- bg名称
         , oversea_desc                 -- 区域描述
         , lv1_code                     -- lv1编码
         , lv1_name                     -- lv1名称
         , lv2_code                     -- lv2编码
         , lv2_name                     -- lv2名称
         , l1_name                      -- l1名称
         , l2_name                      -- l2名称
         , l3_name                      -- l3名称
         , l3_coefficient               -- l3系数
         , currency                     -- 币种
         , equip_rev_cons_before_amt    -- 设备收入金额（对价前）
         , equip_cost_cons_before_amt   -- 设备成本金额（对价前）
         , plan_qty
         , spart_qty
         , ship_qty
         , unit_cost                    -- 单位成本
         , unit_price                   -- 单位价格
         , rev_percent                  -- 收入占比
         , mgp_ratio
         , articulation_flag
			   , '' as remark
	  	   , -1 as created_by
	  	   , current_timestamp as creation_date
	  	   , -1 as last_updated_by
	  	   , current_timestamp as last_update_date
	  	   , 'N' as del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l3_info_his_t
		 where version_code = v_max_version_code  -- 取最大版本数据
		;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 10,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，入到 dm_fop_spart_l3_info_t 表的数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  else
    insert into fin_dm_opt_fop.dm_fop_spart_l3_info_t(
           period_id                    -- 会计期
         , phase_date
         , bg_code                      -- bg编码
         , bg_name                      -- bg名称
         , oversea_desc                 -- 区域描述
         , lv1_code                     -- lv1编码
         , lv1_name                     -- lv1名称
         , lv2_code                     -- lv2编码
         , lv2_name                     -- lv2名称
         , l1_name                      -- l1名称
         , l2_name                      -- l2名称
         , l3_name                      -- l3名称
         , l3_coefficient               -- l3系数
         , currency                     -- 币种
         , equip_rev_cons_before_amt    -- 设备收入金额（对价前）
         , equip_cost_cons_before_amt   -- 设备成本金额（对价前）
         , plan_qty
         , spart_qty
         , ship_qty
         , unit_cost                    -- 单位成本
         , unit_price                   -- 单位价格
         , rev_percent                  -- 收入占比
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
		)
		select period_id                    -- 会计期
         , phase_date
         , bg_code                      -- bg编码
         , bg_name                      -- bg名称
         , oversea_desc                 -- 区域描述
         , lv1_code                     -- lv1编码
         , lv1_name                     -- lv1名称
         , lv2_code                     -- lv2编码
         , lv2_name                     -- lv2名称
         , l1_name                      -- l1名称
         , l2_name                      -- l2名称
         , l3_name                      -- l3名称
         , l3_coefficient               -- l3系数
         , currency                     -- 币种
         , equip_rev_cons_before_amt    -- 设备收入金额（对价前）
         , equip_cost_cons_before_amt   -- 设备成本金额（对价前）
         , plan_qty
         , spart_qty
         , ship_qty
         , unit_cost                    -- 单位成本
         , unit_price                   -- 单位价格
         , rev_percent                  -- 收入占比
         , mgp_ratio
         , articulation_flag
			   , '' as remark
	  	   , -1 as created_by
	  	   , current_timestamp as creation_date
	  	   , -1 as last_updated_by
	  	   , current_timestamp as last_update_date
	  	   , 'N' as del_flag
		  from fin_dm_opt_fop.dm_fop_spart_l3_info_his_t
		 where version_code = p_version_code  -- 取最大版本数据
		;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 10,
        p_log_cal_log_desc => '传入版本：'||p_version_code||'，入到 dm_fop_spart_l3_info_t 表的数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  end if;

  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l3_info_his_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l3_info_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

