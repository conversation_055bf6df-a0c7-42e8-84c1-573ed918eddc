CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_SOP_PLAN_SUM_T(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING, OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲SOP计划量汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
		  参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_SOP_PLAN_SUM_T();
*/
 
 DECLARE
	V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_SOP_PLAN_SUM_T('''||P_VERSION_CODE||')';
	V_TBL_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PLAN_SUM_T';
	V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月_001
	V_VERSION_MONTH VARCHAR(50);  -- 目标表的当前版本年月，格式：年月
	V_STEP_NUM NUMERIC := 0; --步骤号
    V_DML_ROW_COUNT NUMBER DEFAULT 0 ;

BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';                                 --1表示成功
	
	
	        -- 如果是传 VERSION_CODE 调函数取 传入的 P_VERSION_CODE ，如果是自动调度的 则从版本表取最新版本号
	 IF P_VERSION_CODE IS NOT NULL 
	 THEN
	 SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
	 ELSE 
	   -- 从版本表取最新版本号	    
	 SELECT VERSION_CODE INTO V_VERSION_CODE
	 FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
	 WHERE STEP = 1
	 ORDER BY LAST_UPDATE_DATE DESC
	 LIMIT 1
	 ;
	 END IF 
	 ;
		
			-- 当前版本所在的年月
		SELECT SUBSTR(V_VERSION_CODE,1,6)  INTO V_VERSION_MONTH ;
		
	 --1.开始日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '盈利量纲SOP计划量汇总表'||V_TBL_NAME||'，版本编码:'||V_VERSION_CODE||'，版本年月:'||V_VERSION_MONTH||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
  
  

		
		DROP TABLE IF EXISTS PRODUCT_TMP;
	     CREATE TEMPORARY TABLE  PRODUCT_TMP
		          AS
		SELECT DISTINCT 
              LV1_PROD_RND_TEAM_CODE
              ,LV1_PROD_RD_TEAM_CN_NAME
              ,LV1_PROD_RD_TEAM_EN_NAME
              ,LV2_PROD_RND_TEAM_CODE
              ,LV2_PROD_RD_TEAM_CN_NAME
              ,LV2_PROD_RD_TEAM_EN_NAME
		FROM  DMDIM.DM_DIM_PRODUCT_D
		WHERE LV1_PROD_RND_TEAM_CODE IN ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网
										  ;
										  
	     V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '产品维表：PRODUCT_TMP 目标维度的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

		
		-- SOP计划量数据表关联产品维表和量纲维表，获取重量级团队CODE和量纲分组
		 DROP TABLE IF EXISTS SOP_PLAN_TMP;
	     CREATE TEMPORARY TABLE  SOP_PLAN_TMP
		          AS
	     SELECT CAST(T1.MONTH AS NUMERIC)  AS PERIOD_ID
		       ,REPLACE(T1.PERIOD,'/','') AS PHASE_DATE                                                                                      
               ,T1.PERIOD_FLAG                                                                                      
               ,CASE WHEN T1.BG_CN  = '运营商' THEN 'PDCG901159'                                                          
                     WHEN T1.BG_CN  = '政企'   THEN 'PDCG901160'		                                                    
                END AS BG_CODE				                                                                        
               ,T1.BG_CN AS BG_NAME                                                                                                                                                                                       
               ,T1.MEASURE_CODE                                                                                     
               ,T2.LV1_PROD_RND_TEAM_CODE			                                                                
               ,T1.LV1_ORG_CN  AS LV1_PROD_RD_TEAM_CN_NAME                                                           
               ,T2.LV2_PROD_RND_TEAM_CODE			                                                                
               ,T1.LV2_ORG_CN  AS LV2_PROD_RD_TEAM_CN_NAME 
               ,T1.CN_DIMENSION_GROUP_L2_CODE
               ,T1.CN_DIMENSION_GROUP_L2  
               ,T3.PRODUCT_DIMENSION_GROUP_CODE    AS DIMENSION_GROUP_CODE
               ,T3.PRODUCT_DIMENSION_GROUP         AS DIMENSION_GROUP_CN_NAME
               ,T3.PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME                                             
               ,T1.COUNTRY_MEASURE   AS OVERSEA_DESC                                                                               
               ,T1.DIMENSION_SUBCATEGORY_CODE                                                                       
               ,T1.DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,T1.DIMENSION_SUBCATEGORY_EN_NAME  
			  -- ,T1.DEMISION_FLAG
               ,SUM(T1.QTY) AS SNOP_QUANTITY                                                                        
           FROM  FIN_DM_OPT_FOP.DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I T1                                                
		   JOIN  PRODUCT_TMP T2                                                                                
		    ON T1.LV1_ORG_CN = 	T2.LV1_PROD_RD_TEAM_CN_NAME                                                                                 
		   AND T1.LV2_ORG_CN = 	T2.LV2_PROD_RD_TEAM_CN_NAME                                                                            
		   LEFT JOIN FIN_DM_OPT_FOP.DWR_DIM_PRODUCTDIMENSION_D T3                                                                            
		   ON T1.DIMENSION_KEY = T3.DIMENSION_KEY                                                                                                                                       
           WHERE 1=1   
          AND CASE WHEN REPLACE(T1.PERIOD,'/','') LIKE'%-%' 
		            THEN SUBSTRING(T1.PERIOD,POSITION('-' IN T1.PERIOD)+1) >= TO_CHAR(TO_DATE(V_VERSION_MONTH,'YYYYMMDD') - INTERVAL'1MONTH','YYYYMM')			            
					ELSE REPLACE(T1.PERIOD,'/','')  >= TO_CHAR(ADD_MONTHS(TO_DATE(V_VERSION_MONTH,'YYYYMMDD'),-1),'YYYYMM')||'06'   --上个月6号
	                 AND REPLACE(T1.PERIOD,'/','')  < TO_CHAR(TO_DATE(V_VERSION_MONTH,'YYYYMMDD'),'YYYYMM')||'06'     --这个月6号	 
                    END 				
		   AND T1.BG_CN IN ('运营商','政企')                                                                             
		   AND T1.MEASURE_CODE IN ( '滚动S&OP计划','产品线年度规划')                                                     
		   AND CASE WHEN T1.MEASURE_CODE = '滚动S&OP计划'                                                                
		            THEN T1.PERIOD_FLAG = '月调整期次'                                                                              
			   ELSE T1.PERIOD_FLAG = 'N'                                                                                              
			    END                                                                                                                     
		    GROUP BY T1.PERIOD                                                                                           
               ,T1.PERIOD_FLAG                                                                                      
               ,CASE WHEN T1.BG_CN  = '运营商' THEN 'PDCG901159'                                                          
                     WHEN T1.BG_CN  = '政企'   THEN 'PDCG901160'		                                                    
                END  			                                                                        
               ,T1.BG_CN                                                                                            
               ,T1.MONTH                                                                                            
               ,T1.MEASURE_CODE                                                                                     
               ,T2.LV1_PROD_RND_TEAM_CODE			                                                                
               ,T1.LV1_ORG_CN                                                  
               ,T2.LV2_PROD_RND_TEAM_CODE			                                                                
               ,T1.LV2_ORG_CN                                                                                                                                                                                                                                                                                                                             
               ,T1.CN_DIMENSION_GROUP_L2_CODE
               ,T1.CN_DIMENSION_GROUP_L2  
               ,T3.PRODUCT_DIMENSION_GROUP_CODE
               ,T3.PRODUCT_DIMENSION_GROUP 
               ,T3.PRODUCT_DIMENSION_GROUP_EN_NAME                           
               ,T1.COUNTRY_MEASURE                                                                                  
               ,T1.DIMENSION_SUBCATEGORY_CODE                                                                       
               ,T1.DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,T1.DIMENSION_SUBCATEGORY_EN_NAME  
			   --,T1.DEMISION_FLAG              
               ;	
			   
			    V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => 'SOP来源表逻辑处理：SOP_PLAN_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;


   -- 分国内海外分政企的数据（中国区CNBG   是量纲分组L2 ，其余的用量纲分组）
		 DROP TABLE IF EXISTS CNBG_TMP;
	     CREATE TEMPORARY TABLE  CNBG_TMP
		          AS
         SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME  
               ,OVERSEA_DESC			   
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME     
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN T1.CN_DIMENSION_GROUP_L2_CODE    
	                 ELSE T1.DIMENSION_GROUP_CODE
                     END AS DIMENSION_GROUP_CODE                                          
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN T1.CN_DIMENSION_GROUP_L2           
	                 ELSE T1.DIMENSION_GROUP_CN_NAME         
		              END AS DIMENSION_GROUP_CN_NAME                                       
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN NULL          
	                 ELSE T1.DIMENSION_GROUP_EN_NAME 
		             END AS DIMENSION_GROUP_EN_NAME       			                                                                                                                                      
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SUM(SNOP_QUANTITY) AS  SNOP_QUANTITY     			   
           FROM  SOP_PLAN_TMP T1  	
           GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME  
               ,OVERSEA_DESC			   
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME     
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN T1.CN_DIMENSION_GROUP_L2_CODE    
	                 ELSE T1.DIMENSION_GROUP_CODE
                     END                                        
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN T1.CN_DIMENSION_GROUP_L2           
	                 ELSE T1.DIMENSION_GROUP_CN_NAME         
		              END                                        
               ,CASE WHEN T1.BG_NAME = '运营商' AND OVERSEA_DESC = '国内' 
                     THEN NULL          
	                 ELSE T1.DIMENSION_GROUP_EN_NAME 
		             END      			                                                                                                                                      
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  		   
               ;	
			   
			    V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '分国内海外分政企的数据：CNBG_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
			    			   
			    -- 将国内和海外数据合并，获得全球的数据 (全球的量纲分组都是用量纲分组，不需要中国区L2量纲分组) 
		 DROP TABLE IF EXISTS OVERSEA_DESC_TMP;
	     CREATE TEMPORARY TABLE  OVERSEA_DESC_TMP
		          AS
			    SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME 
               ,DIMENSION_GROUP_EN_NAME
               ,'全球' AS OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                     
           FROM  SOP_PLAN_TMP  
           GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME                                                                                                                             
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
			   UNION ALL
			   -- 区分国内海外（量纲分组，不需要中国区L2量纲分组，用于后面计算不分BG的数据）
                SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME                                             
               ,OVERSEA_DESC                                                                               
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                     
           FROM  SOP_PLAN_TMP  
           GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME                                              
               ,OVERSEA_DESC                                                                               
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
		          ;
				  
	 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '国内海外汇总到全球的数据：OVERSEA_DESC_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
                
                -- 	将 运营商和政企 合成ICT的数据(ICT分BG、ICT不分BG)
  				DROP TABLE IF EXISTS BG_TMP;
	            CREATE TEMPORARY TABLE  BG_TMP
		          AS
	            SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,'PROD0002' AS BG_CODE				                                                                        
               ,'ICT' AS BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME                                               
               ,OVERSEA_DESC                                                                               
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                     
           FROM  OVERSEA_DESC_TMP  
           GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                                                                                                                                                                                                            
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME                                               
               ,OVERSEA_DESC                                                                               
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
		          ;
				  
		V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '运营商政企汇总到ICT 的数据：BG_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;

         -- 将四种数据（分国内海外分BG,不区分国内海外分BG，分国内海外不分BG，不区分国内海外不分BG）
				DROP TABLE IF EXISTS ALL_TMP;
	            CREATE TEMPORARY TABLE  ALL_TMP
		          AS
		    SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SNOP_QUANTITY                                                                                     
           FROM  CNBG_TMP  
		   UNION ALL
		   SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SNOP_QUANTITY                                                                                     
           FROM  OVERSEA_DESC_TMP
           WHERE OVERSEA_DESC = '全球' 
             UNION ALL 		   
			SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SNOP_QUANTITY                                                                                     
           FROM  BG_TMP	  
		   ;
		   
		   V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '四种数据汇到一张表：ALL_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		   
		   -- 打 场景标签： 量纲分组、量纲子类 
		   DROP TABLE IF EXISTS SCENARIOS_TMP;
	       CREATE TEMPORARY TABLE  SCENARIOS_TMP
		     AS
			 SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
			   ,'量纲分组' AS SCENARIOS
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC
               ,NULL AS DIMENSION_SUBCATEGORY_CODE                                                                       
               ,NULL AS DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,NULL AS DIMENSION_SUBCATEGORY_EN_NAME  			   
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                     
           FROM  ALL_TMP	  
		    WHERE LV1_PROD_RND_TEAM_CODE IN ('134557','133277','137565')	   /*光、计算、数据通信*/
			GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,DIMENSION_GROUP_CODE
               ,DIMENSION_GROUP_CN_NAME
               ,DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC			   
			UNION ALL
			 SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
			   ,'量纲子类' AS SCENARIOS
               ,NULL AS DIMENSION_GROUP_CODE
               ,NULL AS DIMENSION_GROUP_CN_NAME
               ,NULL AS DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                      
           FROM  ALL_TMP	  
		    WHERE LV1_PROD_RND_TEAM_CODE IN ('100001','101775')	   /*无线、数据存储*/
			GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,OVERSEA_DESC			   
               ,DIMENSION_SUBCATEGORY_CODE                                                                       
               ,DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,DIMENSION_SUBCATEGORY_EN_NAME  
			UNION ALL
			SELECT PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
			   ,'LV2' AS SCENARIOS
			   ,NULL AS DIMENSION_GROUP_CODE
               ,NULL AS DIMENSION_GROUP_CN_NAME
               ,NULL AS DIMENSION_GROUP_EN_NAME
               ,OVERSEA_DESC		
               ,NULL AS DIMENSION_SUBCATEGORY_CODE                                                                       
               ,NULL AS DIMENSION_SUBCATEGORY_CN_NAME                                                                    
               ,NULL AS DIMENSION_SUBCATEGORY_EN_NAME 			   
               ,SUM(SNOP_QUANTITY) AS SNOP_QUANTITY                                                                                     
           FROM  ALL_TMP	  
		    WHERE LV1_PROD_RND_TEAM_CODE IN ('100011')	   /*云核心网*/
			GROUP BY PERIOD_ID
		       ,PHASE_DATE                                                                                      
               ,PERIOD_FLAG                                                                                      
               ,BG_CODE				                                                                        
               ,BG_NAME                                                                                                                                                                                       
               ,MEASURE_CODE                                                                                     
               ,LV1_PROD_RND_TEAM_CODE			                                                                
               ,LV1_PROD_RD_TEAM_CN_NAME                                                            
               ,LV2_PROD_RND_TEAM_CODE			                                                                
               ,LV2_PROD_RD_TEAM_CN_NAME  
               ,OVERSEA_DESC			   
			;
			
			 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      
	  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '按场景汇总数据：SCENARIOS_TMP 目标表的数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		

            -- 删除最大版本数据
      DELETE FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PLAN_SUM_T WHERE VERSION_CODE = V_VERSION_CODE;
	  
	  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PLAN_SUM_T(	
       VERSION_CODE
     ,TIME_WINDOW_CODE
     ,PERIOD_ID
     ,PHASE_DATE
     ,BG_CODE
     ,BG_NAME
     ,OVERSEA_CODE
     ,OVERSEA_DESC
     ,LV1_PROD_RND_TEAM_CODE
     ,LV1_PROD_RD_TEAM_CN_NAME
     ,LV2_PROD_RND_TEAM_CODE
     ,LV2_PROD_RD_TEAM_CN_NAME
     ,SCENARIOS
     ,DIMENSION_GROUP_CODE   
     ,DIMENSION_GROUP_CN_NAME        
     ,DIMENSION_GROUP_EN_NAME
     ,DIMENSION_SUBCATEGORY_CODE    
     ,DIMENSION_SUBCATEGORY_CN_NAME 
     ,DIMENSION_SUBCATEGORY_EN_NAME 
     ,SNOP_QUANTITY
     ,SOURCE_TABLE
     ,REMARK
     ,CREATED_BY
     ,CREATION_DATE
     ,LAST_UPDATED_BY
     ,LAST_UPDATE_DATE
     ,DEL_FLAG
	  )
	  SELECT V_VERSION_CODE AS VERSION_CODE
	  ,'MONTH' AS TIME_WINDOW_CODE
      ,PERIOD_ID
      ,PHASE_DATE
      ,BG_CODE
      ,BG_NAME
	  ,CASE WHEN OVERSEA_DESC = '国内' THEN 'GH0002' 
	        WHEN OVERSEA_DESC = '海外' THEN 'GH0003'
			ELSE 'GH0001'
 			END AS OVERSEA_CODE
      ,OVERSEA_DESC
      ,LV1_PROD_RND_TEAM_CODE
      ,LV1_PROD_RD_TEAM_CN_NAME
      ,LV2_PROD_RND_TEAM_CODE
      ,LV2_PROD_RD_TEAM_CN_NAME
	  ,SCENARIOS
      ,DIMENSION_GROUP_CODE   
      ,DIMENSION_GROUP_CN_NAME        
      ,DIMENSION_GROUP_EN_NAME
      ,DIMENSION_SUBCATEGORY_CODE    
      ,DIMENSION_SUBCATEGORY_CN_NAME 
      ,DIMENSION_SUBCATEGORY_EN_NAME 
      ,SNOP_QUANTITY
      ,'DWK_PS_SOP_PLAN_COUNTRY_MEASURE_I' AS SOURCE_TABLE
      ,'' AS REMARK
 	  , -1 AS CREATED_BY
 	  , CURRENT_TIMESTAMP AS CREATION_DATE
 	  , -1 AS LAST_UPDATED_BY
 	  , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	  , 'N' AS DEL_FLAG
	  FROM SCENARIOS_TMP
	 
	  ;
	  
	  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
			   
	-- 写结束日志
	     V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '生成的版本编码:'||V_VERSION_CODE||'结果表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;


	--收集统计信息
	ANALYSE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PLAN_SUM_T;
	
--处理异常信息
	EXCEPTION
		WHEN OTHERS THEN
		
		 PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	X_SUCCESS_FLAG := 'FAIL';		        --2001表示失败

	
		
 END;
 $BODY$
 LANGUAGE PLPGSQL VOLATILE
  COST 100