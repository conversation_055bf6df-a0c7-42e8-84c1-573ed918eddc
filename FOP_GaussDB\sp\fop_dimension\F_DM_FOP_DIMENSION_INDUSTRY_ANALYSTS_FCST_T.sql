CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING, OUT X_SUCCESS_FLAG TEXT)
 RETURNS PG_CATALOG.TEXT AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：产业分析师预测结果表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
		  参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T();
*/
 
 DECLARE
	V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T('''||P_VERSION_CODE||')';
	V_TBL_NAME VARCHAR(100) := 'FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T';
	V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月_001
	V_VERSION_MONTH VARCHAR(50);  -- 目标表的当前版本年月，格式：年月
	V_STEP_NUM   NUMERIC; --步骤号
	V_DML_ROW_COUNT  NUMBER DEFAULT 0 ;


BEGIN
	X_SUCCESS_FLAG := 'SUCCESS';                                 --1表示成功
	
	
	-- 如果是传 VERSION_CODE 调函数取 传入的 P_VERSION_CODE ，如果是自动调度的 则从版本表取最新版本号
	 IF P_VERSION_CODE IS NOT NULL 
	 THEN
	 SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
	 ELSE 
	   -- 从版本表取最新版本号	    
	 SELECT VERSION_CODE INTO V_VERSION_CODE
	 FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_VERSION_T
	 WHERE STEP = 1
	 ORDER BY LAST_UPDATE_DATE DESC
	 LIMIT 1
	 ;
	 END IF 
	 ;
		
			-- 当前版本所在的年月
		SELECT SUBSTR(V_VERSION_CODE,1,6)  INTO V_VERSION_MONTH ;
	
	 --1.开始日志
  V_STEP_NUM := 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '产业分析师预测结果表'||V_TBL_NAME||',版本编码:'||V_VERSION_CODE||'，版本年月:'||V_VERSION_MONTH||',开始运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
	  
	   -- 取TGT表实际数的LV1和LV2，得到PRODUCT_CODE
	  DROP TABLE IF EXISTS PRODUCT_CODE_TMP;
	  CREATE TEMPORARY TABLE  PRODUCT_CODE_TMP
		     AS
     SELECT CASE WHEN LV2_CODE IS NULL THEN LV1_CODE
		         ELSE LV2_CODE
	             END AS PRODUCT_CODE,
	        CASE WHEN LV2_NAME IS NULL THEN LV1_NAME
		         ELSE LV2_NAME
	             END AS PRODUCT_NAME,
	        LV1_CODE,
	        LV1_NAME,
	        LV2_CODE,
	        LV2_NAME
       FROM(SELECT 
	        DISTINCT LV1_CODE,
		             LV1_NAME,
		             LV2_CODE,
		             LV2_NAME
	     FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T)T
		 ;
		 
		  V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
		  
		  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '取TGT表实际数的LV1和LV2，获取PRODUCT_CODE：PRODUCT_CODE_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		 
		  -- 来源表和维度树成员信息维表关联，获取维度信息
	  DROP TABLE IF EXISTS DIM_CODE_TMP;
	  CREATE TEMPORARY TABLE  DIM_CODE_TMP
		     AS
     SELECT DISTINCT A.DIM_CODE ,
	                 A.NODE_CODE,
	       CASE WHEN A.NODE_CODE='CBRS001' THEN 'ICT' 
		        ELSE A.NODE_CNAME 
				END AS NODE_CNAME
      FROM DIMMGT.DIM_DATA_TREE_P_C_NODE_I A 
       JOIN FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I B
    	 ON B.CUSTOMER_DIM_CODE = A.DIM_CODE
      WHERE A.NODE_CODE IN ('CBRS001', 'PDCG901160', 'PDCG901159', 'PDCG802450' , 'PDCG802558' , 'PDCG805902' , 'PDCG901163' , 'PDCG806200')
	  AND '20' || SUBSTRING(B.YEAR_CODE, '[0-9]+')||
	         CASE WHEN SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15<10 THEN '0' || CAST(SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
		          ELSE CAST(SUBSTRING(B.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
	              END = V_VERSION_MONTH
        ;
		
		V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
		  
		  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '获取维度信息：DIM_CODE_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
		
		 
	  DROP TABLE IF EXISTS INDUSTRY_ANALYSTS_FCST_TMP;
	  CREATE TEMPORARY TABLE  INDUSTRY_ANALYSTS_FCST_TMP
		     AS
      SELECT '20' || SUBSTRING(A.YEAR_CODE, '[0-9]+')||
	         CASE WHEN SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15<10 THEN '0' || CAST(SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
		          ELSE CAST(SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
	              END AS PERIOD_ID ,
	         '20' || SUBSTRING(A.YEAR_CODE, '[0-9]+')||
	    CASE WHEN UPPER(A.PERIOD_CODE) LIKE '%JAN%'       THEN substr(target_code,1,4)||'01'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%FEB%'       THEN substr(target_code,1,4)||'02'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%MAR%'       THEN substr(target_code,1,4)||'03'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%APR%'       THEN substr(target_code,1,4)||'04'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%MAY%'       THEN substr(target_code,1,4)||'05'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%JUN%'       THEN substr(target_code,1,4)||'06'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%JUL%'       THEN substr(target_code,1,4)||'07'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%AUG%'       THEN substr(target_code,1,4)||'08'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%SEP%'       THEN substr(target_code,1,4)||'09'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%OCT%'       THEN substr(target_code,1,4)||'10'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%NOV%'       THEN substr(target_code,1,4)||'11'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%DEC%'       THEN substr(target_code,1,4)||'12'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%YEARTOTAL%' THEN substr(target_code,1,4)||''
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%HY1%'       THEN substr(target_code,1,4)||'H1'
		     WHEN UPPER(A.PERIOD_CODE) LIKE '%HY2%'       THEN substr(target_code,1,4)||'H2'
		     ELSE A.PERIOD_CODE
	         END AS TARGET_CODE,
	    CASE WHEN A.CUSTOMER_CODE='CBRS001'   THEN 'PROD0002' 
		     ELSE A.CUSTOMER_CODE 
			 END AS BG_CODE,
	         D.NODE_CNAME AS BG_NAME,
	         A.GEOGRAPHY_CODE AS OVERSEA_CODE,
	    CASE WHEN A.GEOGRAPHY_CODE='GH0001'   THEN '全球' 
		     WHEN A.GEOGRAPHY_CODE='GH0002'   THEN '国内' 
			 WHEN A.GEOGRAPHY_CODE='GH0003'   THEN '海外' 
			 END AS OVERSEA_DESC ,
	         A.CURRENCY_CODE AS CURRENCY ,
	         B.LV1_CODE,
	         B.LV1_NAME,
	         B.LV2_CODE,
	         B.LV2_NAME,
	         A.VERSION_CODE,
	         C.NODE_CNAME,
	         A.AMOUNT
        FROM FIN_DM_OPT_FOP.DWK_ICTPS_PL_LV2_DIM_FCST_I A
        JOIN PRODUCT_CODE_TMP B 
		  ON A.PRODUCT_CODE = B.PRODUCT_CODE
   LEFT JOIN DIMMGT.DIM_DATA_TREE_P_C_NODE_I C 
          ON A.INDEX_CODE = C.NODE_CODE
	     AND A.INDEX_DIM_CODE = C.DIM_CODE
   LEFT JOIN DIM_CODE_TMP D 
          ON A.CUSTOMER_CODE=D.NODE_CODE
       WHERE 1=1
	   AND '20' || SUBSTRING(A.YEAR_CODE, '[0-9]+')||
	         CASE WHEN SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15<10 THEN '0' || CAST(SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
		          ELSE CAST(SUBSTRING(A.SCENARIO_CODE, '[0-9]+')::INT-15 AS VARCHAR)
	              END = V_VERSION_MONTH
         AND A.GEOGRAPHY_CODE IN ('GH0001','GH0002','GH0003')
         AND A.VERSION_CODE = CASE WHEN A.VERSION_CODE = 'VER088' AND B.LV2_CODE = '' THEN 'DEL'
	                               ELSE A.VERSION_CODE
                                    END 
									;
									
									
		V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
		  
		  V_STEP_NUM := V_STEP_NUM + 1;
      PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_STEP_NUM  => V_STEP_NUM,
          P_LOG_CAL_LOG_DESC => '来源表逻辑处理：INDUSTRY_ANALYSTS_FCST_TMP，数据量：'||V_DML_ROW_COUNT,--日志描述
          P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
          P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
          P_LOG_ERRBUF => NULL  --错误编码
        ) ;
	  
	  
	  DELETE FROM FIN_DM_OPT_FOP.dm_fop_dimension_industry_analysts_fcst_t_bck WHERE VERSION_CODE = V_VERSION_CODE ;
	  
	  
	 INSERT INTO FIN_DM_OPT_FOP.dm_fop_dimension_industry_analysts_fcst_t_bck(
	      VERSION_CODE ,
		  PERIOD_ID,
	      TARGET_CODE ,
		  TARGET_DESC,
	      BG_CODE ,
	      BG_NAME ,
	      OVERSEA_CODE,
	      OVERSEA_DESC ,
	      CURRENCY ,
	      LV1_CODE ,
	      LV1_NAME ,
	      LV2_CODE ,
	      LV2_NAME ,
	      EQUIP_REV_AMT ,
	      MGP_RATIO ,
		  REMARK,
	      CREATED_BY ,
	      CREATION_DATE ,
	      LAST_UPDATED_BY ,
	      LAST_UPDATE_DATE ,
	      DEL_FLAG 
		  )
   SELECT V_VERSION_CODE AS VERSION_CODE,
          PERIOD_ID,
	      TARGET_CODE ,
		  CASE WHEN TARGET_CODE LIKE '%H%' THEN '半年度'
		       WHEN TARGET_CODE LIKE '%Q%' THEN '季度'
			   WHEN LENGTH(TARGET_CODE) = 4 THEN '年度'
			   ELSE '月度'
		        END AS TARGET_DESC,
	      BG_CODE ,
	      BG_NAME ,
	      OVERSEA_CODE,
	      OVERSEA_DESC ,
	      CURRENCY,
	      LV1_CODE ,
	      LV1_NAME ,
	      LV2_CODE ,
	      LV2_NAME ,
	      SUM(CASE WHEN NODE_CNAME = '设备收入' THEN AMOUNT ELSE 0 END) AS EQUIP_REV_AMT,
	      SUM(CASE WHEN NODE_CNAME = '制造毛利率' THEN AMOUNT ELSE 0 END) AS MGP_RATIO,
		  '' AS REMARK,
	      '-1' AS CREATED_BY ,
	      CURRENT_TIMESTAMP AS CREATION_DATE ,
	      '-1' AS LAST_UPDATED_BY ,
	      CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
	      'N' AS DEL_FLAG
   FROM INDUSTRY_ANALYSTS_FCST_TMP
   GROUP BY PERIOD_ID ,
	        TARGET_CODE ,
	        BG_CODE ,
	        BG_NAME ,
			OVERSEA_CODE,
	        OVERSEA_DESC ,
	        CURRENCY,
	        LV1_CODE ,
	        LV1_NAME ,
	        LV2_CODE ,
	        LV2_NAME
			;


		 V_DML_ROW_COUNT := SQL%ROWCOUNT;   -- 收集数据量
      		
		-- 写结束日志
	     V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '生成的版本编码:'||V_VERSION_CODE||'结果表:'||V_TBL_NAME||'数据量:'||V_DML_ROW_COUNT||',结束运行',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => V_DML_ROW_COUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;

	--收集统计信息
	ANALYSE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T;

--处理异常信息
	EXCEPTION
		WHEN OTHERS THEN
		
		 PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
          P_LOG_VERSION_ID => NULL,                 --版本
          P_LOG_SP_NAME => V_SP_NAME,    --SP名称
          P_LOG_PARA_LIST => '',--参数
          P_LOG_CAL_LOG_DESC => V_SP_NAME||'：运行错误',--日志描述
          P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
          P_LOG_ERRBUF => SQLSTATE  --错误编码
        ) ;
  	X_SUCCESS_FLAG := 'FAIL';		        --2001表示失败

	
		
 END;
 $BODY$
 LANGUAGE PLPGSQL VOLATILE
  COST 100

  
