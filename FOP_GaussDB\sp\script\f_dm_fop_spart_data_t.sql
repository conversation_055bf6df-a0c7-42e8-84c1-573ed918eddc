-- ----------------------------
-- Function structure for f_dm_fop_spart_data_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_data_t"("p_version_code" varchar, "p_keystr" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_data_t"(IN "p_version_code" varchar, IN "p_keystr" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*********************************************************************************************************************************************************************
创建时间：2023-6-2
创建人  ：qwx1110218
背景描述：计算Spart粒度的均本均价，来源表是detail_info宽表，字段：L2接口表字段+spart_code，不含S&OP的指标，不含2个发货量；前台用于展示，不能对外提供。
参数描述：参数一(p_version_code)：版本编码，参数格式：202305_V1
		      参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_data_t()
          2023-08-02 lwx1186472	  202309版本区域增加'国内'、'海外'逻辑  
*********************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_data_t('''||p_version_code||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_data_t';
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;

begin
	x_success_flag := '1';

  -- 写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'Spart粒度数据表 '||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  );
  
  -- 创建 spart_detail_info_tmp1 临时表
  drop table if exists spart_detail_info_tmp1;
	create temporary table spart_detail_info_tmp1(
	       version_code                   varchar(50)        -- 版本编码
	     , period_id                      numeric            -- 会计期
		   , spart_code                     varchar(150)       -- spart编码
	     , spart_desc                     varchar(2000)      -- spart描述
		   , bg_code                        varchar(50)        -- BG编码
		   , bg_cn_name                     varchar(200)       -- BG中文名称
		   , bg_en_name                     varchar(200)       -- BG英文名称
		   , oversea_flag                   varchar(50)        -- 区域标识
		   , lv1_prod_rnd_team_code         varchar(100)       -- 重量级团队lv1编码
	     , lv1_prod_rd_team_cn_name       varchar(600)       -- 重量级团队lv1中文描述
	     , lv1_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV1英文描述
	     , lv2_prod_rnd_team_code         varchar(100)       -- 重量级团队lv2编码
	     , lv2_prod_rd_team_cn_name       varchar(600)       -- 重量级团队lv2中文名称
	     , lv2_prod_rd_team_en_name       varchar(600)       -- 重量级团队LV2英文描述
		   , l1_name                        varchar(200)       -- l1名称
		   , l2_name                        varchar(200)       -- l2名称
		   , coa_l2_name                    varchar(200)       -- l2名称
		   , l2_coefficient                 numeric(38,10)     -- l2系数
		   , articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		   , currency                       varchar(50)        -- 币种
		   , equip_rev_cons_before_amt      numeric(38,10)     -- 设备收入额（对价前）
		   , equip_cost_cons_before_amt     numeric(38,10)     -- 设备成本额（对价前）
		   , spart_qty                      numeric(38,10)     -- 收入量（历史）
	)on commit preserve rows distribute by /*replication*/ hash(spart_code,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,l1_name)
	;

	-- 取来源表最大版本
	select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_detail_info_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_info_t where del_flag = 'N')
     and del_flag = 'N'
   group by substr(version_code,1,6)
	;

	-- 如果传入版本无值，则取最大版本的数据
	if(p_version_code is null or p_version_code = '') then
	  -- 删除目标表中最大版本数据
	  delete fin_dm_opt_fop.dm_fop_spart_data_his_t where version_code = v_max_version_code;
    
    insert into spart_detail_info_tmp1(
	       version_code                       -- 版本编码
	     , period_id                          -- 会计期
		   , spart_code                         -- spart编码
	     , spart_desc                         -- spart描述
		   , bg_code                            -- BG编码
		   , bg_cn_name                         -- BG中文名称
		   , bg_en_name                         -- BG英文名称
		   , oversea_flag                       -- 区域标识
		   , lv1_prod_rnd_team_code             -- 重量级团队lv1编码
	     , lv1_prod_rd_team_cn_name           -- 重量级团队lv1中文描述
	     , lv1_prod_rd_team_en_name           -- 重量级团队LV1英文描述
	     , lv2_prod_rnd_team_code             -- 重量级团队lv2编码
	     , lv2_prod_rd_team_cn_name           -- 重量级团队lv2中文名称
	     , lv2_prod_rd_team_en_name           -- 重量级团队LV2英文描述
		   , l1_name                            -- l1名称
		   , l2_name                            -- l2名称
		   , coa_l2_name                        -- l2名称
		   , l2_coefficient                     -- l2系数
		   , articulation_flag                  -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		   , currency                           -- 币种
		   , equip_rev_cons_before_amt          -- 设备收入额（对价前）
		   , equip_cost_cons_before_amt         -- 设备成本额（对价前）
		   , spart_qty                          -- 收入量（历史）
	  )
	  with spart_detail_info_tmp0 as(
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name    -- bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , t.l2_coefficient            -- l2系数
	  		 , t.articulation_flag
	  		 , t.rmb_revenue   -- 收入金额(对价前)
	  		 , t.rmb_cost   -- 成本金额(对价前)
	  		 , t.usd_revenue   -- 收入金额(对价前)
	  		 , t.usd_cost   -- 成本金额(对价前)
	  		 , t.spart_qty  -- 收入量（历史）
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in('SCENO1','SCENO2','SCENO3') --勾稽方法标签限制场景1和场景2,1月版新增场景3
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = v_max_version_code  -- 取最大版本数据
	     and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	     and t.del_flag = 'N'
	  )
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name as bg_cn_name  --	bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
	  		 , t.articulation_flag
	  		 , 'CNY' as currency
	  		 , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
	  		 , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty  -- 收入量（历史）
	    from spart_detail_info_tmp0 t
     group by t.version_code
	       , t.period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
	   union all
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name as bg_cn_name  --	bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
	  		 , t.articulation_flag
	  		 , 'USD' as currency
	  		 , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
	  		 , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty  -- 收入量（历史）
	    from spart_detail_info_tmp0 t
     group by t.version_code
	       , t.period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
    ;
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'， spart_detail_info_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    

  else
	  -- 传入版本有值，判断传入版本判断来源表中是否有对应版本数据，如有，则删除目标表中对应的版本数据
	  if exists(select version_code from fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = p_version_code and del_flag = 'N') then
	    -- 删除传入的版本数据
	    delete fin_dm_opt_fop.dm_fop_spart_data_his_t where version_code = p_version_code;
      
      insert into spart_detail_info_tmp1(
	       version_code                       -- 版本编码
	     , period_id                          -- 会计期
		   , spart_code                         -- spart编码
	     , spart_desc                         -- spart描述
		   , bg_code                            -- BG编码
		   , bg_cn_name                         -- BG中文名称
		   , bg_en_name                         -- BG英文名称
		   , oversea_flag                       -- 区域标识
		   , lv1_prod_rnd_team_code             -- 重量级团队lv1编码
	     , lv1_prod_rd_team_cn_name           -- 重量级团队lv1中文描述
	     , lv1_prod_rd_team_en_name           -- 重量级团队LV1英文描述
	     , lv2_prod_rnd_team_code             -- 重量级团队lv2编码
	     , lv2_prod_rd_team_cn_name           -- 重量级团队lv2中文名称
	     , lv2_prod_rd_team_en_name           -- 重量级团队LV2英文描述
		   , l1_name                            -- l1名称
		   , l2_name                            -- l2名称
		   , coa_l2_name                        -- l2名称
		   , l2_coefficient                     -- l2系数
		   , articulation_flag                  -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		   , currency                           -- 币种
		   , equip_rev_cons_before_amt          -- 设备收入额（对价前）
		   , equip_cost_cons_before_amt         -- 设备成本额（对价前）
		   , spart_qty                          -- 收入量（历史）
	  )
	  with spart_detail_info_tmp0 as(
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name    -- bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , t.l2_coefficient            -- l2系数
	  		 , t.articulation_flag
	  		 , t.rmb_revenue   -- 收入金额(对价前)
	  		 , t.rmb_cost   -- 成本金额(对价前)
	  		 , t.spart_qty  -- 收入量（历史）
	    from fin_dm_opt_fop.dm_fop_spart_detail_info_t t
	   where t.articulation_flag in('SCENO1','SCENO2','SCENO3') --勾稽方法标签限制场景1和场景2,1月版新增场景3
	     and upper(t.industry_type) = 'TGT'
	     and t.version_code = p_version_code  -- 取传入版本数据
	     and t.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除S&OP预测表、S&OP预算表
	     and t.del_flag = 'N'
	  )
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name as bg_cn_name  --	bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
	  		 , t.articulation_flag
	  		 , 'CNY' as currency
	  		 , sum(nvl(t.rmb_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
	  		 , sum(nvl(t.rmb_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty  -- 收入量（历史）
	    from spart_detail_info_tmp0 t
     group by t.version_code
	       , t.period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
	   union all
	  select t.version_code  -- 版本编码
	       , t.period_id  --	会计期
	  		 , t.spart_code -- spart编码
	  		 , t.spart_desc -- spart描述
	  		 , t.bg_code    --	bg编码
	  		 , t.bg_name as bg_cn_name  --	bg中文名称
	  		 , t.bg_en_name             -- BG英文名称
	  		 , t.oversea_flag           -- 区域描述
	  		 , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
	  		 , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
	  		 , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
	  		 , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
	  		 , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
	  		 , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
	  		 , t.l1_name                   -- l1名称
	  		 , t.l2_name                   -- l2名称
	  		 , t.coa_l2_name               -- coa_l2名称
	  		 , round(t.l2_coefficient,6) as l2_coefficient   -- l2系数
	  		 , t.articulation_flag
	  		 , 'USD' as currency
	  		 , sum(nvl(t.usd_revenue,0))        as equip_rev_cons_before_amt   -- 收入金额(对价前)
	  		 , sum(nvl(t.usd_cost,0))           as equip_cost_cons_before_amt  -- 成本金额(对价前)
	  		 , sum(nvl(t.spart_qty,0))          as spart_qty  -- 收入量（历史）
	    from spart_detail_info_tmp0 t
     group by t.version_code
	       , t.period_id
	  		 , t.spart_code
	  		 , t.spart_desc
	  		 , t.bg_code
	  		 , t.bg_name
	  		 , t.bg_en_name
	  		 , t.oversea_flag
	  		 , t.lv1_prod_rnd_team_code
	  		 , t.lv1_prod_rd_team_cn_name
	  		 , t.lv1_prod_rd_team_en_name
	  		 , t.lv2_prod_rnd_team_code
	  		 , t.lv2_prod_rd_team_cn_name
	  		 , t.lv2_prod_rd_team_en_name
	  		 , t.l1_name
	  		 , t.l2_name
	  		 , t.coa_l2_name
	  		 , round(t.l2_coefficient,6)
	  		 , t.articulation_flag
    ;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本：'||p_version_code||'， spart_detail_info_tmp1 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      

	  else
	    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '来源表中没有传入版本数据，请传入已存在的版本编码！'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => null,
          p_log_errbuf => null  --错误编码
        ) ;
     x_success_flag := '2001';
     return;

	  end if;
  end if;
  
  insert into fin_dm_opt_fop.dm_fop_spart_data_his_t(
	       version_code
       , period_id
       , spart_code
       , spart_desc
       , bg_code
       , bg_cn_name
       , bg_en_name
       , oversea_desc
       , lv1_prod_rnd_team_code
       , lv1_prod_rd_team_cn_name
       , lv1_prod_rd_team_en_name
       , lv2_prod_rnd_team_code
       , lv2_prod_rd_team_cn_name
       , lv2_prod_rd_team_en_name
       , l1_name
       , l2_name
       , l2_coefficient
       , currency
       , equip_rev_cons_before_amt
       , equip_cost_cons_before_amt
       , spart_qty
       , avg_cost
       , avg_price
       , rev_percent
       , cost_percent
       , mgp_ratio
       , articulation_flag
       , remark
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
  )
  -- 处理币种字段，拆分为人民币和美元
	with spart_detail_info_tmp as(
   select t.version_code  -- 版本编码
         , t.period_id  --	会计期
         , t.spart_code -- spart编码
         , t.spart_desc -- spart描述
         , t.bg_code    --	bg编码
         , t.bg_cn_name  --	bg中文名称
         , t.bg_en_name             -- BG英文名称
         , (case when t.oversea_flag = 'Y' then '海外'
                 when t.oversea_flag = 'N' then '国内'
            end) as oversea_desc           -- 区域描述
         , t.lv1_prod_rnd_team_code --	重量级团队lv1编码
         , t.lv1_prod_rd_team_cn_name  -- 重量级团队lv1中文描述
         , t.lv1_prod_rd_team_en_name  -- 重量级团队LV1英文描述
         , t.lv2_prod_rnd_team_code    -- 重量级团队lv2编码
         , t.lv2_prod_rd_team_cn_name  -- 重量级团队lv2中文名称
         , t.lv2_prod_rd_team_en_name  -- 重量级团队LV2英文描述
         , t.l1_name                   -- l1名称
         , (case when t.coa_l2_name is not null then t.coa_l2_name else t.l2_name end) as l2_name  -- l2名称
         , t.l2_coefficient   -- l2系数
         , t.currency
         , t.articulation_flag
         , t.equip_rev_cons_before_amt   -- 收入金额(对价前)
         , t.equip_cost_cons_before_amt  -- 成本金额(对价前)
         , t.spart_qty  -- 收入量（历史）
      from spart_detail_info_tmp1 t
  ),
	--区域描述打上全球的标签
	-- 202309版本区域增加'国内'、'海外'逻辑
	 oversea_desc_temp as (
  select version_code                    -- 版本编码
	     , period_id                       -- 会计期
		   , spart_code                      -- spart编码
	     , spart_desc                      -- spart描述
		   , bg_code                         -- bg编码
		   , bg_cn_name                      -- BG中文名称
	     , bg_en_name                      -- BG英文名称
		   , oversea_desc          -- 区域（全球/国内/海外）
		   , lv1_prod_rnd_team_code          -- 重量级团队lv1编码
	     , lv1_prod_rd_team_cn_name        -- 重量级团队lv1中文描述
	     , lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
	     , lv2_prod_rnd_team_code          -- 重量级团队lv2编码
	     , lv2_prod_rd_team_cn_name        -- 重量级团队lv2中文名称
	     , lv2_prod_rd_team_en_name        -- 重量级团队LV2英文描述
		   , l1_name                         -- l1名称
		   , l2_name                         -- l2名称
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)      as equip_rev_cons_before_amt   -- 收入金额(对价前)
		   , sum(equip_cost_cons_before_amt)     as equip_cost_cons_before_amt  -- 成本金额(对价前)
		   , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty                   -- 收入量（历史）
	  from spart_detail_info_tmp
     where oversea_desc is not null    --国内、海外需要排除空值	  
   group by version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		 , oversea_desc
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag   
union all	   
  select version_code                    -- 版本编码
	     , period_id                       -- 会计期
		   , spart_code                      -- spart编码
	     , spart_desc                      -- spart描述
		   , bg_code                         -- bg编码
		   , bg_cn_name                      -- BG中文名称
	     , bg_en_name                      -- BG英文名称
		   , '全球' as oversea_desc          -- 区域（全球/国内/海外）
		   , lv1_prod_rnd_team_code          -- 重量级团队lv1编码
	     , lv1_prod_rd_team_cn_name        -- 重量级团队lv1中文描述
	     , lv1_prod_rd_team_en_name        -- 重量级团队LV1英文描述
	     , lv2_prod_rnd_team_code          -- 重量级团队lv2编码
	     , lv2_prod_rd_team_cn_name        -- 重量级团队lv2中文名称
	     , lv2_prod_rd_team_en_name        -- 重量级团队LV2英文描述
		   , l1_name                         -- l1名称
		   , l2_name                         -- l2名称
		   , l2_coefficient
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)      as equip_rev_cons_before_amt   -- 收入金额(对价前)
		   , sum(equip_cost_cons_before_amt)     as equip_cost_cons_before_amt  -- 成本金额(对价前)
		   , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty                   -- 收入量（历史）
	  from spart_detail_info_tmp
   group by version_code
	     , period_id
		   , spart_code
	     , spart_desc
		   , bg_code
		   , bg_cn_name
	     , bg_en_name
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name
		   , l2_name
		   , l2_coefficient
		   , currency
		   , articulation_flag
  ),
  -- bg名称和bg编码打上集团的标签
	bg_name_temp as(
	select version_code
	     , period_id              -- 会计期
		   , spart_code
	     , spart_desc
		   , 'PROD0002' as bg_code  -- bg编码
		   , 'ICT' as bg_cn_name    -- bg中文名称（集团即ICT）
		   , 'ICT' as bg_en_name    -- bg英文名称
		   , oversea_desc           -- 区域描述
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name            -- l1名称
		   , l2_name            -- l2名称
		   , l2_coefficient     -- l2系数
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt) as equip_rev_cons_before_amt    -- 收入金额(对价前)
		   , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt  -- 成本金额(对价前)
		   , sum(spart_qty) as spart_qty   -- 收入量（历史）
	  from oversea_desc_temp
   group by version_code
	     , period_id              -- 会计期
		   , spart_code
	     , spart_desc
		   , oversea_desc           -- 区域描述
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name            -- l1名称
		   , l2_name            -- l2名称
		   , l2_coefficient     -- l2系数
		   , currency
		   , articulation_flag
   union all
  select version_code
	     , period_id              -- 会计期
		   , spart_code
	     , spart_desc
		   , bg_code  -- bg编码
		   , bg_cn_name    -- bg中文名称（集团即ICT）
		   , bg_en_name    -- bg英文名称
		   , oversea_desc           -- 区域描述
		   , lv1_prod_rnd_team_code
	     , lv1_prod_rd_team_cn_name
	     , lv1_prod_rd_team_en_name
	     , lv2_prod_rnd_team_code
	     , lv2_prod_rd_team_cn_name
	     , lv2_prod_rd_team_en_name
		   , l1_name            -- l1名称
		   , l2_name            -- l2名称
		   , l2_coefficient     -- l2系数
		   , currency
		   , articulation_flag
		   , equip_rev_cons_before_amt    -- 收入金额(对价前)
		   , equip_cost_cons_before_amt   -- 成本金额(对价前)
		   , spart_qty                    -- 收入量（历史）
	  from oversea_desc_temp
  ),
  -- 按照l2层收敛
  l2_temp as(
	select version_code             -- 版本编码
	     , period_id                -- 会计期
		   , bg_code                  -- bg编码
		   , oversea_desc             -- 区域描述
		   , lv1_prod_rnd_team_code   -- 重量级团队lv1编码
		   , lv2_prod_rnd_team_code   -- 重量级团队lv2编码
		   , l1_name                  -- l1名称
		   , l2_name                  -- l2名称
		   , currency
		   , articulation_flag
		   , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt   -- 收入金额(对价前)
		   , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt  -- 成本金额(对价前)
		   , sum(spart_qty)                  as spart_qty  -- 收入量（历史）
	  from bg_name_temp
   group by version_code
	     , period_id
		   , bg_code
		   , oversea_desc
		   , lv1_prod_rnd_team_code
		   , lv2_prod_rnd_team_code
		   , l1_name
		   , l2_name
		   , currency
		   , articulation_flag
	),
	--计算收入占比、制毛率
	all_temp_01 as(
	select t1.version_code                      -- 版本编码
	     , t1.period_id			                     -- 会计期
		   , t1.spart_code
	     , t1.spart_desc
		   , t1.bg_code                               -- bg编码
		   , t1.bg_cn_name                            -- bg中文名称（集团即ICT）
		   , t1.bg_en_name                            -- bg英文名称
		   , t1.oversea_desc											 -- 区域
		   , t1.lv1_prod_rnd_team_code
	     , t1.lv1_prod_rd_team_cn_name
	     , t1.lv1_prod_rd_team_en_name
	     , t1.lv2_prod_rnd_team_code
	     , t1.lv2_prod_rd_team_cn_name
	     , t1.lv2_prod_rd_team_en_name
		   , t2.l1_name														 -- l1名称
		   , t1.l2_name														 -- l2名称
		   , t1.l2_coefficient                     -- l2系数
		   , t1.currency													 -- 币种
		   , t1.articulation_flag                  -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		   , t1.equip_rev_cons_before_amt					 -- 设备收入额(对价前)
		   , t1.equip_cost_cons_before_amt				 -- 设备成本额(对价前)
		   , t1.spart_qty 	--收入量（历史）
		   , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				       when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
				       else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt end) as rev_percent	--收入占比 =  Spart金额/L2对价前收入金额
		   , (case when nvl(t2.equip_cost_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
				       when nvl(t2.equip_cost_cons_before_amt,0) = 0 then -999999
				       else t1.equip_cost_cons_before_amt / t2.equip_cost_cons_before_amt end) as cost_percent	--成本占比 =  Spart金额/L2对价前成本金额
		   , (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) = 0 then 0
				       when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) <> 0 then -999999
				       else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt end) as mgp_ratio	--制毛率 = 1 - Spart粒度对价前成本金额/ Spart粒度对价前收入金额
	  from bg_name_temp t1
    left join l2_temp t2
		  on t1.version_code = t2.version_code
		 and t1.period_id = t2.period_id
	   and t1.bg_code = t2.bg_code
	   and t1.oversea_desc = t2.oversea_desc
	   and t1.lv1_prod_rnd_team_code = t2.lv1_prod_rnd_team_code--	重量级团队lv1编码
	   and t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code--	重量级团队lv2编码
	   and t1.l1_name = t2.l1_name
	   and t1.l2_name = t2.l2_name
	   and t1.currency = t2.currency--币种
	   and t1.articulation_flag = t2.articulation_flag
	 where t1.l2_name is not null or t1.l2_name <> ''  
	),
	-- 计算单位成本、单位价格
	all_temp as(
	select t1.version_code                   -- 版本编码
	     , t1.period_id											 -- 会计期
		   , t1.spart_code                     -- spart编码
	     , t1.spart_desc                     -- spart描述
		   , t1.bg_code                        -- BG编码
		   , t1.bg_cn_name                     -- BG中文名称
		   , t1.bg_en_name                     -- BG英文名称
		   , t1.oversea_desc									 -- 区域
		   , t1.lv1_prod_rnd_team_code         -- 重量级团队lv1编码
	     , t1.lv1_prod_rd_team_cn_name       -- 重量级团队lv1中文描述
	     , t1.lv1_prod_rd_team_en_name       -- 重量级团队LV1英文描述
	     , t1.lv2_prod_rnd_team_code         -- 重量级团队lv2编码
	     , t1.lv2_prod_rd_team_cn_name       -- 重量级团队lv2中文名称
	     , t1.lv2_prod_rd_team_en_name       -- 重量级团队LV2英文描述
		   , t1.l1_name												 -- l1名称
		   , t1.l2_name								         -- l2名称
		   , t1.l2_coefficient                 -- l2系数
		   , t1.currency											 -- 币种
		   , t1.articulation_flag              -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
		   , t1.equip_rev_cons_before_amt		   -- 设备收入额(对价前)
		   , t1.equip_cost_cons_before_amt		 -- 设备成本额(对价前)
		   , t1.spart_qty 	                   -- 收入量（历史）
		   , (case when nvl(t1.equip_cost_cons_before_amt,0) = 0 then null
	             when nvl(t1.spart_qty,0) = 0 then -999999
	             else t1.equip_cost_cons_before_amt / t1.spart_qty end) as avg_cost	-- 平均成本 = Spart粒度对价前成本金额/Spart粒度收入数量/L2系数
		   , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
               when nvl(t1.spart_qty,0) = 0 then -999999
               else t1.equip_rev_cons_before_amt / t1.spart_qty end) as avg_price	-- 平均价格 = Spart粒度对价前收入金额/Spart粒度收入数量/L2系数
		   , t1.rev_percent	        -- 收入占比 = l2对价前收入金额/l1对价前收入金额
		   , t1.cost_percent        -- 成本占比 = l2对价前成本金额/l1对价前成本金额
		   , t1.mgp_ratio           -- 制毛率 = 1 - l2对价前成本金额/l2对价前收入金额
    from all_temp_01 t1
   where nvl(t1.equip_rev_cons_before_amt,0) > 0
      or nvl(t1.equip_rev_cons_before_amt,0) < 0
			or nvl(t1.equip_cost_cons_before_amt,0) > 0
			or nvl(t1.equip_cost_cons_before_amt,0) < 0
			or nvl(t1.spart_qty,0) > 0
			or nvl(t1.spart_qty,0) < 0
	)
	select /*+set global(best_agg_plan 1) set global(query_dop 4)*/version_code
       , period_id
       , spart_code
       , spart_desc
       , bg_code
       , bg_cn_name
       , bg_en_name
       , oversea_desc
       , lv1_prod_rnd_team_code
       , lv1_prod_rd_team_cn_name
       , lv1_prod_rd_team_en_name
       , lv2_prod_rnd_team_code
       , lv2_prod_rd_team_cn_name
       , lv2_prod_rd_team_en_name
       , l1_name
       , l2_name
       , l2_coefficient
       , currency
       , equip_rev_cons_before_amt
       , equip_cost_cons_before_amt
       , spart_qty
       , avg_cost
       , avg_price
       , rev_percent
       , cost_percent
       , mgp_ratio
       , articulation_flag
       , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	  from all_temp
	 /*where nvl(equip_rev_cons_before_amt,0) <> 0
			or nvl(equip_cost_cons_before_amt,0) <> 0
			or nvl(spart_qty,0) <> 0*/
	;

	v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'dm_fop_spart_data_his_t Spart粒度数据历史表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 清空目标表数据
  truncate table fin_dm_opt_fop.dm_fop_spart_data_t;
  
  -- 数据入到目标表
  if(p_version_code is null or p_version_code = '') then
    insert into fin_dm_opt_fop.dm_fop_spart_data_t (
	         period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_amt
         , equip_cost_cons_before_amt
         , spart_qty
         , avg_cost
         , avg_price
         , rev_percent
         , cost_percent
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_amt
         , GS_ENCRYPT(equip_cost_cons_before_amt,p_keystr,'AES128', 'CBC', 'SHA256') as equip_cost_cons_before_amt
         , spart_qty
         , GS_ENCRYPT(avg_cost,p_keystr,'AES128', 'CBC', 'SHA256') as avg_cost
         , avg_price
         , rev_percent
         , cost_percent
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_fop.dm_fop_spart_data_his_t
     where version_code = v_max_version_code  -- 取最大版本数据
    ;
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，入到目标表 dm_fop_spart_data_t 数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  else
    insert into fin_dm_opt_fop.dm_fop_spart_data_t (
	         period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_amt
         , equip_cost_cons_before_amt
         , spart_qty
         , avg_cost
         , avg_price
         , rev_percent
         , cost_percent
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select period_id
         , spart_code
         , spart_desc
         , bg_code
         , bg_cn_name
         , bg_en_name
         , oversea_desc
         , lv1_prod_rnd_team_code
         , lv1_prod_rd_team_cn_name
         , lv1_prod_rd_team_en_name
         , lv2_prod_rnd_team_code
         , lv2_prod_rd_team_cn_name
         , lv2_prod_rd_team_en_name
         , l1_name
         , l2_name
         , l2_coefficient
         , currency
         , equip_rev_cons_before_amt
         , GS_ENCRYPT(equip_cost_cons_before_amt,p_keystr,'AES128', 'CBC', 'SHA256') as equip_cost_cons_before_amt
         , spart_qty
         , GS_ENCRYPT(avg_cost,p_keystr,'AES128', 'CBC', 'SHA256') as avg_cost
         , avg_price
         , rev_percent
         , cost_percent
         , mgp_ratio
         , articulation_flag
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_fop.dm_fop_spart_data_his_t
     where version_code = p_version_code  -- 取传入版本数据
    ;
    
    v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '传入版本：'||p_version_code||'，入到目标表 dm_fop_spart_data_t 数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  end if;
  

  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_data_his_t;
	analyse fin_dm_opt_fop.dm_fop_spart_data_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

