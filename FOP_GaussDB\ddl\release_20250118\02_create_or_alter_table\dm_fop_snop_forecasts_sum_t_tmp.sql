-- ----------------------------
-- Table structure for dm_fop_snop_forecasts_sum_t_tmp
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_t_tmp";
CREATE TABLE "fin_dm_opt_fop"."dm_fop_snop_forecasts_sum_t_tmp" (
  "month" varchar(100) COLLATE "pg_catalog"."default",
  "phase_date" varchar(100) COLLATE "pg_catalog"."default",
  "item_code" varchar(400) COLLATE "pg_catalog"."default",
  "plan_unit_quantity" numeric(38,10),
  "unit" varchar(1000) COLLATE "pg_catalog"."default",
  "quantity" numeric(38,10),
  "prod_code" varchar(500) COLLATE "pg_catalog"."default",
  "bg_code" varchar(100) COLLATE "pg_catalog"."default",
  "bg_cn_name" varchar(1000) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv0_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv1_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv2_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rnd_team_code" varchar(50) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "lst_lv3_prod_rd_team_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "plan_type" varchar(100) COLLATE "pg_catalog"."default",
  "plan_com_lv1" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv2" varchar(1000) COLLATE "pg_catalog"."default",
  "plan_com_lv3" varchar(1000) COLLATE "pg_catalog"."default",
  "busi_lv4" varchar(1000) COLLATE "pg_catalog"."default",
  "port_qty" numeric,
  "remark" text COLLATE "pg_catalog"."default",
  "created_by" int8,
  "creation_date" timestamp(6),
  "last_updated_by" int8,
  "last_update_date" timestamp(6),
  "del_flag" varchar(10) COLLATE "pg_catalog"."default",
  "version_code" varchar(100) COLLATE "pg_catalog"."default",
  "prod_key" numeric,
  "prod_cn_name" varchar(600) COLLATE "pg_catalog"."default",
  "prod_en_name" varchar(600) COLLATE "pg_catalog"."default",
  "bg_en_name" varchar(1000) COLLATE "pg_catalog"."default",
  "is_release_version_flag" text COLLATE "pg_catalog"."default",
  "oversea_desc" varchar(100) COLLATE "pg_catalog"."default"
)
;

