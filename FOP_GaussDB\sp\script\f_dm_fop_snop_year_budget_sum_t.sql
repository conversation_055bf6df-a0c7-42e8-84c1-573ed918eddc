-- ----------------------------
-- Function structure for f_dm_fop_snop_year_budget_sum_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_snop_year_budget_sum_t"("p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_snop_year_budget_sum_t"(IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2022-12-16
创建人  ：鲁广武  lwx1186472
更新时间：2023-02-03
背景描述：产品线年度S&OP预算汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期(年月)
		  参数三(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_snop_year_budget_sum_t()
修改记录：2023-02-03 lwx1186472   新增会计期参数，新增版本字段
          传入会计期，有值：传入值格式：f_dm_fop_snop_year_budget_sum_t(202212)
          传入会计期，无值：传入值格式：f_dm_fop_snop_year_budget_sum_t()
          2023-04-19 lwx1186472   新增字段;放宽年份，不限制取一年;修改取最大版本逻辑
		  20230731   lwx1186472 上游集成表切换BCM的表,表名更改为 fop_dwk_grp_snop_year_fc_in_his_i
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_snop_year_budget_sum_t('||p_period||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t';
	v_max_version_code varchar(50);  --预测表的最大版本
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月_V1...VN
	v_new_version_code varchar(50);  -- 新生成的版本编码，格式：当前年月_V1...VN
	v_target_version_code  varchar(50);  -- 目标表版本
	v_dml_row_count  number default 0 ;
    v_period varchar(50);        --期次作判断用


begin
	x_success_flag := '1';       --1表示成功

	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as version_code into v_current_max_version_code from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取当前最大版本号


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '产品线年度S&OP预算汇总表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的最大版本编码:'||v_current_max_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


	-- 创建 snop_year_budget_temp1 临时表
	drop table if exists snop_year_budget_temp1;
    create temporary table snop_year_budget_temp1
	(
	 version_code character varying(100),                     --版本
	 month numeric NOT NULL,                                  ---月份
	 period character varying(500),                           ---年度计划期
	 item character varying(500),                             ---物料编码
	 description character varying(2500),                     ---物料描述
	 plan_quantity numeric(38,10),                            ---SNOP月计划量
	 coa_no character varying(500),                           ---COA编码
	 bg_code character varying(100),                           ---BG编码
	 bg_cn_name character varying(1000),                       ---BG中文名
	 lst_lv0_prod_rnd_team_code character varying(50),        ---重量级团队LV0编码
	 lst_lv0_prod_rd_team_cn_name character varying(600),     ---重量级团队LV0中文描述
	 lst_lv0_prod_rd_team_en_name character varying(600),     ---重量级团队LV0英文描述
	 lst_lv1_prod_rnd_team_code character varying(50),        ---重量级团队LV1编码
	 lst_lv1_prod_rd_team_cn_name character varying(600),     ---重量级团队LV1中文描述
	 lst_lv1_prod_rd_team_en_name character varying(600),     ---重量级团队LV1英文描述
	 lst_lv2_prod_rnd_team_code character varying(50),        ---重量级团队LV1编码
	 lst_lv2_prod_rd_team_cn_name character varying(600),     ---重量级团队LV2中文描述
	 lst_lv2_prod_rd_team_en_name character varying(600),     ---重量级团队LV2中文描述
	 lst_lv3_prod_rnd_team_code character varying(50),        ---重量级团队LV3编码
	 lst_lv3_prod_rd_team_cn_name character varying(600),     ---重量级团队LV3中文描述
	 lst_lv3_prod_rd_team_en_name character varying(600),     ---重量级团队LV3英文描述
	 plan_com_lv1 character varying(1000),                     ---一级计委包
	 plan_com_lv2 character varying(1000),                     ---二级计委包
	 plan_com_lv3 character varying(1000),                     ---三级计委包
	 busi_lv4 character varying(1000),                         ---四级计委包
	 port_qty numeric(38,10),                                 ---端口数
	 plan_unit_quantity numeric,                               ---计划单元计划量
	 bg_en_name character varying(1000),                        ---BG英文名称
	 region_code character varying(300),                        ---地区部编码
	 region_cn_name character varying(1000),                    ---地区部中文名称
	 region_en_name character varying(1000),                    ---地区部英文名称
	 rep_office_code character varying(300),                    ---代表处编码
	 rep_office_cn_name character varying(1000),                ---代表处中文名称
	 rep_office_en_name character varying(1000)                 ---代表处英文名称
	) on commit preserve rows distribute by replication;
  
  -- 传入参数非空，且传入参数不能>=系统当前年月（系统当前年月数据不全）
  if((p_period is not null or p_period <> '') and p_period > (to_char(current_date,'yyyymm'))::int) then
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数：'||p_period||'，传入参数不能大于或等于系统当前年月！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
  
  --参数非空
  elseif(p_period is not null or p_period <> '') then

	-- 传入参数格式
    if(length(p_period) <> 6) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数：'||p_period||'，传入参数格式有误，正确格式：yyyymm ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
    end if;
   
   -- 需要判断集成表是否有传入年月数据
   -- 集成表有传入参数的期次数据，则
   if exists(select distinct period from --fin_dm_opt_fop.fop_dwk_snop_year_fc_in_his_i
                                         fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i   --20230731   切换BCM的表
                                   where substring(period,position('-' in period)+1) = p_period)
	 then

		insert into snop_year_budget_temp1
		(
		 version_code,                                      --版本
	     month,												---月份
	     period,                                     		---年度计划期
	     item,                                       		---物料编码
	     description,                                		---物料描述
	     coa_no,                                     		---COA编码
	     bg_code,                                    		---BG编码
	     bg_cn_name,	                                	---BG中文名
	     lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
	     lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
	     lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
	     lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
	     lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
	     lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
	     lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
	     lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
	     lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
	     lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
	     plan_quantity,                              		---SNOP月计划量
	     plan_com_lv1,                                   ---一级计委包
         plan_com_lv2,                                   ---二级计委包
         plan_com_lv3,                                   ---三级计委包
         busi_lv4,                                       ---四级计委包
         port_qty,                                        ---端口数
	     plan_unit_quantity,                ---计划单元计划量
	     bg_en_name,                        ---BG英文名称
	     region_code,                       ---地区部编码
	     region_cn_name,                    ---地区部中文名称
	     region_en_name,                    ---地区部英文名称
	     rep_office_code,                   ---代表处编码
	     rep_office_cn_name,                ---代表处中文名称
	     rep_office_en_name                 ---代表处英文名称
		)
    -- 从集成表取数入到临时表1
		select
		    '' as version_code,                                      --版本
			t1.month,								    		                ---月份
			t1.period,                                  		---年度计划期
			t1.item,                                    		---物料编码
			t1.description,                             		---物料描述
			t1.coa_no,                                  		---COA编码
			t1.bg_code,                                 		---BG编码
			t1.bg_cn_name,	                            		---BG中文名
			t1.lst_lv0_prod_rnd_team_code,	            		---重量级团队LV0编码
			t1.lst_lv0_prod_rd_team_cn_name,	        		  ---重量级团队LV0中文描述
			t1.lst_lv0_prod_rd_team_en_name,	        		  ---重量级团队LV0英文描述
			t1.lst_lv1_prod_rnd_team_code,	            		---重量级团队LV1编码
			t1.lst_lv1_prod_rd_team_cn_name,	        		  ---重量级团队LV1中文描述
			t1.lst_lv1_prod_rd_team_en_name,	        		  ---重量级团队LV1英文描述
			t1.lst_lv2_prod_rnd_team_code,	            		---重量级团队LV1编码
			t1.lst_lv2_prod_rd_team_cn_name,	        		  ---重量级团队LV2中文描述
			t1.lst_lv2_prod_rd_team_en_name,	        		  ---重量级团队LV2中文描述
			t1.lst_lv3_prod_rnd_team_code,	            		---重量级团队LV3编码
			t1.lst_lv3_prod_rd_team_cn_name,	        		  ---重量级团队LV3中文描述
			t1.lst_lv3_prod_rd_team_en_name,	        		  ---重量级团队LV3英文描述
			sum(t1.plan_quantity) as plan_quantity, 		---SNOP月计划量
			t1.plan_com_lv1,                          ---一级计委包
            t1.plan_com_lv2,                          ---二级计委包
            t1.plan_com_lv3,                          ---三级计委包
            t1.attr4 as busi_lv4,                     ---四级计委包
            t1.port_qty,                              ---端口数
	        sum(t1.plan_unit_quantity) as plan_unit_quantity,   ---计划单元计划量
	        t1.bg_en_name,                        ---BG英文名称
	        t1.region_code,                       ---地区部编码
	        t1.region_cn_name,                    ---地区部中文名称
	        t1.region_en_name,                    ---地区部英文名称
	        t1.repoffice_code as rep_office_code,                   ---代表处编码
	        t1.repoffice_cn_name as rep_office_cn_name,                ---代表处中文名称
	        t1.repoffice_en_name as rep_office_en_name                 ---代表处英文名称
		--from fin_dm_opt_fop.fop_dwk_snop_year_fc_in_his_i t1          	---产品线年度s&op预算（原始表）
        from fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i t1         --20230731   切换BCM的表
	     where substring(t1.period,position('-' in t1.period)+1) >= p_period   -- 取传入会计期的所有期次数据
		     and t1.del_flag = 'N'
		     and upper(t1.lst_lv0_prod_rnd_team_code) <> 'SNULL'
		     and upper(t1.region_org_type) = 'HUAWEI'
		   group by
  			 t1.month,
  			 t1.period,
  			 t1.item,
  			 t1.description,
  			 t1.coa_no,
  			 t1.bg_code,
  			 t1.bg_cn_name,
  			 t1.lst_lv0_prod_rnd_team_code,
  			 t1.lst_lv0_prod_rd_team_cn_name,
  			 t1.lst_lv0_prod_rd_team_en_name,
  			 t1.lst_lv1_prod_rnd_team_code,
  			 t1.lst_lv1_prod_rd_team_cn_name,
  			 t1.lst_lv1_prod_rd_team_en_name,
  			 t1.lst_lv2_prod_rnd_team_code,
  			 t1.lst_lv2_prod_rd_team_cn_name,
  			 t1.lst_lv2_prod_rd_team_en_name,
  			 t1.lst_lv3_prod_rnd_team_code,
  			 t1.lst_lv3_prod_rd_team_cn_name,
  			 t1.lst_lv3_prod_rd_team_en_name,
  			 t1.plan_com_lv1,
             t1.plan_com_lv2,
             t1.plan_com_lv3,
             t1.attr4,
             t1.port_qty,
	         t1.bg_en_name,
	         t1.region_code,
	         t1.region_cn_name,
	         t1.region_en_name,
	         t1.repoffice_code,
	         t1.repoffice_cn_name,
	         t1.repoffice_en_name
       ;

	end if
       ;

      v_dml_row_count := sql%rowcount;      -- 收集数据量

   	  -- 记录日志
     perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
           p_log_version_id => null,                 --版本
           p_log_sp_name => v_sp_name,    --sp名称
           p_log_para_list => '',--参数
           p_log_step_num  => 2,
           p_log_cal_log_desc => '传入会计期:'||p_period||',snop_year_budget_temp1 临时表的数据量:'||v_dml_row_count,--日志描述
           p_log_formula_sql_txt => null,--错误信息
           p_log_row_count => v_dml_row_count,
           p_log_errbuf => null  --错误编码
         ) ;


	-- 传入参数为空
  elseif(p_period is null or p_period = '') then

		--- 根据与业务沟通，预算每年只有10月-12月的期次数据
   if (substring(current_date,6,2) in (1,11,12)) then
	 -- 从集成表中取当前年月数据，版本编码赋值空
		insert into snop_year_budget_temp1
		(
		 version_code,                                      --版本
	     month,												---月份
	     period,                                     		---年度计划期
	     item,                                       		---物料编码
	     description,                                		---物料描述
	     coa_no,                                     		---COA编码
	     bg_code,                                    		---BG编码
	     bg_cn_name,	                                	---BG中文名
	     lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
	     lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
	     lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
	     lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
	     lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
	     lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
	     lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
	     lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
	     lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
	     lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
	     plan_quantity,                              		---SNOP月计划量
	     plan_com_lv1,                                   ---一级计委包
         plan_com_lv2,                                   ---二级计委包
         plan_com_lv3,                                   ---三级计委包
         busi_lv4,                                       ---四级计委包
         port_qty,                                        ---端口数
	     plan_unit_quantity,                ---计划单元计划量
	     bg_en_name,                        ---BG英文名称
	     region_code,                       ---地区部编码
	     region_cn_name,                    ---地区部中文名称
	     region_en_name,                    ---地区部英文名称
	     rep_office_code,                   ---代表处编码
	     rep_office_cn_name,                ---代表处中文名称
	     rep_office_en_name                 ---代表处英文名称
		)
		select
		    '' as version_code,                                      --版本
			t1.month,								    		                ---月份
			t1.period,                                  		---年度计划期
			t1.item,                                    		---物料编码
			t1.description,                             		---物料描述
			t1.coa_no,                                  		---COA编码
			t1.bg_code,                                 		---BG编码
			t1.bg_cn_name,	                            		---BG中文名
			t1.lst_lv0_prod_rnd_team_code,	            		---重量级团队LV0编码
			t1.lst_lv0_prod_rd_team_cn_name,	        		  ---重量级团队LV0中文描述
			t1.lst_lv0_prod_rd_team_en_name,	        		  ---重量级团队LV0英文描述
			t1.lst_lv1_prod_rnd_team_code,	            		---重量级团队LV1编码
			t1.lst_lv1_prod_rd_team_cn_name,	        		  ---重量级团队LV1中文描述
			t1.lst_lv1_prod_rd_team_en_name,	        		  ---重量级团队LV1英文描述
			t1.lst_lv2_prod_rnd_team_code,	            		---重量级团队LV1编码
			t1.lst_lv2_prod_rd_team_cn_name,	        		  ---重量级团队LV2中文描述
			t1.lst_lv2_prod_rd_team_en_name,	        		  ---重量级团队LV2中文描述
			t1.lst_lv3_prod_rnd_team_code,	            		---重量级团队LV3编码
			t1.lst_lv3_prod_rd_team_cn_name,	        		  ---重量级团队LV3中文描述
			t1.lst_lv3_prod_rd_team_en_name,	        		  ---重量级团队LV3英文描述
			sum(t1.plan_quantity) as plan_quantity, 		---SNOP月计划量
			t1.plan_com_lv1,                          ---一级计委包
			t1.plan_com_lv2,                          ---二级计委包
			t1.plan_com_lv3,                          ---三级计委包
			t1.attr4 as busi_lv4,                     ---四级计委包
            t1.port_qty,                              ---端口数
	        sum(t1.plan_unit_quantity) as plan_unit_quantity,   ---计划单元计划量
	        t1.bg_en_name,                        ---BG英文名称
	        t1.region_code,                       ---地区部编码
	        t1.region_cn_name,                    ---地区部中文名称
	        t1.region_en_name,                    ---地区部英文名称
	        t1.repoffice_code as rep_office_code,                   ---代表处编码
	        t1.repoffice_cn_name as rep_office_cn_name,                ---代表处中文名称
	        t1.repoffice_en_name as rep_office_en_name                 ---代表处英文名称
		--from fin_dm_opt_fop.fop_dwk_snop_year_fc_in_his_i t1          	---产品线年度s&op预算（原始表）
        from fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i t1   --20230731   切换BCM的表
       where substring(t1.period,position('-' in t1.period)+1) >= to_char(current_date - interval'1month','yyyymm')  -- 取期次年月=当前年月-1的所有期次数据；3月版修改为取期次年月>=当前年月-1的所有期次
		     and t1.del_flag = 'N'
		     and upper(t1.lst_lv0_prod_rnd_team_code) <> 'SNULL'
		     and upper(t1.region_org_type) = 'HUAWEI'
		   group by
  			 t1.month,
  			 t1.period,
  			 t1.item,
  			 t1.description,
  			 t1.coa_no,
  			 t1.bg_code,
  			 t1.bg_cn_name,
  			 t1.lst_lv0_prod_rnd_team_code,
  			 t1.lst_lv0_prod_rd_team_cn_name,
  			 t1.lst_lv0_prod_rd_team_en_name,
  			 t1.lst_lv1_prod_rnd_team_code,
  			 t1.lst_lv1_prod_rd_team_cn_name,
  			 t1.lst_lv1_prod_rd_team_en_name,
  			 t1.lst_lv2_prod_rnd_team_code,
  			 t1.lst_lv2_prod_rd_team_cn_name,
  			 t1.lst_lv2_prod_rd_team_en_name,
  			 t1.lst_lv3_prod_rnd_team_code,
  			 t1.lst_lv3_prod_rd_team_cn_name,
  			 t1.lst_lv3_prod_rd_team_en_name,
  			 t1.plan_com_lv1,
             t1.plan_com_lv2,
             t1.plan_com_lv3,
             t1.attr4,
             t1.port_qty,
	         t1.bg_en_name,
	         t1.region_code,
	         t1.region_cn_name,
	         t1.region_en_name,
	         t1.repoffice_code,
	         t1.repoffice_cn_name,
	         t1.repoffice_en_name
   ;

	  end if
	      ;

    v_dml_row_count := sql%rowcount;   -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入会计期:无,当前年月'||to_char(current_date,'yyyymm')||'的数据,snop_year_budget_temp1 临时表的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  end if;

  --传参时,用预测表的最大版本生成预算表版本号,5张sum表版本号保持一致
   if ((p_period is null or p_period = '') and (substring(current_date,6,2) in (1,11,12))) then

   --预测表的最大版本
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code
	  from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
	 where substr(version_code,1,6) in (select max(substr(version_code,1,6))from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t)
	 group by substr(version_code,1,6); -- 取预测表的最大版本号

		insert into fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t
		(
		 version_code,                                      --版本
	     month,												---月份
	     period,                                     		---年度计划期
	     item,                                       		---物料编码
	     description,                                		---物料描述
	     coa_no,                                     		---COA编码
	     bg_code,                                    		---BG编码
	     bg_cn_name,	                                	---BG中文名
	     lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
	     lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
	     lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
	     lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
	     lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
	     lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
	     lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
	     lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
	     lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
	     lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
	     plan_quantity,                              		---SNOP月计划量
	     plan_com_lv1,                                   ---一级计委包
         plan_com_lv2,                                   ---二级计委包
         plan_com_lv3,                                   ---三级计委包
         busi_lv4,                                       ---四级计委包
         port_qty,                                        ---端口数
	     plan_unit_quantity,                ---计划单元计划量
	     bg_en_name,                        ---BG英文名称
	     region_code,                       ---地区部编码
	     region_cn_name,                    ---地区部中文名称
	     region_en_name,                    ---地区部英文名称
	     rep_office_code,                   ---代表处编码
	     rep_office_cn_name,                ---代表处中文名称
	     rep_office_en_name,                ---代表处英文名称
		 remark,                                     		---备注
		 created_by,                                 		---创建人
		 creation_date,                              		---创建时间
		 last_updated_by,                            		---修改人
		 last_update_date,                           		---修改时间
         del_flag                                    		---是否删除
		)
	  select v_max_version_code as version_code,            --版本
	     month,												---月份
	     period,                                     		---年度计划期
	     item,                                       		---物料编码
	     description,                                		---物料描述
	     coa_no,                                     		---COA编码
	     bg_code,                                    		---BG编码
	     bg_cn_name,	                                	---BG中文名
	     lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
	     lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
	     lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
	     lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
	     lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
	     lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
	     lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
	     lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
	     lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
	     lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
	     lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
	     plan_quantity,                              		---SNOP月计划量
	     plan_com_lv1,                                   ---一级计委包
         plan_com_lv2,                                   ---二级计委包
         plan_com_lv3,                                   ---三级计委包
         busi_lv4,                                       ---四级计委包
         port_qty,                                        ---端口数
	     plan_unit_quantity,                ---计划单元计划量
	     bg_en_name,                        ---BG英文名称
	     region_code,                       ---地区部编码
	     region_cn_name,                    ---地区部中文名称
	     region_en_name,                    ---地区部英文名称
	     rep_office_code,                   ---代表处编码
	     rep_office_cn_name,                ---代表处中文名称
	     rep_office_en_name,                 ---代表处英文名称
	     '' as remark,
	  	 -1 as created_by,
	  	 current_timestamp as creation_date,
	  	 -1 as last_updated_by,
	  	 current_timestamp as last_update_date,
	  	 'N' as del_flag
    from snop_year_budget_temp1
	;

	v_dml_row_count := sql%rowcount;   -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '取的最大版本编码:'||v_max_version_code||',dm_fop_snop_year_budget_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


  elseif(p_period is not null or p_period <> '') then
    -- 如果集成表有传入年月的数据，则生成版本数据
    if exists(select period from snop_year_budget_temp1) then
      if exists(select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) 
                  from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t 
                 where substr(version_code,1,6) = to_char(to_date(p_period,'yyyymmdd') + interval'1 month','yyyymm')
                 group by substr(version_code,1,6)  
               ) then
        
        -- 如果目标表已存在传入参数+1的版本，则取（传入参数+1的版本）的最大版本
        select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric)  as version_code into v_target_version_code 
          from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t 
         where substr(version_code,1,6) = to_char(to_date(p_period,'yyyymmdd') + interval'1 month','yyyymm')
         group by substr(version_code,1,6) 
        ;
        
        -- 为了保证5张SUM表的版本统一，所以S&OP预测不能新增其它SUM表没有的版本
        delete from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t where version_code = v_target_version_code;
        
        -- 已存在传入参数+1的版本，则（传入参数+1的版本）+1，数据入到目标表
		    insert into fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t(
		           version_code,                                      --版本
               month,												---月份
               period,                                     		---年度计划期
               item,                                       		---物料编码
               description,                                		---物料描述
               coa_no,                                     		---COA编码
               bg_code,                                    		---BG编码
               bg_cn_name,	                                	---BG中文名
               lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
               lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
               lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
               lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
               lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
               lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
               lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
               lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
               lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
               lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
               lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
               lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
               plan_quantity,                              		---SNOP月计划量
               plan_com_lv1,                                   ---一级计委包
               plan_com_lv2,                                   ---二级计委包
               plan_com_lv3,                                   ---三级计委包
               busi_lv4,                                       ---四级计委包
               port_qty,                                        ---端口数
               plan_unit_quantity,                ---计划单元计划量
               bg_en_name,                        ---BG英文名称
               region_code,                       ---地区部编码
               region_cn_name,                    ---地区部中文名称
               region_en_name,                    ---地区部英文名称
               rep_office_code,                   ---代表处编码
               rep_office_cn_name,                ---代表处中文名称
               rep_office_en_name,                 ---代表处英文名称
               remark,                                     		---备注
               created_by,                                 		---创建人
               creation_date,                              		---创建时间
               last_updated_by,                            		---修改人
               last_update_date,                           		---修改时间
               del_flag                                    		---是否删除
		    )
	      select v_target_version_code as version_code,
               month,												---月份
               period,                                     		---年度计划期
               item,                                       		---物料编码
               description,                                		---物料描述
               coa_no,                                     		---COA编码
               bg_code,                                    		---BG编码
               bg_cn_name,	                                	---BG中文名
               lst_lv0_prod_rnd_team_code,	                	---重量级团队LV0编码
               lst_lv0_prod_rd_team_cn_name,               		---重量级团队LV0中文描述
               lst_lv0_prod_rd_team_en_name,               		---重量级团队LV0英文描述
               lst_lv1_prod_rnd_team_code,	                	---重量级团队LV1编码
               lst_lv1_prod_rd_team_cn_name,               		---重量级团队LV1中文描述
               lst_lv1_prod_rd_team_en_name,               		---重量级团队LV1英文描述
               lst_lv2_prod_rnd_team_code,	                	---重量级团队LV1编码
               lst_lv2_prod_rd_team_cn_name,               		---重量级团队LV2中文描述
               lst_lv2_prod_rd_team_en_name,               		---重量级团队LV2中文描述
               lst_lv3_prod_rnd_team_code,	                	---重量级团队LV3编码
               lst_lv3_prod_rd_team_cn_name,               		---重量级团队LV3中文描述
               lst_lv3_prod_rd_team_en_name,               		---重量级团队LV3英文描述
               plan_quantity,                              		---SNOP月计划量
               plan_com_lv1,                                   ---一级计委包
               plan_com_lv2,                                   ---二级计委包
               plan_com_lv3,                                   ---三级计委包
               busi_lv4,                                       ---四级计委包
               port_qty,                                        ---端口数
               plan_unit_quantity,                ---计划单元计划量
               bg_en_name,                        ---BG英文名称
               region_code,                       ---地区部编码
               region_cn_name,                    ---地区部中文名称
               region_en_name,                    ---地区部英文名称
               rep_office_code,                   ---代表处编码
               rep_office_cn_name,                ---代表处中文名称
               rep_office_en_name,                 ---代表处英文名称
               '' as remark,
               -1 as created_by,
               current_timestamp as creation_date,
               -1 as last_updated_by,
               current_timestamp as last_update_date,
               'N' as del_flag
          from snop_year_budget_temp1
	      ;

	      v_dml_row_count := sql%rowcount;      -- 收集数据量

        -- 写结束日志
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => '传入参数：'||p_period||'，目标表中已存在传入参数+1的版本，生成的版本：'||v_target_version_code||'， dm_fop_snop_year_budget_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
      
      else
      
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 3,
          p_log_cal_log_desc => 'S&OP预测没有传入参数+1版本的数据，需要重新传入参数 ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	    return;
       
      end if;
   
   else
     perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入参数：'||p_period||'，集成表没有传入年月数据！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
   
   end if;
 end if;


--处理异常信息
exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
		p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		p_log_row_count => null,
        p_log_errbuf => sqlstate  --错误编码
        ) ;
	x_success_flag := '2001';       --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

