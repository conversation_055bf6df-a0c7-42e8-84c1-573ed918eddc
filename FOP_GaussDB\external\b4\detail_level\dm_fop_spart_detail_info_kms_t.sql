-- Step 2：数据提取
标签名称：coa_l1_tmp   数据源：fin_dm_opt_fop_uat
select distinct lv1_code
     , l1_name
     , coa_code
     , l2_name
  from fin_dm_opt_fop.apd_fop_coa_l1_t
 where del_flag = 'N'
   and upper(status) = 'SUBMIT'
;

标签名称：plan_com_l2_rel_tmp   数据源：fin_dm_opt_fop_uat
select distinct plan_com_lv1
     , plan_com_lv2
     , plan_com_lv3
     , busi_lv4
     , l1_name
     , l2_name
     , l2_coefficient
  from fin_dm_opt_fop.apd_fop_plan_com_l2_rel_t
 where del_flag = 'N'
   and upper(status) = 'SUBMIT'
;

标签名称：ict_fcst_holistic_view_tmp   数据源：fin_dm_opt_fop_uat
select distinct lv1_code
     , l1_name
     , articulation_flag
     , analysis_flag
  from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t
 where del_flag = 'N'
   and upper(status) = 'SUBMIT'
;

标签名称：spart_profiting_relation_tmp1   数据源：fin_dm_opt_fop_uat
select distinct period_id
     , item_code
     , l1_name
     , l2_name
     , l3_name
     , l1_coefficient
     , l2_coefficient
     , l3_coefficient
     , data_type    /*数据类型（His 历史、Add 新增）*/
     , update_flag
  from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
 where del_flag = 'N'
   and upper(status) = 'SUBMIT'  /*状态（Save 保存、Submit 提交）*/
   and period_id = ${V_PROFITING_RELATION_PERIOD_ID}
;

标签名称：spart_detail_l1_info_all_tmp   数据源：fin_dm_opt_fop_uat
select version_code
     , period_id
     , spart_code
     , spart_desc
     , prod_key
     , prod_code
     , prod_cn_name
     , bg_code
     , bg_name
     , bg_en_name
     , lv0_prod_rnd_team_code
     , lv0_prod_rd_team_cn_name
     , lv0_prod_rd_team_en_name
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv1_prod_rd_team_en_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , lv2_prod_rd_team_en_name
     , lv3_prod_rnd_team_code
     , lv3_prod_rd_team_cn_name
     , lv3_prod_rd_team_en_name
     , plan_com_lv1
     , plan_com_lv2
     , plan_com_lv3
     , busi_lv4
     , geo_pc_key
     , oversea_flag
     , l1_name
     , phase_date
     , unit
     , source_table
     , industry_type
     , spart_qty
     , ship_qty
     , snop_quantity
     , snop_plan_quantity
     , rmb_revenue
     , usd_revenue
     , rmb_cost
     , usd_cost
     , equip_rev_rmb_amt
     , equip_rev_usd_amt
     , equip_cost_rmb_amt
     , equip_cost_usd_amt
     , plan_unit_quantity
  from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_kms_t
 where del_flag = 'N'
   and version_code = '${V_DETAIL_L1_MAX_VERSION_CODE}'
   ;
   
-- Step 3：SQL-Script
cache table spart_profiting_relation_tmp2
as
select cast(period_id as int) as period_id
     , cast((case when length(item_code) < 8 then lpad(item_code,8,'0') else item_code end) as varchar(50)) as item_code
     , l1_name
     , l2_name
     , l3_name
     , l1_coefficient
     , l2_coefficient
     , l3_coefficient
     , data_type
     , update_flag
  from spart_profiting_relation_tmp1
;

cache table spart_profiting_relation_tmp
as
select item_code
     , l1_name
     , l2_name
     , l3_name
     , l1_coefficient
     , l2_coefficient
     , l3_coefficient
     , data_type
     , update_flag
     , row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
  from spart_profiting_relation_tmp2
;

cache table plan_com_l2_rel_tmp2
as
select distinct plan_com_lv1
     , plan_com_lv2
     , plan_com_lv3
     , busi_lv4
     , l2_name
     , l2_coefficient
  from plan_com_l2_rel_tmp
;

cache table spart_detail_l1_info_tmp
as
select t1.version_code
     , cast(t1.period_id as int) as period_id
     , t1.spart_code
     , t1.spart_desc
     , cast(t1.prod_key as numeric(20,0)) as prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , cast(t1.geo_pc_key as numeric(20,0)) as geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.phase_date
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , sum(coalesce(cast(t1.spart_qty as numeric(38,10)),0)) as spart_qty
     , sum(coalesce(cast(t1.ship_qty as numeric(38,10)),0)) as ship_qty
     , sum(coalesce(cast(t1.snop_quantity as numeric(38,10)),0)) as snop_quantity
     , sum(coalesce(cast(t1.snop_plan_quantity as numeric(38,10)),0)) as snop_plan_quantity
     , sum(coalesce(t1.rmb_revenue,0)) as rmb_revenue
     , sum(coalesce(t1.usd_revenue,0)) as usd_revenue
     , sum(coalesce(t1.rmb_cost,0)) as rmb_cost
     , sum(coalesce(t1.usd_cost,0)) as usd_cost
     , sum(coalesce(t1.equip_rev_rmb_amt,0)) as  equip_rev_rmb_amt
     , sum(coalesce(t1.equip_rev_usd_amt,0)) as  equip_rev_usd_amt
     , sum(coalesce(t1.equip_cost_rmb_amt,0)) as equip_cost_rmb_amt
     , sum(coalesce(t1.equip_cost_usd_amt,0)) as equip_cost_usd_amt
     , sum(coalesce(cast(t1.plan_unit_quantity as numeric(38,10)),0)) as plan_unit_quantity
  from spart_detail_l1_info_all_tmp t1
 group by t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.phase_date
     , t1.unit
     , t1.source_table
     , t1.industry_type
;

cache table spart_fop_tmp
as
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , (case when t1.industry_type = 'OTHR' then 'SCENO3' else t3.articulation_flag end) as articulation_flag  /*勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三*/
     , t3.analysis_flag   /* 分析场景（使用或提个给知识表示时，都只用“是”的数据）*/
     , t1.phase_date
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , t2.l2_name
     , t2.l3_name
     , t2.l1_coefficient
     , t2.l2_coefficient
     , t2.l3_coefficient
     , (case when t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t' then ''  /* ICT损益没有spart_code，所以不用打L2*/
             when t2.l2_name is null then 'New'
             when upper(t2.data_type) = 'ADD' and upper(t2.update_flag) = 'N' then 'AI'
             when upper(t2.update_flag) = 'Y' then 'Adjust'
	           else 'Manual'
	      end) as data_type
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.plan_unit_quantity
  from spart_detail_l1_info_tmp t1
  left join spart_profiting_relation_tmp t2
    on t1.spart_code = t2.item_code
   and t1.l1_name = t2.l1_name
   and t2.rn = 1
  left join ict_fcst_holistic_view_tmp t3  /*关联产业维表取“勾稽方法标签”*/
    on ((t1.lv1_prod_rnd_team_code = t3.lv1_code and t3.l1_name is null) or (t1.l1_name = t3.l1_name and t3.l1_name is not null))
 where t1.industry_type is not null  /* L1打完标签后，如果还有空值，需要剔除；但是 industry_type='OTHR'不能剔除*/
   and t1.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  /*排除SNOP的两张表*/
;


/*通过LV1、L1、COA关联维表，取L2值*/
cache table spart_fop_l2_tmp
as
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.articulation_flag
     , t1.analysis_flag
     , t1.phase_date
     , t1.plan_unit_quantity
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , t1.l2_name
     , '' as coa_l2_name
     , t1.l3_name
     , t1.l1_coefficient
     , t1.l2_coefficient
     , t1.l3_coefficient
     , t1.data_type
  from spart_fop_tmp t1
 where nvl(t1.l1_name,'SNULL') not in(select distinct l1_name from coa_l1_tmp)  /*l1_name不在COA维表里的，还是用spart对象系数关系表的l2_name*/
union all
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.articulation_flag
     , t1.analysis_flag
     , t1.phase_date
     , t1.plan_unit_quantity
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , t1.l2_name
     , (case when t2.l2_name is not null then t2.l2_name
             else '其他'
        end) as coa_l2_name
     , t1.l3_name
     , t1.l1_coefficient
     , t1.l2_coefficient
     , t1.l3_coefficient
     , t1.data_type
  from spart_fop_tmp t1
  left join coa_l1_tmp t2
    on t1.lv1_prod_rnd_team_code = t2.lv1_code
   and t1.l1_name = t2.l1_name
   and t1.prod_code = t2.coa_code
 where nvl(t1.l1_name,'SNULL') in(select distinct l1_name from coa_l1_tmp) /*l1_name在COA维表里的，prod_code=coa_code 取COA维表的l1_name；否则l1_name='其他'*/
;

/*【SNOP的两张表】关联“L2与计委包的关系”表取L2_name、L2系数：1、根据LV1~LV4+L1关联匹配L2_name；2、如果L2_name是空值，则根据LV1~LV3+L1匹配；3、如果L2_name是空值，则根据LV1~LV2+L1匹配；*/
cache table snop_l2_tmp1
as
/*1、根据LV1~LV4+L1，关联“L2与计委包的关系”表取L2_name、L2系数*/
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , (case when t1.industry_type = 'OTHR' then 'SCENO3' else t3.articulation_flag end) as articulation_flag  /*勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三*/
     , t3.analysis_flag   /* 分析场景（使用或提个给知识表示时，都只用“是”的数据）*/
     , t1.phase_date
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.plan_unit_quantity
  from spart_detail_l1_info_tmp t1
  left join ict_fcst_holistic_view_tmp t3
    on nvl(t1.l1_name,'SNULL') = nvl(t3.l1_name,'SNULL')
 where t1.industry_type is not null  /*L1打完标签后，如果还有空值，需要剔除；但是 industry_type='OTHR'不能剔除*/
   and t1.source_table in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')
;

cache table snop_l2_tmp2
as
/*1、根据LV1~LV3+L1，关联“L2与计委包的关系”表取L2_name、L2系数*/
select distinct t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.articulation_flag  /* 勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三*/
     , t1.analysis_flag
     , t1.phase_date
     , t1.unit
     , t1.source_table
     , t1.industry_type
     , t2.l2_name
     , t2.l2_coefficient
     , '' as data_type   /*数据类型（关注的是收入、发货，所以SNOP的可以给空值）*/
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.plan_unit_quantity
  from snop_l2_tmp1 t1
  left join plan_com_l2_rel_tmp2 t2
    on ((t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t1.plan_com_lv3 = t2.plan_com_lv3
   and t1.busi_lv4 = t2.busi_lv4)
    or (t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t1.plan_com_lv3 = t2.plan_com_lv3
   and t2.busi_lv4 is null)
    or (t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t2.plan_com_lv3 is null
   and t2.busi_lv4 is null))
;

cache table spart_detail_info_tmp
as
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t1.l1_name
     , t1.l2_name
     , t1.coa_l2_name
     , t1.l3_name
     , (case when t1.l2_name is not null and t1.l1_coefficient is null then 0 else t1.l1_coefficient end) as l1_coefficient
     , t1.l2_coefficient
     , t1.l3_coefficient
     , t1.data_type
     , t1.articulation_flag
     , t1.analysis_flag
     , t1.phase_date
     , t1.plan_unit_quantity
     , t1.unit
     , t1.source_table
     , t1.industry_type
  from spart_fop_l2_tmp t1
union all
select t1.version_code
     , t1.period_id
     , t1.spart_code
     , t1.spart_desc
     , t1.spart_qty
     , t1.ship_qty
     , t1.snop_quantity
     , t1.snop_plan_quantity
     , t1.rmb_revenue
     , t1.usd_revenue
     , t1.rmb_cost
     , t1.usd_cost
     , t1.equip_rev_rmb_amt
     , t1.equip_rev_usd_amt
     , t1.equip_cost_rmb_amt
     , t1.equip_cost_usd_amt
     , t1.prod_key
     , t1.prod_code
     , t1.prod_cn_name
     , t1.bg_code
     , t1.bg_name
     , t1.bg_en_name
     , t1.lv0_prod_rnd_team_code
     , t1.lv0_prod_rd_team_cn_name
     , t1.lv0_prod_rd_team_en_name
     , t1.lv1_prod_rnd_team_code
     , t1.lv1_prod_rd_team_cn_name
     , t1.lv1_prod_rd_team_en_name
     , t1.lv2_prod_rnd_team_code
     , t1.lv2_prod_rd_team_cn_name
     , t1.lv2_prod_rd_team_en_name
     , t1.lv3_prod_rnd_team_code
     , t1.lv3_prod_rd_team_cn_name
     , t1.lv3_prod_rd_team_en_name
     , t1.plan_com_lv1
     , t1.plan_com_lv2
     , t1.plan_com_lv3
     , t1.busi_lv4
     , t1.geo_pc_key
     , t1.oversea_flag
     , t2.l1_name   /*24年9月版  S&OP的 l1_name 取计委包的*/
     , t1.l2_name
     , '' as coa_l2_name
     , '' as l3_name
     , 0 as l1_coefficient
     , t1.l2_coefficient
     , 0 as l3_coefficient
     , t1.data_type
     , t1.articulation_flag
     , t1.analysis_flag
     , t1.phase_date
     , t1.plan_unit_quantity
     , t1.unit
     , t1.source_table
     , t1.industry_type
  from snop_l2_tmp2 t1
  left join plan_com_l2_rel_tmp t2
    on ((t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t1.plan_com_lv3 = t2.plan_com_lv3
   and t1.busi_lv4 = t2.busi_lv4)
    or (t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t1.plan_com_lv3 = t2.plan_com_lv3
   and t2.busi_lv4 is null)
    or (t1.plan_com_lv1 = t2.plan_com_lv1
   and t1.plan_com_lv2 = t2.plan_com_lv2
   and t2.plan_com_lv3 is null
   and t2.busi_lv4 is null))
;

select version_code
     , period_id
     , (case when spart_code is null then '00000000' else spart_code end) as spart_code
     , spart_desc
     , spart_qty
     , ship_qty
     , snop_quantity
     , snop_plan_quantity
     , rmb_revenue
     , usd_revenue
     , rmb_cost
     , usd_cost
     , equip_rev_rmb_amt
     , equip_rev_usd_amt
     , equip_cost_rmb_amt
     , equip_cost_usd_amt
     , prod_key
     , prod_code
     , prod_cn_name
     , bg_code
     , bg_name
     , bg_en_name
     , lv0_prod_rnd_team_code
     , lv0_prod_rd_team_cn_name
     , lv0_prod_rd_team_en_name
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv1_prod_rd_team_en_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , lv2_prod_rd_team_en_name
     , lv3_prod_rnd_team_code
     , lv3_prod_rd_team_cn_name
     , lv3_prod_rd_team_en_name
     , plan_com_lv1
     , plan_com_lv2
     , plan_com_lv3
     , busi_lv4
     , geo_pc_key
     , oversea_flag
     , l1_name
     , l2_name
     , coa_l2_name
     , l3_name
     , l1_coefficient
     , l2_coefficient
     , l3_coefficient
     , data_type
     , articulation_flag
     , analysis_flag
     , phase_date
     , plan_unit_quantity
     , unit
     , source_table
     , industry_type
     , '' as remark
     , -1 as created_by
     , CURRENT_TIMESTAMP as creation_date
     , -1 as last_updated_by
     , CURRENT_TIMESTAMP as last_update_date
     , 'N' as del_flag
  from spart_detail_info_tmp
;

-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                  目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_detail_info_kms_t   模式：DELETE
                                            删除条件：version_code = '${V_DETAIL_L1_MAX_VERSION_CODE}'