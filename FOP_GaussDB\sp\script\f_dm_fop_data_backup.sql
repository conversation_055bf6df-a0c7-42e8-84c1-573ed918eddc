-- ----------------------------
-- Function structure for f_dm_fop_data_backup
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_data_backup"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_data_backup"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/***********************************************************************************************************************************************************************
创建时间：2023-10-11
创建人  ：朱雅欣 zwx1275798
背景描述：数据备份函数，每月6号自动备份集成表，以便以后能调度恢复
参数描述：
		  参数一(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_data_backup()
	  
************************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(200) := 'fin_dm_opt_fop.f_dm_fop_data_backup';
	v_tbl_name varchar(200) := 'fin_dm_opt_fop.f_dm_fop_data_backup';
	v_dml_row_count number default 0 ;
    v_period varchar(50);
	v_sql varchar(4000);
	
begin
	x_success_flag := '1';

   
	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '数据备份'||v_tbl_name||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


 /*-- 判断确定period值
  --select nvl(p_period,to_char(sysdate,'YYYYMMDD')) into v_period;*/
  
  select to_char(sysdate,'YYYYMMDDHH24MI') into v_period;
  
  
  -- 动态SQL执行删表和备份
  v_sql := 'drop table if exists fin_dm_opt_fop.fop_dwl_prod_spart_ship_dtl_i_'||v_period||'bak;
  drop table if exists fin_dm_opt_fop.fop_dwl_prod_prod_unit_kms_i_'||v_period||'bak;    
  drop table if exists fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_'||v_period||'bak;    
  drop table if exists fin_dm_opt_fop.fop_dwk_grp_pln_pub_snop_product_i_'||v_period||'bak; 
  drop table if exists fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i_'||v_period||'bak;    
  drop table if exists fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v_'||v_period||'bak;
  drop table if exists fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i'||v_period||'bak;
  create table fin_dm_opt_fop.fop_dwl_prod_spart_ship_dtl_i_'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwl_prod_spart_ship_dtl_i;  
  create table fin_dm_opt_fop.fop_dwl_prod_prod_unit_kms_i'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i;  
  create table fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i;  
  create table fin_dm_opt_fop.fop_dwk_grp_pln_pub_snop_product_i_'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwk_grp_pln_pub_snop_product_i;  
  create table fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i_'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwk_grp_snop_year_fc_in_his_i;  
  create table fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v_'||v_period||'bak as select * from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v;
  create table fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i'||v_period||'bak as select * from fin_dm_opt_fop.fop_dwk_grp_sop_act_fcst_qty_i';  
  
  execute v_sql;
  
  

-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
		p_log_cal_log_desc =>'数据备份'||v_tbl_name||':取数据量:'||v_dml_row_count||'结束运行',--日志描述		
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_row_count => null,
			p_log_errbuf => sqlstate  --错误编码
			) ;
	x_success_flag := '2001';
	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

