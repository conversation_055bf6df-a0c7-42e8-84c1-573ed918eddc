-- ----------------------------
-- Function structure for f_dm_fop_data_to_java
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_data_to_java"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_data_to_java"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-05-08
创建人  ：鲁广武
背景描述：维度监控页面，JAVA端用到的表，调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_data_to_java()
变更记录：2024年1月版新增S&OP预测数据

*/


declare
	v_sp_name varchar(50) := 'fin_dm_opt_fop.f_dm_fop_data_to_java';
	v_tbl_name1 varchar(100) := 'fin_dm_opt_fop.dm_fop_ict_pl_sum_to_java';
	v_tbl_name2 varchar(100) := 'fin_dm_opt_fop.dm_fop_snop_sum_to_java';
	v_dml_row_count number default 0 ;
  v_max_version_code varchar(100);
  

begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功


	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '维度监控（JAVA）'||v_tbl_name1||','||v_tbl_name2||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

        ---清空目标表
		truncate table fin_dm_opt_fop.dm_fop_ict_pl_sum_to_java;    --ict损益表（JAVA）

		--插入目标表数据
		insert into fin_dm_opt_fop.dm_fop_ict_pl_sum_to_java     --ict损益表（JAVA）
		(
	     version_code
        ,year
	    ,prod_code
	    ,prod_cn_name
	    ,prod_en_name
	    ,lv0_prod_rnd_team_code
	    ,lv0_prod_rd_team_cn_name
	    ,lv0_prod_rd_team_en_name
	    ,lv1_prod_rnd_team_code
	    ,lv1_prod_rd_team_cn_name
	    ,lv1_prod_rd_team_en_name
	    ,lv2_prod_rnd_team_code
	    ,lv2_prod_rd_team_cn_name
	    ,lv2_prod_rd_team_en_name
	    ,lv3_prod_rnd_team_code
	    ,lv3_prod_rd_team_cn_name
	    ,lv3_prod_rd_team_en_name
	    ,created_by
	    ,creation_date
	    ,last_updated_by
	    ,last_update_date
	    ,del_flag
		)
		select distinct
	     version_code
		,substr(period_id,1,4) as year
	    ,prod_code
	    ,prod_cn_name
	    ,prod_en_name
	    ,lv0_prod_rnd_team_code
	    ,lv0_prod_rd_team_cn_name
	    ,lv0_prod_rd_team_en_name
	    ,lv1_prod_rnd_team_code
	    ,lv1_prod_rd_team_cn_name
	    ,lv1_prod_rd_team_en_name
	    ,lv2_prod_rnd_team_code
	    ,lv2_prod_rd_team_cn_name
	    ,lv2_prod_rd_team_en_name
	    ,lv3_prod_rnd_team_code
	    ,lv3_prod_rd_team_cn_name
	    ,lv3_prod_rd_team_en_name
		,created_by
		,creation_date
		,last_updated_by
		,last_update_date
		,del_flag
	from fin_dm_opt_fop.dm_fop_ict_pl_sum_t       ---ict损益表
	  ;

	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写入日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '维度监控（JAVA）'||v_tbl_name1||':数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    ---清空目标表
		truncate table fin_dm_opt_fop.dm_fop_snop_sum_to_java;    --snop预测表（JAVA）
		
		-- 获取预测表的最大版本
		select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
      from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t
     where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t)
     group by substr(version_code,1,6)
	  ;

		--插入目标表数据
		insert into fin_dm_opt_fop.dm_fop_snop_sum_to_java  --snop预测表（JAVA）
		(
	     version_code
	    ,lst_lv0_prod_rnd_team_code
	    ,lst_lv0_prod_rd_team_cn_name
	    ,lst_lv0_prod_rd_team_en_name
	    ,lst_lv1_prod_rnd_team_code
	    ,lst_lv1_prod_rd_team_cn_name
	    ,lst_lv1_prod_rd_team_en_name
	    ,lst_lv2_prod_rnd_team_code
	    ,lst_lv2_prod_rd_team_cn_name
	    ,lst_lv2_prod_rd_team_en_name
	    ,lst_lv3_prod_rnd_team_code
	    ,lst_lv3_prod_rd_team_cn_name
	    ,lst_lv3_prod_rd_team_en_name
	    ,plan_com_lv1
	    ,plan_com_lv2
	    ,plan_com_lv3
	    ,busi_lv4
		  ,phase_date
		  ,phase_date_type	  -- 期次类型（预算、预测）
		  ,oversea_desc
	    ,created_by
	    ,creation_date
	    ,last_updated_by
	    ,last_update_date
	    ,del_flag
		)
		select distinct
	     version_code
	    ,lst_lv0_prod_rnd_team_code
	    ,lst_lv0_prod_rd_team_cn_name
	    ,lst_lv0_prod_rd_team_en_name
	    ,lst_lv1_prod_rnd_team_code
	    ,lst_lv1_prod_rd_team_cn_name
	    ,lst_lv1_prod_rd_team_en_name
	    ,lst_lv2_prod_rnd_team_code
	    ,lst_lv2_prod_rd_team_cn_name
	    ,lst_lv2_prod_rd_team_en_name
	    ,lst_lv3_prod_rnd_team_code
	    ,lst_lv3_prod_rd_team_cn_name
	    ,lst_lv3_prod_rd_team_en_name
	    ,plan_com_lv1
	    ,plan_com_lv2
	    ,plan_com_lv3
	    ,busi_lv4
		  ,phase_date
		  ,'预测' as phase_date_type
		  ,oversea_desc
		  ,created_by
		  ,creation_date
		  ,last_updated_by
		  ,last_update_date
		  ,del_flag
	from fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t         ---snop预测表
	where phase_date <= substr(v_max_version_code,1,6)||'06'
    and oversea_desc in('全球','国内','海外')
	union all
	select distinct version_code
       , lst_lv0_prod_rnd_team_code
       , lst_lv0_prod_rd_team_cn_name
       , lst_lv0_prod_rd_team_en_name
       , lst_lv1_prod_rnd_team_code
       , lst_lv1_prod_rd_team_cn_name
       , lst_lv1_prod_rd_team_en_name
       , lst_lv2_prod_rnd_team_code
       , lst_lv2_prod_rd_team_cn_name
       , lst_lv2_prod_rd_team_en_name
       , lst_lv3_prod_rnd_team_code
       , lst_lv3_prod_rd_team_cn_name
       , lst_lv3_prod_rd_team_en_name
       , plan_com_lv1
       , plan_com_lv2
       , plan_com_lv3
       , busi_lv4
       , period as phase_date
       , '预算' as phase_date_type
       , '全球'::varchar(100) as oversea_desc
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
	  from fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t         ---snop预算表
	;

	v_dml_row_count := nvl(sql%rowcount,0);	-- 收集数据量

  -- 写结束日志
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '维度监控（JAVA）'||v_tbl_name2||':数据量:'||v_dml_row_count||'结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

    --收集统计信息
    analyse fin_dm_opt_fop.dm_fop_ict_pl_sum_to_java;
	analyse fin_dm_opt_fop.dm_fop_snop_sum_to_java;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

