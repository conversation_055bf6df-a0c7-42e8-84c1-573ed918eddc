-- ----------------------------
-- Table structure for fop_dwl_prod_prod_unit_kms_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_kms_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_kms_i" (
  "period_id" numeric,
  "spart_code" varchar(200) COLLATE "pg_catalog"."default",
  "p_flag" varchar(15) COLLATE "pg_catalog"."default",
  "scenario" varchar(20) COLLATE "pg_catalog"."default",
  "prod_key" varchar(50) COLLATE "pg_catalog"."default",
  "prod_code" varchar(50) COLLATE "pg_catalog"."default",
  "geo_pc_key" numeric,
  "dimension_key" numeric,
  "part_qty" numeric,
  "rmb_fact_rate_amt" varchar(5000) COLLATE "pg_catalog"."default",
  "usd_fact_rate_amt" varchar(5000) COLLATE "pg_catalog"."default"
)
;

