-- ----------------------------
-- Function structure for f_dm_fop_prod_prod_unit_sum_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_prod_prod_unit_sum_t"("p_schedule" varchar, "p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_prod_prod_unit_sum_t"(IN "p_schedule" varchar=NULL::character varying, IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/***********************************************************************************************************************************************************************
创建时间：2022-10-25
创建人  ：陈芖 cwx1182800
更新时间：2023-02-01
更新人  ：鲁广武  lwx1186472
背景描述：收入时点全量part量本价明细表关联维表按照收敛逻辑加工入到收入时点S-Part量本价汇总表中
参数描述：参数一(p_schedule)：调度类型 增量(I) 或 全量(F)
		  参数二(p_period)：传入会计期(年月)
		  参数三(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_prod_prod_unit_sum_t()
修改记录：2023-02-01 lwx1186472   新增会计期参数，新增版本字段
          调度类型+传入会计期，有值：1、增量（从集成表取上月数据）+ 最大版本的数据（所有数据关联维度表刷新）；传入值格式：f_dm_fop_prod_prod_unit_sum_t('I',202210)
                                     2、全量（从集成表取period_id<传入会计期的数据）；传入值格式：f_dm_fop_prod_prod_unit_sum_t('F',202210)					  
          调度类型+传入会计期，无值：取集成表period_id<当前年月的数据；如:period_id<202302；传入值格式：f_dm_fop_prod_prod_unit_sum_t()
		  2023-04-19 lwx1186472   新增字段;修改取最大版本逻辑
		  2023-06-19 lwx1186472   新增物料维表逻辑,取spart_desc的值(7月版)；上游集成表20230720切换BCM的表,表名不变,uat环境临时用fop_dwl_prod_prod_unit_i_bcm		  
************************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_prod_prod_unit_sum_t('''||p_schedule||''','||p_period||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t';
	v_max_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月_V1...VN
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月_V1...VN
	v_new_version_code varchar(50);  -- 新生成的版本编码，格式：当前年月_V1...VN
	v_dml_row_count  number default 0 ;
	v_count          number default 0;  -- 根据传入会计期取来源表的数据	

	
begin
	x_success_flag := '1';

    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as version_code into v_max_version_code 
	  from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t
	 where substr(version_code,1,6) in (select max(substr(version_code,1,6))from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t)
	 group by substr(version_code,1,6);  -- 取最大版本号	
	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as version_code into v_current_max_version_code from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取当前日期的最大版本号
	
	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '收入时点S-Part量本价汇总表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的最大版本编码:'||v_current_max_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	-- 创建 prod_unit_tmp1 临时表，取最大版本的数据或者没有版本的全量数据
	drop table if exists prod_unit_tmp1;
    create temporary table prod_unit_tmp1
	(
	 version_code character varying(100),                    --版本
	 period_id numeric NOT NULL,                             --会计期
	 spart_code character varying(150),						 --part编码
	 spart_desc character varying(2000),                     --part描述
	 spart_qty numeric(38,10),								 --物料数量
	 rmb_revenue numeric(38,10),							 --spart收入金额_人民币
	 usd_revenue numeric(38,10),                             --spart收入金额_美金
	 rmb_cost numeric(38,10),								 --bpart成本金额_人民币(绝密)
	 usd_cost numeric(38,10),                                --bpart成本金额_美金(绝密)
	 prod_key numeric,									     --产品key
	 prod_code character varying(50),                        --产品编码
	 prod_cn_name character varying(600),                    --产品名称
	 bg_code character varying(50),                          --bg编码
	 bg_name character varying(200),                         --bg名称
	 lv0_prod_rnd_team_code character varying(50),			 --重量级团队lv0编码
	 lv0_prod_rd_team_cn_name character varying(600),        --重量级团队LV0中文描述
	 lv0_prod_rd_team_en_name character varying(600),        --重量级团队LV0英文描述
	 lv1_prod_rnd_team_code character varying(50),           --重量级团队lv1编码
	 lv1_prod_rd_team_cn_name character varying(600),        --重量级团队LV1中文描述
	 lv1_prod_rd_team_en_name character varying(600),        --重量级团队LV1英文描述
	 lv2_prod_rnd_team_code character varying(50),			 --重量级团队lv2编码
	 lv2_prod_rd_team_cn_name character varying(600),        --重量级团队LV2中文描述
	 lv2_prod_rd_team_en_name character varying(600),        --重量级团队LV2英文描述
	 lv3_prod_rnd_team_code character varying(50),           --重量级团队lv3编码
	 lv3_prod_rd_team_cn_name character varying(600),        --重量级团队LV3中文描述
	 lv3_prod_rd_team_en_name character varying(600),        --重量级团队LV3英文描述
	 geo_pc_key numeric,									 --区域责任中心key
	 oversea_flag character varying(20),                     --海外标志
	 prod_en_name character varying(600),                    --产品英文描述
	 bg_en_name character varying(200),                      --BG英文名称
	 region_code character varying(50),                      --地区部编码
	 region_cn_name character varying(200),                  --地区部中文名称
	 region_en_name character varying(200),                  --地区部英文名称
	 rep_office_code character varying(50),                  --代表处编码
	 rep_office_cn_name character varying(200),              --代表处中文名称
	 rep_office_en_name character varying(200)               --代表处英文名称
	) on commit preserve rows distribute by hash(version_code,period_id,spart_code,prod_key);

    --参数一非空，参数二非空
  if((p_schedule is not null or p_schedule <> '') and (p_period is not null or p_period <> '')) then
     -- 生成版本之前需要先判断来源表中是否有传入参数的数据，如没有则不用生成版本
      select count(*) as cnt into v_count 
	      from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i aa     -- 收入时点，20230720 切换BCM的表
        -- from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_bcm aa	--20230720 切换BCM的表，UAT环境测试用	 	    
       where aa.period_id =to_char(add_months(to_date(p_period,'yyyymm'),-1),'yyyymm')
      ;
	-- 传入参数一格式
	  if(upper(p_schedule) not in('I','F')) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入参数格式有误，传入第一个参数只能是I、F！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	  x_success_flag := '2001';
	  return;
	   
	-- 传入参数二格式
	 elseif(length(p_period) <> 6) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入第二个参数：'||p_period||'，传入参数格式有误，正确格式：yyyymm ！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	   return;
	  
	  -- 判断传入会计期在来源表是否有数据
	  elseif(v_count = 0) then
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入会计期：'||p_period||'，来源表中没数据，请重新传入会计期！',--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => 0,
          p_log_errbuf => null  --错误编码
        ) ;
	    x_success_flag := '2001';
	    return;
	  
     -- 调度类型+传入会计期，有值：1、增量（从集成表取上月数据）+ 最大版本的数据（所有数据关联维度表刷新）
     -- 更新了传入会计期的数据，最大版本的所有数据都用最新的维表刷新
      elseif(upper(p_schedule)='I') then  
       insert into prod_unit_tmp1
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name               --代表处英文名称
				)	
        -- 取最大版本数据，所有数据关联维度表刷新
	      select aa.version_code,--版本
	             aa.period_id,--	会计期
			     aa.prod_key,--	产品key
			     aa.geo_pc_key,--区域责任中心key
			     cc.prod_code,--	产品编码
			     aa.rmb_cost,--bpart成本金额_人民币(绝密)
			     aa.usd_cost,--bpart成本金额_美金(绝密)
			     aa.rmb_revenue,--spart收入金额_人民币
			     aa.usd_revenue,--spart收入金额_美金
			     aa.spart_qty,--	物料数量
			     aa.spart_code,--part编码
			     aa.spart_desc,--part描述
			     cc.prod_cn_name ,--产品名称
			     cc.lv0_prod_list_code as bg_code,--bg编码
			     cc.lv0_prod_list_cn_name as bg_name,--bg名称
	             cc.lv0_prod_rnd_team_code,		     --重量级团队lv0编码
	             cc.lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             cc.lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             cc.lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             cc.lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             cc.lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             cc.lv2_prod_rnd_team_code,		     --重量级团队lv2编码
	             cc.lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             cc.lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             cc.lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             cc.lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             cc.lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
			     aa.oversea_flag,--海外标志
				 cc.prod_en_name,                    --产品英文描述
				 cc.lv0_prod_list_en_name as bg_en_name,                      --BG英文名称
				 aa.region_code,                      --地区部编码
				 aa.region_cn_name,                  --地区部中文名称
				 aa.region_en_name,                  --地区部英文名称
				 aa.rep_office_code,                  --代表处编码
				 aa.rep_office_cn_name,              --代表处中文名称
				 aa.rep_office_en_name               --代表处英文名称				 
	        from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t aa  					    --收入时点	
       left join dmdim.dm_dim_product_d cc				--产品维表
	          on aa.prod_key = cc.prod_key
			 --and cc.scd_active_ind = 1 
		     and cc.del_flag = 'N'
	       where aa.version_code = v_max_version_code      --目标表最大版本
            ;
		
        -- 删除最大版本数据中上月数据
     delete from prod_unit_tmp1 where period_id = to_char(add_months(to_date(p_period,'yyyymm'),-1),'yyyymm');

				
        -- 从集成表取数入到临时表1
     insert into prod_unit_tmp1
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name               --代表处英文名称				 
				)
	---收入时点的spart编码不足8位的，需要前面补0
	 with prod_unit_tmp as(
		 select period_id,
				    case when length(spart_code) < 8 then lpad(spart_code,8,'0') else spart_code end as spart_code,
				    p_flag,
				    scenario,
				    prod_key,
			    	prod_code,
			    	geo_pc_key,
			    	dimension_key,
			    	part_qty,
			    	rmb_fact_rate_amt,
				    usd_fact_rate_amt,
			    	row_number() over(partition by period_id,
												                   spart_code,
												                   p_flag,
											                     scenario,
											                   	 prod_key,
												                   prod_code,
												                   geo_pc_key order by dimension_key) as rn
		    from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i		--收入时点全量part量本价明细表，20230720 切换BCM的表											   
		  -- from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_bcm    --20230720 切换BCM的表，UAT环境测试用	
	     where period_id = to_char(add_months(to_date(p_period,'yyyymm'),-1),'yyyymm')               --取上月历史数据   
		),
	---物料维表处理成临时表,对物料维表spart编码不足8位的，需要前面补0
	  material_tamp as (
	    select
	         case when length(t1.material_code) < 8 then lpad(t1.material_code,8,'0') else t1.material_code end as material_code, ---物料编码
	         t1.scd_active_begin_date,
	         t1.master_org_material_cn_desc
	         from (
	         select
	         t.material_code,
	         t.scd_active_begin_date,
	         t.master_org_material_cn_desc,
	         row_number()over(partition by t.material_code
					             order by t.scd_active_begin_date desc,
					                      t.master_org_material_cn_desc) as rn   ---取最大的 scd_active_begin_date 
	     from dwrdim.dwr_dim_material_d t                                     ---物料维表
        where t.scd_active_ind = 1
	      and t.master_org_material_cn_desc is not null                       ---排除空值
	      and t.master_org_material_cn_desc <> '源为空'                       ---排除'源为空'
	      and t.master_org_material_cn_desc <> '--'							             ---排除'--'
	      and t.del_flag = 'N'
	       ) t1
        where t1.rn = 1                                                       ---取第一条最大的
	   )		
	      select '' as version_code,--版本
	             aa.period_id,--	会计期
			     aa.prod_key::numeric,--	产品key
			     aa.geo_pc_key,--区域责任中心key
			     cc.prod_code,--	产品编码
			    (case when upper(aa.scenario) = 'GC' then aa.rmb_fact_rate_amt end ) as rmb_cost,--bpart成本金额_人民币(绝密)
			    (case when upper(aa.scenario) = 'GC' then aa.usd_fact_rate_amt end ) as usd_cost,--bpart成本金额_美金(绝密)
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.rmb_fact_rate_amt end ) as rmb_revenue,--spart收入金额_人民币
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.usd_fact_rate_amt end ) as usd_revenue,--spart收入金额_美金
			    (case when upper(aa.scenario) = 'REV' then aa.part_qty end ) as spart_qty,--	物料数量
			    aa.spart_code,--part编码
			    ee.master_org_material_cn_desc as spart_desc,--part描述
			    cc.prod_cn_name ,--产品名称
			    cc.lv0_prod_list_code as bg_code,--bg编码
			    cc.lv0_prod_list_cn_name as bg_name,--bg名称
	            cc.lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	            cc.lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	            cc.lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	            cc.lv1_prod_rnd_team_code,          --重量级团队lv1编码
	            cc.lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	            cc.lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	            cc.lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	            cc.lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	            cc.lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	            cc.lv3_prod_rnd_team_code,          --重量级团队lv3编码
	            cc.lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	            cc.lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
			    dd.oversea_flag,--海外标志
				cc.prod_en_name,                    --产品英文描述
				cc.lv0_prod_list_en_name as bg_en_name,                      --BG英文名称
				dd.region_code,                      --地区部编码
				dd.region_cn_name,                  --地区部中文名称
				dd.region_en_name,                  --地区部英文名称
				dd.repoffice_code as rep_office_code,                  --代表处编码
				dd.repoffice_cn_name as rep_office_cn_name,              --代表处中文名称
				dd.repoffice_en_name as rep_office_en_name               --代表处英文名称			
	       from prod_unit_tmp aa  					    --收入时点临时表	
      left join dmdim.dm_dim_product_d cc				--产品维表
	         on aa.prod_key = cc.prod_key
			--and cc.scd_active_ind = 1 
		    and cc.del_flag = 'N'
      left join dmdim.dm_dim_region_rc_d dd				--区域维表
	         on aa.geo_pc_key = dd.geo_pc_key
	    	and dd.del_flag = 'N'		 
      left join material_tamp ee				--物料维表
	         on aa.spart_code = ee.material_code
	      where aa.rn = 1	  	  
        ;	  
  
 v_dml_row_count := sql%rowcount;      -- 收集数据量

	-- 记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '调度类型:'||p_schedule||',传入会计期:'||p_period||',增量(从集成表取上月数据)+最大版本的数据(所有数据关联维度表刷新),prod_unit_tmp1 临时表的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 调度类型+传入会计期，有值：2、全量（从集成表取period_id<传入会计期的数据）
  -- 更新了传入会计期的数据，period_id<传入会计期的数据都用最新的维表刷新
  elseif(upper(p_schedule)='F') then
        -- 从集成表取数入到临时表1
      insert into prod_unit_tmp1
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name               --代表处英文名称					 
				)	
	---收入时点的spar编码不足8位的，需要前面补0
	with prod_unit_tmp as(
		 select period_id,
				    case when length(spart_code) < 8 then lpad(spart_code,8,'0') else spart_code end as spart_code,
				    p_flag,
				    scenario,
				    prod_key,
			    	prod_code,
			    	geo_pc_key,
			    	dimension_key,
			    	part_qty,
			    	rmb_fact_rate_amt,
				    usd_fact_rate_amt,
			    	row_number() over(partition by period_id,
												                   spart_code,												               
												                   p_flag,
											                     scenario,
											                   	 prod_key,
												                   prod_code,
												                   geo_pc_key order by dimension_key) as rn
		    from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i		--收入时点全量part量本价明细表，20230720 切换BCM的表											   
		  -- from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_bcm    --20230720 切换BCM的表，UAT环境测试用	
	     where period_id < p_period                --取period_id<传入会计期的数据   
		),
	---物料维表处理成临时表,对物料维表spart编码不足8位的，需要前面补0
	  material_tamp as (
	    select
	         case when length(t1.material_code) < 8 then lpad(t1.material_code,8,'0') else t1.material_code end as material_code, ---物料编码
	         t1.scd_active_begin_date,
	         t1.master_org_material_cn_desc
	         from (
	         select
	         t.material_code,
	         t.scd_active_begin_date,
	         t.master_org_material_cn_desc,
	         row_number()over(partition by t.material_code
					             order by t.scd_active_begin_date desc,
					                      t.master_org_material_cn_desc) as rn   ---取最大的 scd_active_begin_date 
	     from dwrdim.dwr_dim_material_d t                                     ---物料维表
        where t.scd_active_ind = 1
	      and t.master_org_material_cn_desc is not null                       ---排除空值
	      and t.master_org_material_cn_desc <> '源为空'                       ---排除'源为空'
	      and t.master_org_material_cn_desc <> '--'							             ---排除'--'
	      and t.del_flag = 'N'
	       ) t1
        where t1.rn = 1                                                       ---取第一条最大的
	   )			
	      select '' as version_code,--版本
	             aa.period_id,--	会计期
			     aa.prod_key::numeric,--	产品key
			     aa.geo_pc_key,--区域责任中心key
			     cc.prod_code,--	产品编码
			    (case when upper(aa.scenario) = 'GC' then aa.rmb_fact_rate_amt end ) as rmb_cost,--bpart成本金额_人民币(绝密)
			    (case when upper(aa.scenario) = 'GC' then aa.usd_fact_rate_amt end ) as usd_cost,--bpart成本金额_美金(绝密)
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.rmb_fact_rate_amt end ) as rmb_revenue,--spart收入金额_人民币
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.usd_fact_rate_amt end ) as usd_revenue,--spart收入金额_美金
			    (case when upper(aa.scenario) = 'REV' then aa.part_qty end ) as spart_qty,--	物料数量
			    aa.spart_code,--part编码
			    ee.master_org_material_cn_desc as spart_desc,--part描述
			    cc.prod_cn_name ,--产品名称
			    cc.lv0_prod_list_code as bg_code,--bg编码
			    cc.lv0_prod_list_cn_name as bg_name,--bg名称
	            cc.lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	            cc.lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	            cc.lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	            cc.lv1_prod_rnd_team_code,          --重量级团队lv1编码
	            cc.lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	            cc.lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	            cc.lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	            cc.lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	            cc.lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	            cc.lv3_prod_rnd_team_code,          --重量级团队lv3编码
	            cc.lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	            cc.lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
			    dd.oversea_flag,--海外标志
				cc.prod_en_name,                    --产品英文描述
				cc.lv0_prod_list_en_name as bg_en_name,                      --BG英文名称
				dd.region_code,                      --地区部编码
				dd.region_cn_name,                  --地区部中文名称
				dd.region_en_name,                  --地区部英文名称
				dd.repoffice_code as rep_office_code,                  --代表处编码
				dd.repoffice_cn_name as rep_office_cn_name,              --代表处中文名称
				dd.repoffice_en_name as rep_office_en_name               --代表处英文名称			
	       from prod_unit_tmp aa  					    --收入时点临时表	
	  left join dmdim.dm_dim_product_d cc				--产品维表
	  		 on aa.prod_key = cc.prod_key
			--and cc.scd_active_ind = 1 
	  		and cc.del_flag = 'N'		 
	  left join dmdim.dm_dim_region_rc_d dd				--区域维表
	  		 on aa.geo_pc_key = dd.geo_pc_key
	  		and dd.del_flag = 'N'		 
      left join material_tamp ee				--物料维表
	         on aa.spart_code = ee.material_code
	      where aa.rn = 1			  
		 ;
		 
    v_dml_row_count := sql%rowcount;   -- 收集数据量
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '调度类型:'||p_schedule||',传入会计期:'||p_period||',全量(从集成表取period_id<传入会计期的数据),prod_unit_tmp1 临时表的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
    end if;
	
	-- 传入参数一非空，传入参数二空
  elseif((p_schedule is not null or p_schedule <> '') and (p_period is null or p_period = '')) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，传入第一个参数只能是I、F；传入第二个参数格式：yyyymm ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	  x_success_flag := '2001';
	  return;
	
	-- 传入参数一空，传入参数二非空
  elseif((p_schedule is null or p_schedule = '') and (p_period is not null or p_period <> '')) then
	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入第一个参数：'||p_schedule||'，传入第二个参数：'||p_period||'，传入第一个参数只能是I、F；传入第二个参数格式：yyyymm ！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	  x_success_flag := '2001';
	  return;


	-- 传入参数一空，传入参数二空  
  -- 调度类型+传入会计期，无值：取集成表period_id<当前年月的数据
  else
    insert into prod_unit_tmp1
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name               --代表处英文名称				 
				)	
    -- 从集成表取数入到临时表1
	
	---收入时点的spart编码不足8位的，需要前面补0
	with prod_unit_tmp as(
		 select period_id,
				    case when length(spart_code) < 8 then lpad(spart_code,8,'0') else spart_code end as spart_code,
				    p_flag,
				    scenario,
				    prod_key,
			    	prod_code,
			    	geo_pc_key,
			    	dimension_key,
			    	part_qty,
			    	rmb_fact_rate_amt,
				    usd_fact_rate_amt,
			    	row_number() over(partition by period_id,
												                   spart_code,
												                   p_flag,
											                     scenario,
											                   	 prod_key,
												                   prod_code,
												                   geo_pc_key order by dimension_key) as rn
		    from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i		--收入时点全量part量本价明细表，20230720 切换BCM的表											   																   
		  -- from fin_dm_opt_fop.fop_dwl_prod_prod_unit_i_bcm    --20230720 切换BCM的表，UAT环境测试用	
	     where period_id < to_char(current_date,'yyyymm')              --取period_id<当前年月的数据   
		),
	---物料维表处理成临时表,对物料维表spart编码不足8位的，需要前面补0
	  material_tamp as (
	    select
	         case when length(t1.material_code) < 8 then lpad(t1.material_code,8,'0') else t1.material_code end as material_code, ---物料编码
	         t1.scd_active_begin_date,
	         t1.master_org_material_cn_desc
	         from (
	         select
	         t.material_code,
	         t.scd_active_begin_date,
	         t.master_org_material_cn_desc,
	         row_number()over(partition by t.material_code
					             order by t.scd_active_begin_date desc,
					                      t.master_org_material_cn_desc) as rn   ---取最大的 scd_active_begin_date 
	     from dwrdim.dwr_dim_material_d t                                     ---物料维表
        where t.scd_active_ind = 1
	      and t.master_org_material_cn_desc is not null                       ---排除空值
	      and t.master_org_material_cn_desc <> '源为空'                       ---排除'源为空'
	      and t.master_org_material_cn_desc <> '--'							             ---排除'--'
	      and t.del_flag = 'N'
	       ) t1
        where t1.rn = 1                                                       ---取第一条最大的
	   )			
	      select '' as version_code,--版本
	             aa.period_id,--	会计期
			     aa.prod_key::numeric,--	产品key
			     aa.geo_pc_key,--区域责任中心key
			     cc.prod_code,--	产品编码
			    (case when upper(aa.scenario) = 'GC' then aa.rmb_fact_rate_amt end ) as rmb_cost,--bpart成本金额_人民币(绝密)
			    (case when upper(aa.scenario) = 'GC' then aa.usd_fact_rate_amt end ) as usd_cost,--bpart成本金额_美金(绝密)
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.rmb_fact_rate_amt end ) as rmb_revenue,--spart收入金额_人民币
			    (case when upper(aa.scenario) = 'REV_SPLIT' then aa.usd_fact_rate_amt end ) as usd_revenue,--spart收入金额_美金
			    (case when upper(aa.scenario) = 'REV' then aa.part_qty end ) as spart_qty,--	物料数量
			    aa.spart_code,--part编码
			    ee.master_org_material_cn_desc as spart_desc,--part描述
			    cc.prod_cn_name ,--产品名称
			    cc.lv0_prod_list_code as bg_code,--bg编码
			    cc.lv0_prod_list_cn_name as bg_name,--bg名称
	            cc.lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	            cc.lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	            cc.lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	            cc.lv1_prod_rnd_team_code,          --重量级团队lv1编码
	            cc.lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	            cc.lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	            cc.lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	            cc.lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	            cc.lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	            cc.lv3_prod_rnd_team_code,          --重量级团队lv3编码
	            cc.lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	            cc.lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
			    dd.oversea_flag,--海外标志
				cc.prod_en_name,                    --产品英文描述
				cc.lv0_prod_list_en_name as bg_en_name,                      --BG英文名称
				dd.region_code,                      --地区部编码
				dd.region_cn_name,                  --地区部中文名称
				dd.region_en_name,                  --地区部英文名称
				dd.repoffice_code as rep_office_code,                  --代表处编码
				dd.repoffice_cn_name as rep_office_cn_name,              --代表处中文名称
				dd.repoffice_en_name as rep_office_en_name               --代表处英文名称				
	       from prod_unit_tmp aa  					    --收入时点临时表	
      left join dmdim.dm_dim_product_d cc				--产品维表
      	     on aa.prod_key = cc.prod_key
			--and cc.scd_active_ind = 1 
      		and cc.del_flag = 'N'		 
      left join dmdim.dm_dim_region_rc_d dd				--区域维表
      	     on aa.geo_pc_key = dd.geo_pc_key
      		and dd.del_flag = 'N'		 
      left join material_tamp ee				--物料维表
	         on aa.spart_code = ee.material_code
	      where aa.rn = 1             	  
		  ;
		 
    v_dml_row_count := sql%rowcount;   -- 收集数据量
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '调度类型:无,传入会计期:无,取集成表period_id<当前年月的数据,prod_unit_tmp1 临时表的数据量:'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;  

end if
   ;
	  
  -- 取当前日期的最大版本数据，如果有值，则版本号+1；如果没有值，则版本编码=当前年月日_V1
  if(substring(v_current_max_version_code,9) is null or substring(v_current_max_version_code,9) = '') then
    -- 如果当前日期没有版本，则生成新的版本编码，数据入到目标表	 
    insert into fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name,               --代表处英文名称				 
				 remark,                            ---备注
				 created_by,          ---创建人
				 creation_date,       ---创建时间
				 last_updated_by,     ---修改人
				 last_update_date,    ---修改时间
				 del_flag             ---是否删除	
				)	
		  select ((select to_char(current_date,'yyyymm'))||'_'||'V1') as version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name,               --代表处英文名称				 
				 '' as remark,							---备注
				 -1 as created_by,                       ---创建人
				 current_timestamp as creation_date,     ---创建时间
				 -1 as last_updated_by,                  ---修改人
				 current_timestamp as last_update_date,  ---修改时间
				 'N' as del_flag                         ---是否删除				 
            from prod_unit_tmp1
			   ; 
  else
    -- 如果有当前日期的版本，则版本+1，数据入到目标表	
    insert into fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t
				(
				 version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name,               --代表处英文名称				 
				 remark,                            ---备注
				 created_by,          ---创建人
				 creation_date,       ---创建时间
				 last_updated_by,     ---修改人
				 last_update_date,    ---修改时间
				 del_flag             ---是否删除	
				)	
		  select (substring(v_current_max_version_code,1,8)||(substring(v_current_max_version_code,9)+1)) as version_code,--版本
				 period_id,--会计期
				 prod_key,--	产品key
				 geo_pc_key,--区域责任中心key
				 prod_code,--产品编码
				 rmb_cost,--bpart成本金额_人民币(绝密)
				 usd_cost,--bpart成本金额_美金(绝密)
				 rmb_revenue,--spart收入金额_人民币
				 usd_revenue,--spart收入金额_美金
				 spart_qty,--物料数量
				 spart_code,--part编码
				 spart_desc,--part描述
				 prod_cn_name ,--产品名称
				 bg_code,--bg编码
				 bg_name,--bg名称
	             lv0_prod_rnd_team_code,		  --重量级团队lv0编码
	             lv0_prod_rd_team_cn_name,        --重量级团队LV0中文描述
	             lv0_prod_rd_team_en_name,        --重量级团队LV0英文描述
	             lv1_prod_rnd_team_code,          --重量级团队lv1编码
	             lv1_prod_rd_team_cn_name,        --重量级团队LV1中文描述
	             lv1_prod_rd_team_en_name,        --重量级团队LV1英文描述
	             lv2_prod_rnd_team_code,		  --重量级团队lv2编码
	             lv2_prod_rd_team_cn_name,        --重量级团队LV2中文描述
	             lv2_prod_rd_team_en_name,        --重量级团队LV2英文描述
	             lv3_prod_rnd_team_code,          --重量级团队lv3编码
	             lv3_prod_rd_team_cn_name,        --重量级团队LV3中文描述
	             lv3_prod_rd_team_en_name,        --重量级团队LV3英文描述
				 oversea_flag,--海外标志
				 prod_en_name,                    --产品英文描述
				 bg_en_name,                      --BG英文名称
				 region_code,                      --地区部编码
				 region_cn_name,                  --地区部中文名称
				 region_en_name,                  --地区部英文名称
				 rep_office_code,                  --代表处编码
				 rep_office_cn_name,              --代表处中文名称
				 rep_office_en_name,               --代表处英文名称				 
				 '' as remark,							---备注
				 -1 as created_by,                       ---创建人
				 current_timestamp as creation_date,     ---创建时间
				 -1 as last_updated_by,                  ---修改人
				 current_timestamp as last_update_date,  ---修改时间
				 'N' as del_flag                         ---是否删除				 
            from prod_unit_tmp1
			;
  end if
	  ;
   
	v_dml_row_count := sql%rowcount;      -- 收集数据量
	
	select to_char(current_date,'yyyymm')||'_V'||max(substr(version_code,9)::numeric) as version_code into v_new_version_code from fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t
	where substr(version_code,1,6) = to_char(current_date,'yyyymm');  -- 取最新版本号
	
			  
-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_new_version_code||',dm_fop_prod_prod_unit_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_row_count => null,
			p_log_errbuf => sqlstate  --错误编码
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_prod_prod_unit_sum_t;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

