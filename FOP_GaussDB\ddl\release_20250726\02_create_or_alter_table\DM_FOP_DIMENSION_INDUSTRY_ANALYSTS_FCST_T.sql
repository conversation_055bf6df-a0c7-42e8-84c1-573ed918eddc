/*产品线预测结果表*/
DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T;
CREATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T(
 VERSION_CODE     VARCHAR(50 ),
 PERIOD_ID        NUMERIC,
 TARGET_CODE      VARCHAR(50 ),
 TARGET_DESC      VARCHAR(100),
 BG_CODE          VARCHAR(50 ),
 BG_NAME          VARCHAR(200),
 OVERSEA_CODE     VARCHAR(50 ),
 OVERSEA_DESC     VARCHAR(50 ),
 LV1_CODE         VARCHAR(100),
 LV1_NAME         VARCHAR(600),
 LV2_CODE         VARCHAR(100),
 LV2_NAME         VARCHAR(600),
 CURRENCY         VARCHAR(50 ),
 EQUIP_REV_AMT    NUMERIC(38,10),
 MGP_RATIO        NUMERIC(38,10),
 REMAR<PERSON>           VARCHAR(500),
 CREATED_BY       INT8,
 CREATION_DATE    TIMESTAMP,
 LAST_UPDATED_BY  INT8,
 LAST_UPDATE_DATE TIMESTAMP,
 DEL_FLAG         VARCHAR(10)
) WITH (ORIENTATION = COLUMN,COMPRESSION = LOW, COLVERSION = 2.0, ENABLE_DELTA = FALSE)   
DISTRIBUTE BY HASH(VERSION_CODE,PERIOD_ID);

COMMENT ON TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T                   IS '产品线预测结果表';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.VERSION_CODE     IS '版本（年月_001）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.PERIOD_ID        IS '预测会计期';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.TARGET_CODE      IS '预测步长编码（以2024年为例：月度为2024，季度为2024Q1~2024Q4，半年度为2024H1,2024H2,年度为2024）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.TARGET_DESC      IS '预测步长描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.BG_CODE          IS 'BG编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.BG_NAME          IS 'BG名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.OVERSEA_CODE     IS '区域编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.OVERSEA_DESC     IS '区域（包括全球、国内、海外）';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LV1_CODE         IS '重量级团队LV1编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LV1_NAME         IS '重量级团队LV1描述';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LV2_CODE         IS '重量级团队LV2编码';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LV2_NAME         IS '重量级团队LV2名称';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.CURRENCY         IS '币种';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.EQUIP_REV_AMT    IS '设备收入';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.MGP_RATIO        IS '制毛率';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.REMARK           IS '备注';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.CREATED_BY       IS '创建人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.CREATION_DATE    IS '创建时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LAST_UPDATED_BY  IS '修改人';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.LAST_UPDATE_DATE IS '修改时间';
COMMENT ON COLUMN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_INDUSTRY_ANALYSTS_FCST_T.DEL_FLAG         IS '是否删除';
