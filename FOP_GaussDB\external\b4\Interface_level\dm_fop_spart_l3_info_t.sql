-- Step 2：数据提取

标签名称：spart_detail_info_tmp1   数据源：fin_dm_opt_fop_uat
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_flag
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , l2_name
     , coa_l2_name
     , l3_name
     , l1_coefficient
     , l2_coefficient
     , l3_coefficient
     , rmb_revenue
     , rmb_cost
     , equip_rev_rmb_amt
     , equip_cost_rmb_amt
     , equip_rev_usd_amt
     , equip_cost_usd_amt
     , usd_revenue
     , usd_cost
     , snop_quantity
     , snop_plan_quantity
     , ship_qty
     , spart_qty
     , plan_unit_quantity
     , articulation_flag
     , source_table
  from fin_dm_opt_fop.dm_fop_spart_detail_info_kms_t
 where articulation_flag in('SCENO1','SCENO2','SCENO3')
   and upper(industry_type) = 'TGT'
   and version_code = '${V_DETAIL_MAX_VERSION_CODE}'
   and del_flag = 'N'
;

-- Step 3：SQL-Script
/*S&OP预算：如果<10月取期次年份的全年数据，如果>=10月取期次年份下一年的全年*/
cache lazy table spart_detail_info_tmp
as
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(snop_plan_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  /*S&OP预算*/
   and substr(period_id,1,4) = '${V_SNOP_BUDGET_PHASE_YEAR}'  /*取期次年份的全年数据，取最大版本的所有期次*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
 union all
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0)) as equip_rev_cons_before_amt    /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))    as equip_cost_cons_before_amt   /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt   /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt  /*设备成本金额(对价后)*/
     , sum(coalesce(snop_plan_quantity,0)) as plan_qty   /*发货量（SNOP）*/
     , sum(coalesce(ship_qty,0))           as ship_qty   /*发货量（历史）*/
     , sum(coalesce(spart_qty,0))          as spart_qty  /*收入量（历史）*/
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t'  /*S&OP预算*/
   and substr(period_id,1,4) = '${V_SNOP_BUDGET_PHASE_YEAR}'  /*取期次年份的全年数据，取最大版本的所有期次*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
 union all
/*非S&OP计算逻辑*/
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(snop_quantity,0) + coalesce(snop_plan_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
 union all
select version_code    /*版本编码*/
     , cast(period_id as int) as period_id     /*会计期  */
     , phase_date
     , bg_code       /*bg编码  */
     , bg_name       /*bg名称  */
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code    /*重量级团队lv1编码*/
     , lv1_prod_rd_team_cn_name as lv1_name    /*重量级团队lv1描述*/
     , lv2_prod_rnd_team_code   as lv2_code    /*重量级团队lv2编码*/
     , lv2_prod_rd_team_cn_name as lv2_name    /*重量级团队lv2名称*/
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0))        as equip_rev_cons_before_amt   /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))           as equip_cost_cons_before_amt  /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt    /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt   /*设备成本金额(对价后)*/
     , sum(coalesce(snop_quantity,0) + coalesce(snop_plan_quantity,0)) as plan_qty  /*发货量（snop）*/
     , sum(coalesce(ship_qty,0))  as ship_qty  /*发货量（历史）*/
     , sum(coalesce(spart_qty,0)) as spart_qty  /*收入量（历史）*/
     , articulation_flag   /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
     , source_table
  from spart_detail_info_tmp1
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'OTH' then '其他' end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
 union all
/*S&OP预测的取数逻辑与传入起始日期参数有关，所以逻辑需要单独取*/
/*传入参数为空时，期次取系统当前年月的6号至上月6号*/
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'CNY' as currency
     , sum(coalesce(rmb_revenue,0)) as equip_rev_cons_before_amt
     , sum(coalesce(rmb_cost,0))    as equip_cost_cons_before_amt
     , sum(coalesce(equip_rev_rmb_amt,0))  as equip_rev_cons_after_amt
     , sum(coalesce(equip_cost_rmb_amt,0)) as equip_cost_cons_after_amt
     , sum(coalesce(plan_unit_quantity,0)) as plan_qty
     , sum(coalesce(ship_qty,0))  as ship_qty
     , sum(coalesce(spart_qty,0)) as spart_qty
     , articulation_flag
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'   /*S&OP预测*/
   and phase_date >= '${V_SNOP_FCST_BEGIN_PHASE_DATE}'   /*如果传入参数为空，则取系统当前年月的上个月6号*/
   and phase_date < '${V_SNOP_FCST_END_PHASE_DATE}'      /*如果传入参数为空，则取系统当前年月6号*/
   and substr(period_id,1,4) = '${V_SNOP_FCST_YEAR}'     /*如果传入参数为空，则取系统当前年份数据*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
 union all
select version_code
     , cast(period_id as int) as period_id
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50)) as oversea_desc
     , lv1_prod_rnd_team_code   as lv1_code
     , lv1_prod_rd_team_cn_name as lv1_name
     , lv2_prod_rnd_team_code   as lv2_code
     , lv2_prod_rd_team_cn_name as lv2_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200)) as l2_name
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6)) as l1_coefficient
     , cast(round(l2_coefficient,6) as numeric(38,6)) as l2_coefficient
     , cast(round(l3_coefficient,6) as numeric(38,6)) as l3_coefficient
     , 'USD' as currency
     , sum(coalesce(usd_revenue,0))        as equip_rev_cons_before_amt   /*收入金额(对价前)*/
     , sum(coalesce(usd_cost,0))           as equip_cost_cons_before_amt  /*成本金额(对价前)*/
     , sum(coalesce(equip_rev_usd_amt,0))  as equip_rev_cons_after_amt    /*设备收入金额(对价后)*/
     , sum(coalesce(equip_cost_usd_amt,0)) as equip_cost_cons_after_amt   /*设备成本金额(对价后)*/
     , sum(coalesce(plan_unit_quantity,0)) as plan_qty   /*发货量（SNOP）*/
     , sum(coalesce(ship_qty,0))  as ship_qty   /*发货量（历史）*/
     , sum(coalesce(spart_qty,0)) as spart_qty  /*收入量（历史）*/
     , articulation_flag  /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
     , source_table
  from spart_detail_info_tmp1
 where source_table = 'fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t'   /*S&OP预测*/
   and phase_date >= '${V_SNOP_FCST_BEGIN_PHASE_DATE}'   /*如果传入参数为空，则取系统当前年月的上个月6号*/
   and phase_date < '${V_SNOP_FCST_END_PHASE_DATE}'      /*如果传入参数为空，则取系统当前年月6号*/
   and substr(period_id,1,4) = '${V_SNOP_FCST_YEAR}'     /*如果传入参数为空，则取系统当前年份数据*/
 group by version_code
     , cast(period_id as int)
     , phase_date
     , bg_code
     , bg_name
     , cast((case when oversea_flag = 'Y' then '海外' when oversea_flag = 'N' then '国内' when oversea_flag = 'G' then '全球' else oversea_flag end) as varchar(50))
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , l1_name
     , cast((case when coa_l2_name is not null then coa_l2_name else l2_name end) as varchar(200))
     , l3_name
     , cast((case when l1_coefficient is null then 0 else round(l1_coefficient,6) end) as numeric(38,6))
     , cast(round(l2_coefficient,6) as numeric(38,6))
     , cast(round(l3_coefficient,6) as numeric(38,6))
     , articulation_flag
     , source_table
;

/*区域描述打上全球的标签*/
cache lazy table oversea_desc_temp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name         /*l1名称*/
     , l2_name         /*l2名称*/
     , l3_name         /*l3名称*/
     , l2_coefficient  /*l2系数*/
     , l3_coefficient  /*l3系数*/
     , currency
     , articulation_flag
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(coalesce(l3_coefficient,0)*plan_qty ) as plan_qty
     , sum(coalesce(l3_coefficient,0)*ship_qty ) as ship_qty
     , sum(coalesce(l3_coefficient,0)*spart_qty) as spart_qty
  from spart_detail_info_tmp
 where oversea_desc is not null    /*国内、海外需要排除空值*/
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l2_coefficient
     , l3_coefficient
     , currency
     , articulation_flag
union all
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , '全球' as oversea_desc  /*全球=中国区+海外+其他*/
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name         /*l1名称*/
     , l2_name         /*l2名称*/
     , l3_name         /*l3名称*/
     , l2_coefficient  /*l2系数*/
     , l3_coefficient  /*l3系数*/
     , currency
     , articulation_flag
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(coalesce(l3_coefficient,0)*plan_qty ) as plan_qty
     , sum(coalesce(l3_coefficient,0)*ship_qty ) as ship_qty
     , sum(coalesce(l3_coefficient,0)*spart_qty) as spart_qty
  from spart_detail_info_tmp
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t')  /*排除S&OP表的国内、海外、全球数据*/
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l2_coefficient
     , l3_coefficient
     , currency
     , articulation_flag
;

/*bg名称和bg编码打上集团的标签*/
cache lazy table bg_name_temp
as
select version_code
     , period_id
     , phase_date
     , 'PROD0002' as bg_code
     , 'ICT' as bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l2_coefficient
     , l3_coefficient
     , currency
     , articulation_flag
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(plan_qty ) as plan_qty
     , sum(ship_qty ) as ship_qty
     , sum(spart_qty) as spart_qty
  from oversea_desc_temp
 group by version_code
     , period_id
     , phase_date
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l2_coefficient
     , l3_coefficient
     , currency
     , articulation_flag
 union all
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l2_coefficient
     , l3_coefficient
     , currency
     , articulation_flag
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , plan_qty
     , ship_qty
     , spart_qty
  from oversea_desc_temp
;

/*按照l3层级收敛*/
cache lazy table l3_temp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , articulation_flag
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(plan_qty ) as plan_qty
     , sum(ship_qty ) as ship_qty
     , sum(spart_qty) as spart_qty
  from bg_name_temp
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , articulation_flag
;

/*按照l2层级收敛*/
/*场景1对应的L1系数有2种：0或0.33333，其中0.33333的是射频模块的标识*/
cache lazy table l2_temp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv2_code
     , l1_name
     , l2_name
     , currency
     , articulation_flag
     , sum(coalesce(equip_rev_cons_before_amt,0)) as equip_rev_cons_before_amt  /*l1对价前收入金额*/
     , sum(case when articulation_flag = 'SCENO1' then plan_qty/3   /*S&OP的系数是从计委包取的，而计委包没有L1系数，计委包场景1的L1系数默认1/3*/
                else null
           end) as plan_qty  /*发货量（snop）*/
     , sum(case when articulation_flag = 'SCENO1' then coalesce(l2_coefficient,0)*ship_qty else null end)  as ship_qty  /*发货量（历史）*/
     , sum(case when articulation_flag = 'SCENO1' then coalesce(l2_coefficient,0)*spart_qty else null end) as spart_qty  /*收入量（历史）*/
  from bg_name_temp
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv2_code
     , l1_name
     , l2_name
     , currency
     , articulation_flag
;

/*计算收入占比、制毛率*/
cache lazy table all_temp_01
as
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t2.l1_name
     , t1.l2_name
     , t1.l3_name
     , t1.l3_coefficient
     , t1.currency
     , t1.equip_rev_cons_before_amt
     , t1.equip_cost_cons_before_amt
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件','其他') then (nvl(t2.plan_qty,0)*3) else t1.plan_qty end)   as plan_qty
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件','其他') then (nvl(t2.ship_qty,0)*3) else t1.ship_qty end)   as ship_qty
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件','其他') then (nvl(t2.spart_qty,0)*3) else t1.spart_qty end) as spart_qty
     , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
             when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
             else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt
        end) as rev_percent	 /*收入占比 = l3对价前收入金额/l2对价前收入金额*/
     , (case when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) = 0 then 0
             when nvl(t1.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_cost_cons_before_amt,0) <> 0 then -999999
             else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt
        end) as mgp_ratio	   /*制毛率   = 1 - l3对价前成本金额/l3对价前收入金额*/
     , t1.articulation_flag
     , (case when t1.phase_date is not null and t1.plan_qty = 0 then 'D' else '' end) as del_ind   /*phase_date有值且plan_qty为0的是要剔除的*/
  from l3_temp t1
  left join l2_temp t2
    on t1.version_code = t2.version_code
   and t1.period_id = t2.period_id
   and t1.bg_code = t2.bg_code
   and t1.oversea_desc = t2.oversea_desc
   and t1.lv1_code = t2.lv1_code
   and t1.lv2_code = t2.lv2_code
   and t1.currency = t2.currency
   and t1.l1_name = t2.l1_name
   and t1.l2_name = t2.l2_name
   and t1.articulation_flag = t2.articulation_flag
   and coalesce(t1.phase_date,'SNULL') = coalesce(t2.phase_date,'SNULL')
 where t1.l2_name is not null or t1.l2_name <> ''
;

/*计算单位成本、单位价格*/
cache lazy table all_temp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , (case when nvl(equip_cost_cons_before_amt,0) = 0 then null
             when nvl(spart_qty,0) = 0 then -999999
             else equip_cost_cons_before_amt / spart_qty
        end) as unit_cost	 /*单位成本 = l3对价前成本金额/l3收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)*/
     , (case when nvl(equip_rev_cons_before_amt,0) = 0 then null
             when nvl(spart_qty,0) = 0 then -999999
             else equip_rev_cons_before_amt / spart_qty
        end) as unit_price	/*单位价格 = l3对价前收入金额/l3收入数量*/
     , rev_percent	        /*收入占比 = l3对价前收入金额/l2对价前收入金额*/
     , mgp_ratio	          /*制毛率   = 1 - l3对价前成本金额/l3对价前收入金额*/
     , articulation_flag
     , del_ind
  from all_temp_01
;

/*L2期次造数逻辑（知识表示需要补全所有 l2_name 的期次数据，金额、数量，赋值0；系数给空值）*/
/*取期次对应会计期的全量数据*/
cache lazy table phase_info_tmp
as
select distinct version_code, period_id, phase_date
  from all_temp
 where coalesce(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (coalesce(equip_rev_cons_before_amt,0) <> 0
        or coalesce(equip_cost_cons_before_amt,0) <> 0
        or coalesce(plan_qty,0) <> 0
        or coalesce(spart_qty,0) <> 0
        or coalesce(ship_qty,0) <> 0
		   )
;

/*已经存在的期次对应会计期数据*/
cache lazy table l2_info_tmp
as
select distinct version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
	      or nvl(equip_cost_cons_before_amt,0) <> 0
	      or nvl(plan_qty,0) <> 0
	      or nvl(spart_qty,0) <> 0
	      or nvl(ship_qty,0) <> 0
		   )
;

cache lazy table other_info_tmp
as
select distinct version_code
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
	      or nvl(equip_cost_cons_before_amt,0) <> 0
	      or nvl(plan_qty,0) <> 0
	      or nvl(spart_qty,0) <> 0
	      or nvl(ship_qty,0) <> 0
		   )
;

cache lazy table l2_all_info_tmp
as
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t3.bg_code
     , t3.bg_name
     , t3.oversea_desc
     , t3.lv1_code
     , t3.lv1_name
     , t3.lv2_code
     , t3.lv2_name
     , t3.l1_name
     , t3.l2_name
     , t3.l3_name
     , t3.l3_coefficient
     , t3.currency
     , t3.articulation_flag
  from phase_info_tmp t1
  left join other_info_tmp t3
    on 1=1
;

/*需要造的数据入到临时表*/
cache lazy table l2_all_phase_info_tmp
as
select t5.version_code
     , t5.period_id
     , t5.phase_date
     , t5.bg_code
     , t5.bg_name
     , t5.oversea_desc
     , t5.lv1_code
     , t5.lv1_name
     , t5.lv2_code
     , t5.lv2_name
     , t5.l1_name
     , t5.l2_name
     , t5.l3_name
		 , t5.l3_coefficient
     , t5.currency
     , t5.articulation_flag
  from l2_all_info_tmp t5
  left join l2_info_tmp t2
    on t5.version_code       = t2.version_code
   and t5.period_id          = t2.period_id
   and nvl(t5.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t5.bg_code            = t2.bg_code
   and t5.bg_name            = t2.bg_name
   and t5.oversea_desc       = t2.oversea_desc
   and t5.lv1_code           = t2.lv1_code
   and t5.lv1_name           = t2.lv1_name
   and t5.lv2_code           = t2.lv2_code
   and t5.lv2_name           = t2.lv2_name
   and t5.l1_name            = t2.l1_name
   and t5.l2_name            = t2.l2_name
   and t5.currency           = t2.currency
   and t5.articulation_flag  = t2.articulation_flag
 where t2.period_id is null
   and t5.phase_date is not null   /*只取期次不为空的*/
;

cache lazy table spart_l2_info_tmp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
        or nvl(equip_cost_cons_before_amt,0) <> 0
        or nvl(plan_qty,0) <> 0
        or nvl(spart_qty,0) <> 0
        or nvl(ship_qty,0) <> 0
			 )
 union all
/*l2_name缺失期次的数据入到目标表*/
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , t1.l2_name
     , t1.l3_name
     , t1.l3_coefficient
     , t1.currency
     , 0 as equip_rev_cons_before_amt
     , 0 as equip_cost_cons_before_amt
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name in ('软件','其他') then (nvl(t2.plan_qty,0)*3) else 0 end) as plan_qty
     , 0 as spart_qty
     , 0 as ship_qty
     , null as unit_cost
     , null as unit_price
     , null as rev_percent
     , 0 as mgp_ratio
     , t1.articulation_flag
  from l2_all_phase_info_tmp t1
  left join l2_temp t2
    on t1.period_id = t2.period_id
   and t1.bg_code = t2.bg_code
   and t1.oversea_desc = t2.oversea_desc
   and t1.lv1_code = t2.lv1_code
   and t1.lv2_code = t2.lv2_code
   and t1.currency = t2.currency
   and t1.l1_name = t2.l1_name
   and t1.l2_name = t2.l2_name
   and t1.articulation_flag = t2.articulation_flag
   and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
;

cache lazy table dm_fop_spart_l2_info_his_tmp
as
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(plan_qty)  as plan_qty
     , sum(spart_qty) as spart_qty
     , sum(ship_qty)  as ship_qty
     , sum(unit_cost) as unit_cost
     , sum(unit_price)  as unit_price
     , sum(rev_percent) as rev_percent
     , sum(mgp_ratio)   as mgp_ratio
     , articulation_flag
     , '' as remark
     ,  -1 as created_by
     ,  current_timestamp as creation_date
     ,  -1 as last_updated_by
     ,  current_timestamp as last_update_date
     ,  'N' as del_flag
  from spart_l2_info_tmp
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , articulation_flag
;

/*数据入到提供给知识表示的接口表 dm_fop_spart_l2_info_t*/
select period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , l3_name
     , l3_coefficient
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
     , '' as remark
     ,  -1 as created_by
     ,  current_timestamp as creation_date
     ,  -1 as last_updated_by
     ,  current_timestamp as last_update_date
     ,  'N' as del_flag
  from dm_fop_spart_l2_info_his_tmp
;

	  	-- Step 4：数据装载
数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_l3_info_t             模式：TRUNCATE_TABLE


数据源：fin_dm_opt_fop_uat                    目标Schema：fin_dm_opt_fop
目标表(T)：dm_fop_spart_l3_info_his_t         模式：DELETE
                                              删除条件：version_code = '${V_L3_VERSION_CODE}'