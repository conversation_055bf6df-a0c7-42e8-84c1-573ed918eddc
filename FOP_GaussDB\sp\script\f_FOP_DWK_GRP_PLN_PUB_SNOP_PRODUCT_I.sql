-- ----------------------------
-- Function structure for f_FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I"(OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
	/*
创建时间：2023-08-21
创建人  ：柳兴旺 l00521248
背景描述：产品线S&OP预测明细数据,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I()

*/


declare
	v_sp_name varchar(500) := 'fin_dm_opt_fop.f_FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I';
	v_tbl_name varchar(500) := 'fin_dm_opt_fop.FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I';
	v_dml_row_count  number default 0 ;
	
begin
	x_success_flag := '1';       --1表示成功
	

	 --写日志,开始
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => v_sp_name||'：开始运行' ,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      

		---支持重跑，清除目标表要插入会计期的数据
		delete from fin_dm_opt_fop.FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I 
		where phase_date in (select distinct phase_date from fin_dm_opt_fop.fop_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I_temp);    ---temp表的日期 

		---插入目标表数据
		insert into fin_dm_opt_fop.FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I            ----供应中心发货时点明细数据
		(
		PHASE_NO,
		PHASE_DATE,
		MONTH,
		FCST_MONTH,
		IS_RELEASE_VERSION_FLAG,
		SNOP_CODE,
		ITEM_CODE,
		ITEM_KEY,
		COA_NO,
		PROD_KEY,
		MEASURE_CODE,
		BUCKET_ID,
		AREA_ID,
		AREA_KEY,
		AREA_TYPE,
		BG_CODE,
		BG_CN_NAME,
		BG_EN_NAME,
		LST_LV0_PROD_RND_TEAM_CODE,
		LST_LV0_PROD_RD_TEAM_CN_NAME,
		LST_LV0_PROD_RD_TEAM_EN_NAME,
		LST_LV1_PROD_RND_TEAM_CODE,
		LST_LV1_PROD_RD_TEAM_CN_NAME,
		LST_LV1_PROD_RD_TEAM_EN_NAME,
		LST_LV2_PROD_RND_TEAM_CODE,
		LST_LV2_PROD_RD_TEAM_CN_NAME,
		LST_LV2_PROD_RD_TEAM_EN_NAME,
		LST_LV3_PROD_RND_TEAM_CODE,
		LST_LV3_PROD_RD_TEAM_CN_NAME,
		LST_LV3_PROD_RD_TEAM_EN_NAME,
		SITE_CODE,
		SITE_KEY,
		HEADER_ID,
		LINE_ID,
		BUCKET_DESC,
		REVIEW_STATUS,
		PGROUP_CODE,
		END_DATE,
		PORT_QTY,
		PLAN_TYPE,
		PLAN_COM_LV1,
		PLAN_COM_LV2,
		PLAN_COM_LV3,
		ATTR4,
		PLAN_UNIT_QUANTITY,
		PROD_TYPE_CODE,
		PROD_TYPE,
		PROD_SOUS_TYPE_CODE,
		PROD_SOUS_TYPE,
		UNIT,
		LST_LV1_PROD_LIST_CODE,
		LST_LV1_PROD_LIST_CN_NAME,
		LST_LV1_PROD_LIST_EN_NAME,
		LST_LV2_PROD_LIST_CODE,
		LST_LV2_PROD_LIST_CN_NAME,
		LST_LV2_PROD_LIST_EN_NAME,
		LST_LV3_PROD_LIST_CODE,
		LST_LV3_PROD_LIST_CN_NAME,
		LST_LV3_PROD_LIST_EN_NAME,
		MATERIAL_ID,
		PRODUCT_ID,
		PRODUCT_MAIN_YN,
		QUANTITY,
		SS_ID,
		BD_BG_CODE,
		BD_BG_CN_NAME,
		BD_BG_EN_NAME,
		BD_BU_CODE,
		BD_BU_CN_NAME,
		BD_BU_EN_NAME,
		CUSTOM_ATTR_1,
		CUSTOM_ATTR_2,
		CUSTOM_ATTR_3,
		CUSTOM_ATTR_4,
		CUSTOM_ATTR_5,
		LAST_UPDATE_DATE_CODE,
		DEL_FLAG,
		CRT_CYCLE_ID,
		LAST_UPD_CYCLE_ID,
		CRT_JOB_INSTANCE_ID,
		UPD_JOB_INSTANCE_ID,
		DW_LAST_UPDATE_DATE
		)
		select 
		PHASE_NO,
		PHASE_DATE,
		MONTH,
		FCST_MONTH,
		IS_RELEASE_VERSION_FLAG,
		SNOP_CODE,
		ITEM_CODE,
		ITEM_KEY,
		COA_NO,
		PROD_KEY,
		MEASURE_CODE,
		BUCKET_ID,
		AREA_ID,
		AREA_KEY,
		AREA_TYPE,
		BG_CODE,
		BG_CN_NAME,
		BG_EN_NAME,
		LST_LV0_PROD_RND_TEAM_CODE,
		LST_LV0_PROD_RD_TEAM_CN_NAME,
		LST_LV0_PROD_RD_TEAM_EN_NAME,
		LST_LV1_PROD_RND_TEAM_CODE,
		LST_LV1_PROD_RD_TEAM_CN_NAME,
		LST_LV1_PROD_RD_TEAM_EN_NAME,
		LST_LV2_PROD_RND_TEAM_CODE,
		LST_LV2_PROD_RD_TEAM_CN_NAME,
		LST_LV2_PROD_RD_TEAM_EN_NAME,
		LST_LV3_PROD_RND_TEAM_CODE,
		LST_LV3_PROD_RD_TEAM_CN_NAME,
		LST_LV3_PROD_RD_TEAM_EN_NAME,
		SITE_CODE,
		SITE_KEY,
		HEADER_ID,
		LINE_ID,
		BUCKET_DESC,
		REVIEW_STATUS,
		PGROUP_CODE,
		END_DATE,
		PORT_QTY,
		PLAN_TYPE,
		PLAN_COM_LV1,
		PLAN_COM_LV2,
		PLAN_COM_LV3,
		ATTR4,
		PLAN_UNIT_QUANTITY,
		PROD_TYPE_CODE,
		PROD_TYPE,
		PROD_SOUS_TYPE_CODE,
		PROD_SOUS_TYPE,
		UNIT,
		LST_LV1_PROD_LIST_CODE,
		LST_LV1_PROD_LIST_CN_NAME,
		LST_LV1_PROD_LIST_EN_NAME,
		LST_LV2_PROD_LIST_CODE,
		LST_LV2_PROD_LIST_CN_NAME,
		LST_LV2_PROD_LIST_EN_NAME,
		LST_LV3_PROD_LIST_CODE,
		LST_LV3_PROD_LIST_CN_NAME,
		LST_LV3_PROD_LIST_EN_NAME,
		MATERIAL_ID,
		PRODUCT_ID,
		PRODUCT_MAIN_YN,
		QUANTITY,
		SS_ID,
		BD_BG_CODE,
		BD_BG_CN_NAME,
		BD_BG_EN_NAME,
		BD_BU_CODE,
		BD_BU_CN_NAME,
		BD_BU_EN_NAME,
		CUSTOM_ATTR_1,
		CUSTOM_ATTR_2,
		CUSTOM_ATTR_3,
		CUSTOM_ATTR_4,
		CUSTOM_ATTR_5,
		LAST_UPDATE_DATE_CODE,
		DEL_FLAG,
		CRT_CYCLE_ID,
		LAST_UPD_CYCLE_ID,
		CRT_JOB_INSTANCE_ID,
		UPD_JOB_INSTANCE_ID,
		DW_LAST_UPDATE_DATE
	   from fin_dm_opt_fop.fop_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I_temp
  		  ;

	v_dml_row_count := sql%rowcount;          -- 收集数据量

        -- 写结束日志
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '产品线S&OP预测明细数据'||v_tbl_name||'：结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
		
		    --收集统计信息
    analyse fin_dm_opt_fop.FOP_DWK_GRP_PLN_PUB_SNOP_PRODUCT_I;	
		

exception
  	when others then
        -- 写结束日志
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => sqlstate  --错误编码
      ) ;

	x_success_flag := '2001';       --2001表示失败
	

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

