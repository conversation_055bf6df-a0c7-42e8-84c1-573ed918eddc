-- ----------------------------
-- Table structure for fop_dwl_prod_prod_unit_i
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i";
CREATE TABLE "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i" (
  "period_id" numeric,
  "spart_code" varchar(200) COLLATE "pg_catalog"."default",
  "p_flag" varchar(19) COLLATE "pg_catalog"."default",
  "scenario" varchar(25) COLLATE "pg_catalog"."default",
  "prod_key" numeric,
  "prod_code" varchar(63) COLLATE "pg_catalog"."default",
  "geo_pc_key" numeric,
  "dimension_key" numeric,
  "part_qty" numeric,
  "rmb_fact_rate_amt" numeric,
  "usd_fact_rate_amt" numeric,
  "child_spartno" varchar(300) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."spart_code" IS 'part编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."p_flag" IS '标识(收入;成本;收入拆分)';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."scenario" IS '量纲场景(rev:收入;gc:集团成本;rev_split:收入拆分)';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."prod_key" IS '产品key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."prod_code" IS '产品编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."geo_pc_key" IS '区域责任中心key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."dimension_key" IS '量纲子类明细key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."part_qty" IS 'part物料数量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."rmb_fact_rate_amt" IS '成本场景:bpart成本金额_人民币;收入拆分场景:spart拆分金额_人民币';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i"."usd_fact_rate_amt" IS '成本场景:bpart成本金额_美元;收入拆分场景:spart拆分金额_美元';
COMMENT ON TABLE "fin_dm_opt_fop"."fop_dwl_prod_prod_unit_i" IS '业财联接_ICT产业产品量纲_Spart粒度_收入时点_量本价';

