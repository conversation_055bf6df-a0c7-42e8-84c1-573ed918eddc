-- ----------------------------
-- Function structure for f_dm_fop_spart_detail_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_detail_info_t"("p_version_code" varchar, "p_period" int8, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_detail_info_t"(IN "p_version_code" varchar=NULL::character varying, IN "p_period" int8=NULL::bigint, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
/*
创建时间：2022-10-12
创建人  ：qwx1110218
背景描述：SPART明细+L2~L3/L1~L3系数+type类型:
          1、通过物料编码+L1名称关联“Spart对象关系”维表打L2、L3标签以及L1~L3系数
          2、数据类型（Manual、AI、Adjust、New）：关联“Spart对象关系”维表后，场景三的置空，l2_name 为空则置New，否则置Manual；如果COA维表关联后l2_name有值，数据类型还是保持原来的；
          3、关于l2_name：通过“Spart对象关系”维表取的l2_name为一个字段(l2_name)，通过COA维表关联出来的l2_name为另一个字段(coa_l2_name)；
          4、L2层接口的l2_name取值逻辑：优先取 coa_l2_name 字段值，没有则取l2_name；
参数描述：参数一(p_version_code)：版本编码，参数格式：202207_V1
          参数二(p_period): 传入会计期，参数格式：202212；主要用于限制标签审视表，可以取标签审视的历史表
		      参数三(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_detail_info_t('202207_V1');
修改记录：20230601 qwx1110218 新增“BG英文名称”字段

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_detail_info_t('''||p_version_code||''','||p_period||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_detail_info_t';
	v_version_code varchar(50);  -- 取来源表中对应的传入版本
	v_max_version_code varchar(50);  -- 目标表中最大版本
	v_current_max_version_code varchar(50);  -- 目标表当前日期的最大版本编码，格式：当前年月日_V1...VN
	v_dml_row_count  number default 0 ;

begin
	
	set enable_force_vector_engine to on;
	x_success_flag := '1';

	-- 开始记录日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'SPART明细+L2~L3/L1~L3系数+type类型表：'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 spart_profiting_relation_tmp 临时表
  DROP TABLE IF EXISTS spart_profiting_relation_tmp;
  create temporary table spart_profiting_relation_tmp(
         item_code        varchar(150)
       , l1_name          varchar(200)             -- L1名称
       , l2_name          varchar(200)             -- L1名称
       , l3_name          varchar(200)             -- L1名称
       , l1_coefficient   numeric(38,10)           -- L1系数
       , l2_coefficient   numeric(38,10)           -- L1系数
       , l3_coefficient   numeric(38,10)           -- L1系数
       , data_type        varchar(50) -- 数据类型（His 历史、Add 新增）
       , update_flag      varchar(10) -- 修改标识（Y 是、N 否）
       , rn               numeric
  )on commit preserve rows distribute by hash(item_code,l1_name)
  ;


  -- 创建 spart_detail_l1_info_tmp 临时表
  DROP TABLE IF EXISTS spart_detail_l1_info_tmp;
  create temporary table spart_detail_l1_info_tmp(
    version_code                   varchar(100)             -- 版本编码
    ,period_id                     numeric                  -- 会计期
    ,spart_code                    varchar(150)             -- 物料编码
    ,spart_desc                    varchar(2000)            -- 物料描述
    ,prod_key                      numeric                  -- 产品KEY
    ,prod_code                     varchar(50)              -- 产品编码
    ,prod_cn_name                  varchar(600)             -- 产品名称
    ,bg_code                       varchar(50)              -- BG编码
    ,bg_name                       varchar(200)             -- BG名称
    ,bg_en_name                    varchar(200)             -- BG中文名称
    ,lv0_prod_rnd_team_code        varchar(50)              -- 重量级团队LV0编码
    ,lv0_prod_rd_team_cn_name      varchar(600)             -- 重量级团队LV0中文描述
    ,lv0_prod_rd_team_en_name      varchar(600)             -- 重量级团队LV0英文描述
    ,lv1_prod_rnd_team_code        varchar(50)              -- 重量级团队LV1编码
    ,lv1_prod_rd_team_cn_name      varchar(600)             -- 重量级团队LV1中文描述
    ,lv1_prod_rd_team_en_name      varchar(600)             -- 重量级团队LV1英文描述
    ,lv2_prod_rnd_team_code        varchar(50)              -- 重量级团队LV2编码
    ,lv2_prod_rd_team_cn_name      varchar(600)             -- 重量级团队LV2中文描述
    ,lv2_prod_rd_team_en_name      varchar(600)             -- 重量级团队LV2英文描述
    ,lv3_prod_rnd_team_code        varchar(50)              -- 重量级团队LV3编码
    ,lv3_prod_rd_team_cn_name      varchar(600)             -- 重量级团队LV3中文描述
    ,lv3_prod_rd_team_en_name      varchar(600)             -- 重量级团队LV3英文描述
    ,plan_com_lv1                  varchar(600)             -- 一级计委包
    ,plan_com_lv2                  varchar(600)             -- 二级计委包
    ,plan_com_lv3                  varchar(600)             -- 三级计委包
    ,busi_lv4                      varchar(600)             -- 四级业务包
    ,geo_pc_key                    numeric                  -- 区域责任中心KEY
    ,oversea_flag                  varchar(20)              -- 海外标志
    ,l1_name                       varchar(200)             -- L1名称
    ,phase_date                    varchar(60)              -- 期次分区字段
    ,unit                          varchar(600)             -- 单位
    ,source_table                  varchar(100)             -- 来源表
    ,industry_type                 varchar(50)              -- 产业类型（TGT 目标产业、OTHR 其它产业）
    ,spart_qty                     numeric(38,10)           -- Part物料数量
    ,ship_qty                      numeric(38,10)           -- Part发货数量
    ,snop_quantity                 numeric(38,10)           -- SNOP预测计划单元计划量
    ,snop_plan_quantity            numeric(38,10)           -- SNOP预算计划单元计划量
    ,rmb_revenue                   numeric(38,10)           -- Spart收入金额_人民币
    ,usd_revenue                   numeric(38,10)           -- Spart收入金额_美金
    ,rmb_cost                      numeric(38,10)           -- Bpart成本金额_人民币
    ,usd_cost                      numeric(38,10)           -- Bpart成本金额_美金
    ,equip_rev_rmb_amt             numeric(38,10)           -- 设备收入人民币金额
    ,equip_rev_usd_amt             numeric(38,10)           -- 设备收入美元金额
    ,equip_cost_rmb_amt            numeric(38,10)           -- 设备成本人民币金额
    ,equip_cost_usd_amt            numeric(38,10)           -- 设备成本美元金额
    ,plan_unit_quantity            numeric(38,10)           -- 计划单元计划量
  )on commit preserve rows distribute by hash(period_id,spart_code,prod_key)
  ;

  -- 取来源表的最大版本
  select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code into v_max_version_code
    from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t
   where substr(version_code,1,6) in(select max(substr(version_code,1,6)) from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t)
   group by substr(version_code,1,6)
	;
  
  -- 传入参数一空值，传入参数二空值
  -- 如果传入版本有值，则根据传入版本取值，否则取最大版本的数据
  if(p_version_code is null or p_version_code = '') then  
    -- 如果来源表有最大版本数据，则删除目标表中的最大版本
    delete fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = v_max_version_code;

    insert into spart_detail_l1_info_tmp(
      version_code                         -- 版本编码
      ,period_id                           -- 会计期
      ,spart_code                          -- 物料编码
      ,spart_desc                          -- 物料描述
      ,prod_key                            -- 产品KEY
      ,prod_code                           -- 产品编码
      ,prod_cn_name                        -- 产品名称
      ,bg_code                             -- BG编码
      ,bg_name                             -- BG英文名称
      ,bg_en_name                          -- BG中文名称
      ,lv0_prod_rnd_team_code              -- 重量级团队LV0编码
      ,lv0_prod_rd_team_cn_name            -- 重量级团队LV0中文描述
      ,lv0_prod_rd_team_en_name            -- 重量级团队LV0英文描述
      ,lv1_prod_rnd_team_code              -- 重量级团队LV1编码
      ,lv1_prod_rd_team_cn_name            -- 重量级团队LV1中文描述
      ,lv1_prod_rd_team_en_name            -- 重量级团队LV1英文描述
      ,lv2_prod_rnd_team_code              -- 重量级团队LV2编码
      ,lv2_prod_rd_team_cn_name            -- 重量级团队LV2中文描述
      ,lv2_prod_rd_team_en_name            -- 重量级团队LV2英文描述
      ,lv3_prod_rnd_team_code              -- 重量级团队LV3编码
      ,lv3_prod_rd_team_cn_name            -- 重量级团队LV3中文描述
      ,lv3_prod_rd_team_en_name            -- 重量级团队LV3英文描述
      ,plan_com_lv1                        -- 一级计委包
      ,plan_com_lv2                        -- 二级计委包
      ,plan_com_lv3                        -- 三级计委包
      ,busi_lv4                            -- 四级业务包
      ,geo_pc_key                          -- 区域责任中心KEY
      ,oversea_flag                        -- 海外标志
      ,l1_name                             -- L1名称
      ,phase_date                          -- 期次分区字段
      ,unit                                -- 单位
      ,source_table                        -- 来源表
      ,industry_type                       -- 产业类型（TGT 目标产业、OTHR 其它产业）
      ,spart_qty                           -- Part物料数量
      ,ship_qty                            -- Part发货数量
      ,snop_quantity                       -- SNOP预测计划单元计划量
      ,snop_plan_quantity                  -- SNOP预算计划单元计划量
      ,rmb_revenue                         -- Spart收入金额_人民币
      ,usd_revenue                         -- Spart收入金额_美金
      ,rmb_cost                            -- Bpart成本金额_人民币
      ,usd_cost                            -- Bpart成本金额_美金
      ,equip_rev_rmb_amt                   -- 设备收入人民币金额
      ,equip_rev_usd_amt                   -- 设备收入美元金额
      ,equip_cost_rmb_amt                  -- 设备成本人民币金额
      ,equip_cost_usd_amt                  -- 设备成本美元金额
      ,plan_unit_quantity                  -- 计划单元计划量
    )
    select t1.version_code
         , t1.period_id
         --, (case when t1.spart_code is null then '00000000' else t1.spart_code end) as spart_code   -- 如果spart编码为空，则赋值8个0
         , t1.spart_code
         , t1.spart_desc
         , t1.prod_key
         , t1.prod_code
         , t1.prod_cn_name
         , t1.bg_code
         , t1.bg_name
         , t1.bg_en_name
         , t1.lv0_prod_rnd_team_code
         , t1.lv0_prod_rd_team_cn_name
         , t1.lv0_prod_rd_team_en_name
         , t1.lv1_prod_rnd_team_code
         , t1.lv1_prod_rd_team_cn_name
         , t1.lv1_prod_rd_team_en_name
         , t1.lv2_prod_rnd_team_code
         , t1.lv2_prod_rd_team_cn_name
         , t1.lv2_prod_rd_team_en_name
         , t1.lv3_prod_rnd_team_code
         , t1.lv3_prod_rd_team_cn_name
         , t1.lv3_prod_rd_team_en_name
         , t1.plan_com_lv1
         , t1.plan_com_lv2
         , t1.plan_com_lv3
         , t1.busi_lv4
         , t1.geo_pc_key
         , t1.oversea_flag
         , t1.l1_name
         , t1.phase_date
         , t1.unit
         , t1.source_table
         , t1.industry_type
         , sum(nvl(t1.spart_qty,0)) as spart_qty       -- Part物料数量
         , sum(nvl(t1.ship_qty,0)) as ship_qty
         , sum(nvl(t1.snop_quantity,0)) as snop_quantity
         , sum(nvl(t1.snop_plan_quantity,0)) as snop_plan_quantity        -- SNOP月计划量
         , sum(nvl(t1.rmb_revenue,0)) as rmb_revenue
         , sum(nvl(t1.usd_revenue,0)) as usd_revenue
         , sum(nvl(t1.rmb_cost,0)) as rmb_cost
         , sum(nvl(t1.usd_cost,0)) as usd_cost
         , sum(nvl(t1.equip_rev_rmb_amt,0)) as  equip_rev_rmb_amt        -- 设备收入人民币金额
         , sum(nvl(t1.equip_rev_usd_amt,0)) as  equip_rev_usd_amt        -- 设备收入美元金额
         , sum(nvl(t1.equip_cost_rmb_amt,0)) as equip_cost_rmb_amt          -- 设备成本人民币金额
         , sum(nvl(t1.equip_cost_usd_amt,0)) as equip_cost_usd_amt          -- 设备成本美元金额
         , sum(nvl(t1.plan_unit_quantity,0)) as plan_unit_quantity
      from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t t1  -- SPART明细L1标签表
     where t1.del_flag = 'N'
       and t1.version_code = v_max_version_code  -- 取最大版本的数据
     group by t1.version_code
         , t1.period_id
         , t1.spart_code
         , t1.spart_desc
         , t1.prod_key
         , t1.prod_code
         , t1.prod_cn_name
         , t1.bg_code
         , t1.bg_name
         , t1.bg_en_name
         , t1.lv0_prod_rnd_team_code
         , t1.lv0_prod_rd_team_cn_name
         , t1.lv0_prod_rd_team_en_name
         , t1.lv1_prod_rnd_team_code
         , t1.lv1_prod_rd_team_cn_name
         , t1.lv1_prod_rd_team_en_name
         , t1.lv2_prod_rnd_team_code
         , t1.lv2_prod_rd_team_cn_name
         , t1.lv2_prod_rd_team_en_name
         , t1.lv3_prod_rnd_team_code
         , t1.lv3_prod_rd_team_cn_name
         , t1.lv3_prod_rd_team_en_name
         , t1.plan_com_lv1
         , t1.plan_com_lv2
         , t1.plan_com_lv3
         , t1.busi_lv4
         , t1.geo_pc_key
         , t1.oversea_flag
         , t1.l1_name
         , t1.phase_date
         , t1.unit
         , t1.source_table
         , t1.industry_type
    ;

    v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，spart_detail_l1_info_tmp 临时表最大版本的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  else
    -- 根据传入版本查询来源表数据
    if exists(select distinct version_code from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t where version_code = p_version_code) then
      select distinct version_code into v_version_code from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t where version_code = p_version_code;
      -- 根据传入版本查询来源表是否存在，如果存在则删除目标表对应的版本数据
      delete fin_dm_opt_fop.dm_fop_spart_detail_info_t where version_code = v_version_code;

      insert into spart_detail_l1_info_tmp(
        version_code                         -- 版本编码
        ,period_id                           -- 会计期
        ,spart_code                          -- 物料编码
        ,spart_desc                          -- 物料描述
        ,prod_key                            -- 产品KEY
        ,prod_code                           -- 产品编码
        ,prod_cn_name                        -- 产品名称
        ,bg_code                             -- BG编码
        ,bg_name                             -- BG中文名称
        ,bg_en_name                          -- BG英文名称
        ,lv0_prod_rnd_team_code              -- 重量级团队LV0编码
        ,lv0_prod_rd_team_cn_name            -- 重量级团队LV0中文描述
        ,lv0_prod_rd_team_en_name            -- 重量级团队LV0英文描述
        ,lv1_prod_rnd_team_code              -- 重量级团队LV1编码
        ,lv1_prod_rd_team_cn_name            -- 重量级团队LV1中文描述
        ,lv1_prod_rd_team_en_name            -- 重量级团队LV1英文描述
        ,lv2_prod_rnd_team_code              -- 重量级团队LV2编码
        ,lv2_prod_rd_team_cn_name            -- 重量级团队LV2中文描述
        ,lv2_prod_rd_team_en_name            -- 重量级团队LV2英文描述
        ,lv3_prod_rnd_team_code              -- 重量级团队LV3编码
        ,lv3_prod_rd_team_cn_name            -- 重量级团队LV3中文描述
        ,lv3_prod_rd_team_en_name            -- 重量级团队LV3英文描述
        ,plan_com_lv1                        -- 一级计委包
        ,plan_com_lv2                        -- 二级计委包
        ,plan_com_lv3                        -- 三级计委包
        ,busi_lv4                            -- 四级业务包
        ,geo_pc_key                          -- 区域责任中心KEY
        ,oversea_flag                        -- 海外标志
        ,l1_name                             -- L1名称
        ,phase_date                          -- 期次分区字段
        ,unit                                -- 单位
        ,source_table                        -- 来源表
        ,industry_type                       -- 产业类型（TGT 目标产业、OTHR 其它产业）
        ,spart_qty                           -- Part物料数量
        ,ship_qty                            -- Part发货数量
        ,snop_quantity                       -- SNOP预测计划单元计划量
        ,snop_plan_quantity                  -- SNOP预算计划单元计划量
        ,rmb_revenue                         -- Spart收入金额_人民币
        ,usd_revenue                         -- Spart收入金额_美金
        ,rmb_cost                            -- Bpart成本金额_人民币
        ,usd_cost                            -- Bpart成本金额_美金
        ,equip_rev_rmb_amt                   -- 设备收入人民币金额
        ,equip_rev_usd_amt                   -- 设备收入美元金额
        ,equip_cost_rmb_amt                  -- 设备成本人民币金额
        ,equip_cost_usd_amt                  -- 设备成本美元金额
        ,plan_unit_quantity                  -- 计划单元计划量
      )
      select t1.version_code
           , t1.period_id
           -- , (case when t1.spart_code is null then '00000000' else t1.spart_code end) as spart_code   -- 如果spart编码为空，则赋值8个0
           , t1.spart_code
           , t1.spart_desc
           , t1.prod_key
           , t1.prod_code
           , t1.prod_cn_name
           , t1.bg_code
           , t1.bg_name
           , t1.bg_en_name
           , t1.lv0_prod_rnd_team_code
           , t1.lv0_prod_rd_team_cn_name
           , t1.lv0_prod_rd_team_en_name
           , t1.lv1_prod_rnd_team_code
           , t1.lv1_prod_rd_team_cn_name
           , t1.lv1_prod_rd_team_en_name
           , t1.lv2_prod_rnd_team_code
           , t1.lv2_prod_rd_team_cn_name
           , t1.lv2_prod_rd_team_en_name
           , t1.lv3_prod_rnd_team_code
           , t1.lv3_prod_rd_team_cn_name
           , t1.lv3_prod_rd_team_en_name
           , t1.plan_com_lv1
           , t1.plan_com_lv2
           , t1.plan_com_lv3
           , t1.busi_lv4
           , t1.geo_pc_key
           , t1.oversea_flag
           , t1.l1_name
           , t1.phase_date
           , t1.unit
           , t1.source_table
           , t1.industry_type
           , sum(nvl(t1.spart_qty,0)) as spart_qty       -- Part物料数量
           , sum(nvl(t1.ship_qty,0)) as ship_qty
           , sum(nvl(t1.snop_quantity,0)) as snop_quantity
           , sum(nvl(t1.snop_plan_quantity,0)) as snop_plan_quantity        -- SNOP月计划量
           , sum(nvl(t1.rmb_revenue,0)) as rmb_revenue
           , sum(nvl(t1.usd_revenue,0)) as usd_revenue
           , sum(nvl(t1.rmb_cost,0)) as rmb_cost
           , sum(nvl(t1.usd_cost,0)) as usd_cost
           , sum(nvl(t1.equip_rev_rmb_amt,0)) as  equip_rev_rmb_amt        -- 设备收入人民币金额
           , sum(nvl(t1.equip_rev_usd_amt,0)) as  equip_rev_usd_amt        -- 设备收入美元金额
           , sum(nvl(t1.equip_cost_rmb_amt,0)) as equip_cost_rmb_amt          -- 设备成本人民币金额
           , sum(nvl(t1.equip_cost_usd_amt,0)) as equip_cost_usd_amt          -- 设备成本美元金额
           , sum(nvl(t1.plan_unit_quantity,0)) as plan_unit_quantity
        from fin_dm_opt_fop.dm_fop_spart_detail_l1_info_t t1  -- SPART明细L1标签表
       where t1.del_flag = 'N'
         and t1.version_code = v_version_code  -- 取传入版本的数据
       group by t1.version_code
           , t1.period_id
           , t1.spart_code
           , t1.spart_desc
           , t1.prod_key
           , t1.prod_code
           , t1.prod_cn_name
           , t1.bg_code
           , t1.bg_name
           , t1.bg_en_name
           , t1.lv0_prod_rnd_team_code
           , t1.lv0_prod_rd_team_cn_name
           , t1.lv0_prod_rd_team_en_name
           , t1.lv1_prod_rnd_team_code
           , t1.lv1_prod_rd_team_cn_name
           , t1.lv1_prod_rd_team_en_name
           , t1.lv2_prod_rnd_team_code
           , t1.lv2_prod_rd_team_cn_name
           , t1.lv2_prod_rd_team_en_name
           , t1.lv3_prod_rnd_team_code
           , t1.lv3_prod_rd_team_cn_name
           , t1.lv3_prod_rd_team_en_name
           , t1.plan_com_lv1
           , t1.plan_com_lv2
           , t1.plan_com_lv3
           , t1.busi_lv4
           , t1.geo_pc_key
           , t1.oversea_flag
           , t1.l1_name
           , t1.phase_date
           , t1.unit
           , t1.source_table
           , t1.industry_type
      ;

      v_dml_row_count := sql%rowcount;  -- 收集数据量

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
          p_log_version_id => null,                 --版本
          p_log_sp_name => v_sp_name,    --sp名称
          p_log_para_list => '',--参数
          p_log_step_num  => 2,
          p_log_cal_log_desc => '传入版本：'||v_version_code||'，spart_detail_l1_info_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
          p_log_formula_sql_txt => null,--错误信息
          p_log_row_count => v_dml_row_count,
          p_log_errbuf => null  --错误编码
        ) ;
    else
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本：'||v_version_code||'，来源表中没有此版本，请重新传入版本！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => 0,
        p_log_errbuf => null  --错误编码
      ) ;
	    x_success_flag := '2001';
	    return;
    end if;
  end if;
  
  -- 【排除SNOP的两张表】通过物料编码+L1名称关联“Spart对象关系”维表打L2、L3标签以及L1~L3系数
  -- 【排除SNOP的两张表】数据类型（Manual、AI、Adjust、New）：关联“Spart对象关系”维表后，场景三的置空，l2_name 为空则置New，否则置Manual；如果COA维表关联后l2_name有值，数据类型还是保持原来的；
  -- 【排除SNOP的两张表】关于l2_name：通过“Spart对象关系”维表取的l2_name为一个字段(l2_name)，通过COA维表关联出来的l2_name为另一个字段(coa_l2_name)；
  -- 【排除SNOP的两张表】L2层接口的l2_name取值逻辑：优先取 coa_l2_name 字段值，没有则取l2_name；
  -- “Spart对象关系”维表数据去重，取最新版本数据，历史数据也用最新版的；只取提交的版本；
	-- 如果业务有调整，update_flag=Y，则优先取业务调整的数据
  
  -- 传入参数一空值，传入参数二空值，则取最大版本年月的数据
  if((p_period is null or p_period = '') and (p_version_code is null or p_version_code = '')) then
    insert into spart_profiting_relation_tmp(
           item_code
         , l1_name            -- L1名称
         , l2_name            -- L1名称
         , l3_name            -- L1名称
         , l1_coefficient     -- L1系数
         , l2_coefficient     -- L1系数
         , l3_coefficient     -- L1系数
         , data_type          -- 数据类型（His 历史、Add 新增）
         , update_flag        -- 修改标识（Y 是、N 否）
         , rn
    )
    select item_code
         , l1_name
         , l2_name
         , l3_name
         , l1_coefficient
         , l2_coefficient
         , l3_coefficient
         , data_type    -- 数据类型（His 历史、Add 新增）
         , update_flag  -- 修改标识（Y 是、N 否）
         , row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
      from (
            select distinct (case when length(item_code) < 8 then lpad(item_code,8,'0') else item_code end) as item_code
                 , l1_name
                 , l2_name
                 , l3_name
                 , l1_coefficient
                 , l2_coefficient
                 , l3_coefficient
                 , data_type    -- 数据类型（His 历史、Add 新增）
                 , update_flag  -- 修改标识（Y 是、N 否）
              from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
             where del_flag = 'N'
               and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
               and period_id = substr(v_max_version_code,1,6)::numeric  -- 取最大版本编码的年月
           )
    ;
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '最大版本编码'||v_max_version_code||'，取的标签审视 '||substr(v_max_version_code,1,6)||' 版本的数据。',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
  
  -- 传入参数一有值，传入参数二空值，则取传入参数一（即版本编码）的年月数据   
  elseif((p_period is null or p_period = '') and (p_version_code is not null or p_version_code <> '')) then
    insert into spart_profiting_relation_tmp(
           item_code
         , l1_name            -- L1名称
         , l2_name            -- L1名称
         , l3_name            -- L1名称
         , l1_coefficient     -- L1系数
         , l2_coefficient     -- L1系数
         , l3_coefficient     -- L1系数
         , data_type          -- 数据类型（His 历史、Add 新增）
         , update_flag        -- 修改标识（Y 是、N 否）
         , rn
    )
    select item_code
         , l1_name
         , l2_name
         , l3_name
         , l1_coefficient
         , l2_coefficient
         , l3_coefficient
         , data_type    -- 数据类型（His 历史、Add 新增）
         , update_flag  -- 修改标识（Y 是、N 否）
         , row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
      from (
            select /*+set global(best_agg_plan 1) set global(query_dop 4)*/distinct (case when length(item_code) < 8 then lpad(item_code,8,'0') else item_code end) as item_code
                 , l1_name
                 , l2_name
                 , l3_name
                 , l1_coefficient
                 , l2_coefficient
                 , l3_coefficient
                 , data_type    -- 数据类型（His 历史、Add 新增）
                 , update_flag  -- 修改标识（Y 是、N 否）
              from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
             where del_flag = 'N'
               and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
               and period_id = substr(p_version_code,1,6)::numeric  -- 取版本编码的年月
           )
    ;
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '传入版本编码：'||p_version_code||'，取的标签审视 '||substr(p_version_code,1,6)||' 版本的数据。',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
      
  else
    insert into spart_profiting_relation_tmp(
           item_code
         , l1_name            -- L1名称
         , l2_name            -- L1名称
         , l3_name            -- L1名称
         , l1_coefficient     -- L1系数
         , l2_coefficient     -- L1系数
         , l3_coefficient     -- L1系数
         , data_type          -- 数据类型（His 历史、Add 新增）
         , update_flag        -- 修改标识（Y 是、N 否）
         , rn
    )
    select item_code
         , l1_name
         , l2_name
         , l3_name
         , l1_coefficient
         , l2_coefficient
         , l3_coefficient
         , data_type    -- 数据类型（His 历史、Add 新增）
         , update_flag  -- 修改标识（Y 是、N 否）
         , row_number() over(partition by item_code, l1_name, data_type order by update_flag desc) as rn
      from (
            select distinct (case when length(item_code) < 8 then lpad(item_code,8,'0') else item_code end) as item_code
                 , l1_name
                 , l2_name
                 , l3_name
                 , l1_coefficient
                 , l2_coefficient
                 , l3_coefficient
                 , data_type    -- 数据类型（His 历史、Add 新增）
                 , update_flag  -- 修改标识（Y 是、N 否）
              from fin_dm_opt_fop.dm_dim_fop_spart_profiting_relation_t
             where del_flag = 'N'
               and upper(status) = 'SUBMIT'  -- 状态（Save 保存、Submit 提交）
               and period_id = p_period  -- 取传入会计期的数据
           )
    ;
    
    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '传入会计期：'||p_period||'，取的标签审视 '||p_period||' 版本的数据。',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => null,
        p_log_errbuf => null  --错误编码
      ) ;
    
  end if;
  
  with spart_fop_tmp as(
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , (case when t1.industry_type = 'OTHR' then 'SCENO3' else t3.articulation_flag end) as articulation_flag  -- -- 勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三
       , t3.analysis_flag   -- 分析场景（使用或提个给知识表示时，都只用“是”的数据）
       , t1.phase_date
       , t1.unit
       , t1.source_table
       , t1.industry_type
       , t2.l2_name
       , t2.l3_name
       , t2.l1_coefficient
       , t2.l2_coefficient
       , t2.l3_coefficient
       , (case when t1.source_table = 'fin_dm_opt_fop.dm_fop_ict_pl_sum_t' then ''  -- ICT损益没有spart_code，所以不用打L2
               when t2.l2_name is null then 'New'
               when upper(t2.data_type) = 'ADD' and upper(t2.update_flag) = 'N' then 'AI'
               when upper(t2.update_flag) = 'Y' then 'Adjust'
  	           else 'Manual'
  	      end) as data_type  -- 数据类型（Manual、AI、Adjust、New）
       , t1.spart_qty       -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity        -- SNOP月计划量
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.plan_unit_quantity
    from spart_detail_l1_info_tmp t1  -- SPART明细L1标签表
    left join spart_profiting_relation_tmp t2  -- Spart与L1、L2、L3以及L1~L3系数关系维表（需要去重）
      on t1.spart_code = t2.item_code
     and t1.l1_name = t2.l1_name
     and t2.rn = 1
    left join (select distinct lv1_code, l1_name, articulation_flag, analysis_flag from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t 
                where del_flag = 'N' and upper(status) = 'SUBMIT'
              ) t3  -- 关联产业维表取“勾稽方法标签”
      on ((t1.lv1_prod_rnd_team_code = t3.lv1_code and t3.l1_name is null) or (t1.l1_name = t3.l1_name and t3.l1_name is not null))
   where t1.industry_type is not null  -- L1打完标签后，如果还有空值，需要剔除；但是 industry_type='OTHR'不能剔除
     and t1.source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- 排除SNOP的两张表
  ),
  coa_l1_tmp as(
  select distinct lv1_code, l1_name, coa_code, l2_name from fin_dm_opt_fop.apd_fop_coa_l1_t where del_flag = 'N' and status = 'Submit'
  ),
  -- 通过LV1、L1、COA关联维表，取L2值
  spart_fop_l2_tmp as(
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.spart_qty       -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity        -- SNOP月计划量
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , t1.articulation_flag
       , t1.analysis_flag
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
       , t1.l2_name
       , '' as coa_l2_name
       , t1.l3_name
       , t1.l1_coefficient
       , t1.l2_coefficient
       , t1.l3_coefficient
       , t1.data_type
    from spart_fop_tmp t1
   where nvl(t1.l1_name,'SNULL') not in(select distinct l1_name from coa_l1_tmp)  -- l1_name不在COA维表里的，还是用spart对象系数关系表的l2_name
  union all
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.spart_qty       -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity        -- SNOP月计划量
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , t1.articulation_flag
       , t1.analysis_flag
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
       , t1.l2_name
       , (case when t2.l2_name is not null then t2.l2_name
               else '其他'
          end) as coa_l2_name
       , t1.l3_name
       , t1.l1_coefficient
       , t1.l2_coefficient
       , t1.l3_coefficient
       , t1.data_type
    from spart_fop_tmp t1
    left join coa_l1_tmp t2
      on t1.lv1_prod_rnd_team_code = t2.lv1_code
     and t1.l1_name = t2.l1_name
     and t1.prod_code = t2.coa_code
   where nvl(t1.l1_name,'SNULL') in(select distinct l1_name from coa_l1_tmp) -- l1_name在COA维表里的，prod_code=coa_code 取COA维表的l1_name；否则l1_name='其他'
  ),
  -- 【SNOP的两张表】关联“L2与计委包的关系”表取L2_name、L2系数：1、根据LV1~LV4+L1关联匹配L2_name；2、如果L2_name是空值，则根据LV1~LV3+L1匹配；3、如果L2_name是空值，则根据LV1~LV2+L1匹配；
  snop_l2_tmp1 as(
  -- 1、根据LV1~LV4+L1，关联“L2与计委包的关系”表取L2_name、L2系数
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , (case when t1.industry_type = 'OTHR' then 'SCENO3' else t3.articulation_flag end) as articulation_flag  -- -- 勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三
       , t3.analysis_flag   -- 分析场景（使用或提个给知识表示时，都只用“是”的数据）
       , t1.phase_date
       , t1.unit
       , t1.source_table
       , t1.industry_type
       , t1.spart_qty       -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity        -- SNOP月计划量
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.plan_unit_quantity
    from spart_detail_l1_info_tmp t1  -- SPART明细L1标签表
    left join (select distinct lv1_code, l1_name, articulation_flag, analysis_flag from fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t 
                where del_flag = 'N' and upper(status) = 'SUBMIT'
              ) t3  -- 关联产业维表取“勾稽方法标签”
      on nvl(t1.l1_name,'SNULL') = nvl(t3.l1_name,'SNULL')
   where t1.industry_type is not null  -- L1打完标签后，如果还有空值，需要剔除；但是 industry_type='OTHR'不能剔除
     and t1.source_table in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t','fin_dm_opt_fop.dm_fop_snop_year_budget_sum_t')  -- SNOP的两张表
  ),
  snop_l2_tmp2 as(
  -- 1、根据LV1~LV3+L1，关联“L2与计委包的关系”表取L2_name、L2系数
  select distinct t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , t1.articulation_flag  -- -- 勾稽方法标签（01、场景一  02、场景二  03、场景三）；“其他产业”的勾稽方法都是场景三
       , t1.analysis_flag
       , t1.phase_date
       , t1.unit
       , t1.source_table
       , t1.industry_type
       , t2.l2_name
       , t2.l2_coefficient
       , '' as data_type  -- 数据类型（关注的是收入、发货，所以SNOP的可以给空值）
       , t1.spart_qty       -- Part物料数量
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity        -- SNOP月计划量
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.plan_unit_quantity
    from snop_l2_tmp1 t1  -- SPART明细L1标签表
    left join fin_dm_opt_fop.apd_fop_plan_com_l2_rel_t t2  -- L2与计委包的关系表
      on ((t1.plan_com_lv1 = t2.plan_com_lv1
     and t1.plan_com_lv2 = t2.plan_com_lv2
     and t1.plan_com_lv3 = t2.plan_com_lv3
     and t1.busi_lv4 = t2.busi_lv4
     and t1.l1_name = t2.l1_name)
      or (t1.plan_com_lv1 = t2.plan_com_lv1
     and t1.plan_com_lv2 = t2.plan_com_lv2
     and t1.plan_com_lv3 = t2.plan_com_lv3
     and t1.l1_name = t2.l1_name
     and t2.busi_lv4 is null)
      or (t1.plan_com_lv1 = t2.plan_com_lv1
     and t1.plan_com_lv2 = t2.plan_com_lv2
     and t1.l1_name = t2.l1_name
     and t2.plan_com_lv3 is null
     and t2.busi_lv4 is null))
     and t2.status = 'Submit'
  ),
  spart_detail_info_tmp as(
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.spart_qty
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , t1.l2_name
       , t1.coa_l2_name
       , t1.l3_name
       , (case when t1.l2_name is not null and t1.l1_coefficient is null then 0 else t1.l1_coefficient end) as l1_coefficient
       , t1.l2_coefficient
       , t1.l3_coefficient
       /*, (case when t1.articulation_flag = 'SCENO3' then ''   -- 场景三都置空（包括目标产业、其他产业）
               else t1.data_type
          end) as data_type
       */
       , t1.data_type
       , t1.articulation_flag
       , t1.analysis_flag
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
    from spart_fop_l2_tmp t1
  union all
  select t1.version_code
       , t1.period_id
       , t1.spart_code
       , t1.spart_desc
       , t1.spart_qty
       , t1.ship_qty
       , t1.snop_quantity
       , t1.snop_plan_quantity
       , t1.rmb_revenue
       , t1.usd_revenue
       , t1.rmb_cost
       , t1.usd_cost
       , t1.equip_rev_rmb_amt        -- 设备收入人民币金额
       , t1.equip_rev_usd_amt        -- 设备收入美元金额
       , t1.equip_cost_rmb_amt          -- 设备成本人民币金额
       , t1.equip_cost_usd_amt          -- 设备成本美元金额
       , t1.prod_key
       , t1.prod_code
       , t1.prod_cn_name
       , t1.bg_code
       , t1.bg_name
       , t1.bg_en_name
       , t1.lv0_prod_rnd_team_code
       , t1.lv0_prod_rd_team_cn_name
       , t1.lv0_prod_rd_team_en_name
       , t1.lv1_prod_rnd_team_code
       , t1.lv1_prod_rd_team_cn_name
       , t1.lv1_prod_rd_team_en_name
       , t1.lv2_prod_rnd_team_code
       , t1.lv2_prod_rd_team_cn_name
       , t1.lv2_prod_rd_team_en_name
       , t1.lv3_prod_rnd_team_code
       , t1.lv3_prod_rd_team_cn_name
       , t1.lv3_prod_rd_team_en_name
       , t1.plan_com_lv1
       , t1.plan_com_lv2
       , t1.plan_com_lv3
       , t1.busi_lv4
       , t1.geo_pc_key
       , t1.oversea_flag
       , t1.l1_name
       , t1.l2_name
       , '' as coa_l2_name
       , '' as l3_name
       , 0 as l1_coefficient
       , t1.l2_coefficient
       , 0 as l3_coefficient
       , t1.data_type
       , t1.articulation_flag
       , t1.analysis_flag
       , t1.phase_date
       , t1.plan_unit_quantity
       , t1.unit
       , t1.source_table
       , t1.industry_type
    from snop_l2_tmp2 t1
  )
  -- 数据入到目标表
  insert into fin_dm_opt_fop.dm_fop_spart_detail_info_t(
         version_code
       , period_id
       , spart_code
       , spart_desc
       , spart_qty
       , ship_qty
       , snop_quantity
       , snop_plan_quantity
       , rmb_revenue
       , usd_revenue
       , rmb_cost
       , usd_cost
       , equip_rev_rmb_amt        -- 设备收入人民币金额
       , equip_rev_usd_amt        -- 设备收入美元金额
       , equip_cost_rmb_amt          -- 设备成本人民币金额
       , equip_cost_usd_amt          -- 设备成本美元金额
       , prod_key
       , prod_code
       , prod_cn_name
       , bg_code
       , bg_name
       , bg_en_name
       , lv0_prod_rnd_team_code
       , lv0_prod_rd_team_cn_name
       , lv0_prod_rd_team_en_name
       , lv1_prod_rnd_team_code
       , lv1_prod_rd_team_cn_name
       , lv1_prod_rd_team_en_name
       , lv2_prod_rnd_team_code
       , lv2_prod_rd_team_cn_name
       , lv2_prod_rd_team_en_name
       , lv3_prod_rnd_team_code
       , lv3_prod_rd_team_cn_name
       , lv3_prod_rd_team_en_name
       , plan_com_lv1
       , plan_com_lv2
       , plan_com_lv3
       , busi_lv4
       , geo_pc_key
       , oversea_flag
       , l1_name
       , l2_name
       , coa_l2_name
       , l3_name
       , l1_coefficient
       , l2_coefficient
       , l3_coefficient
       , data_type
       , articulation_flag
       , analysis_flag
       , phase_date
       , plan_unit_quantity
       , unit
       , source_table
       , industry_type
       , remark
       , created_by
       , creation_date
       , last_updated_by
       , last_update_date
       , del_flag
  )
  select version_code  -- 版本编码
       , period_id                         -- 会计期
       , (case when spart_code is null then '00000000' else spart_code end) as spart_code                       -- 物料编码
       , spart_desc                       -- 物料描述
       , spart_qty                        -- Part物料数量
       , ship_qty                         -- Part发货数量
       , snop_quantity                    -- SNOP预测计划单元计划量
       , snop_plan_quantity               -- SNOP预算计划单元计划量
       , rmb_revenue                      -- Spart收入金额_人民币
       , usd_revenue                      -- Spart收入金额_美金
       , rmb_cost                         -- Bpart成本金额_人民币
       , usd_cost                         -- Bpart成本金额_美金
       , equip_rev_rmb_amt                -- 设备收入人民币金额
       , equip_rev_usd_amt                -- 设备收入美元金额
       , equip_cost_rmb_amt               -- 设备成本人民币金额
       , equip_cost_usd_amt               -- 设备成本美元金额
       , prod_key                         -- 产品KEY
       , prod_code                        -- 产品编码
       , prod_cn_name                     -- 产品名称
       , bg_code                          -- BG编码
       , bg_name                          -- BG中文名称
       , bg_en_name                       -- BG英文名称
       , lv0_prod_rnd_team_code           -- 重量级团队LV0编码
       , lv0_prod_rd_team_cn_name         -- 重量级团队LV0中文描述
       , lv0_prod_rd_team_en_name         -- 重量级团队LV0英文描述
       , lv1_prod_rnd_team_code           -- 重量级团队LV1编码
       , lv1_prod_rd_team_cn_name         -- 重量级团队LV1中文描述
       , lv1_prod_rd_team_en_name         -- 重量级团队LV1英文描述
       , lv2_prod_rnd_team_code           -- 重量级团队LV2编码
       , lv2_prod_rd_team_cn_name         -- 重量级团队LV2中文描述
       , lv2_prod_rd_team_en_name         -- 重量级团队LV2英文描述
       , lv3_prod_rnd_team_code           -- 重量级团队LV3编码
       , lv3_prod_rd_team_cn_name         -- 重量级团队LV3中文描述
       , lv3_prod_rd_team_en_name         -- 重量级团队LV3英文描述
       , plan_com_lv1                     -- 一级计委包
       , plan_com_lv2                     -- 二级计委包
       , plan_com_lv3                     -- 三级计委包
       , busi_lv4                         -- 四级业务包
       , geo_pc_key                       -- 区域责任中心KEY
       , oversea_flag                     -- 海外标志
       , l1_name                          -- L1名称
       , l2_name                          -- L2名称
       , coa_l2_name                      -- L2名称（COA）
       , l3_name                          -- L3名称
       , l1_coefficient                   -- L1系数
       , l2_coefficient                   -- L2系数
       , l3_coefficient                   -- L3系数
       , data_type                        -- 数据类型（Manual、AI、Adjust、New）
       , articulation_flag                -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
       , analysis_flag                    -- 分析场景（Y 是、N 否）
       , phase_date                       -- 期次分区字段
       , plan_unit_quantity               -- 计划单元计划量
       , unit                             -- 单位
       , source_table                     -- 来源表
       , industry_type                    -- 产业类型（TGT 目标产业、OTHR 其它产业）
       , '' as remark
       , -1::int8 as created_by
       , CURRENT_TIMESTAMP as creation_date
       , -1::int8 as last_updated_by
       , CURRENT_TIMESTAMP as last_update_date
       , 'N' as del_flag
  from spart_detail_info_tmp
  ;

  v_dml_row_count := sql%rowcount;  -- 收集数据量

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 4,
      p_log_cal_log_desc => 'dm_fop_spart_detail_info_t 目标表的数据量：'||v_dml_row_count||'，结束运行！',--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
    ) ;


exception
  	when others then

      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
        p_log_row_count => null,
        p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';

	-- 收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_detail_info_t;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

