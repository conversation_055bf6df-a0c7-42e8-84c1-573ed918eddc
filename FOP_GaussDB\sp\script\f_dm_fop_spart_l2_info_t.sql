-- ----------------------------
-- Function structure for f_dm_fop_spart_l2_info_t
-- ----------------------------
DROP FUNCTION IF EXISTS "fin_dm_opt_fop"."f_dm_fop_spart_l2_info_t"("p_version_code" varchar, OUT "x_success_flag" text);
CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_spart_l2_info_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*********************************************************************************************************************************************************************
创建时间：2024-10-22
创建人  ：朱雅欣 zwx1275798
背景描述：连接术加平台作业对象L2层级，术加解密后落中间表，再用此函数按照逻辑加工入到作业对象L2层级，全量抽取，支持删除重跑（提供给知识表示）
参数描述：参数一(p_version_code)：版本编码，参数格式：202410_V1
		  参数五(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_spart_l2_info_t() 										   
*********************************************************************************************************************************************************************/
Declare
	v_sp_name varchar(100) := 'fin_dm_opt_fop.f_dm_fop_spart_l2_info_t('||p_version_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_spart_l2_info_t';
	v_max_version_code varchar(50);  -- 来源表中最大版本
	v_dml_row_count  number default 0 ;


begin
  set enable_force_vector_engine to on;
	x_success_flag := '1';

  --写日志,开始
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '作业对象L2层级数据'||v_tbl_name||'，开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    if p_version_code is not null or p_version_code <> '' then select p_version_code into v_max_version_code;
	else 
    select substr(version_code,1,6)||'_V'||max(substr(version_code,9)::numeric) as max_version_code  into v_max_version_code
	from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t_01 
	where substr(version_code,1,6) in(select max(substr(version_code,1,6)) 
	                                    from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t_01 
										where del_flag = 'N')
	and del_flag = 'N' 
	group by substr(version_code,1,6) ;
	end if
          ; 
	
	-- 创建 bg_name_temp 临时表
  drop table if exists bg_name_temp;
	create temporary table bg_name_temp(
	    version_code  varchar(50),  -- 版本编码
	    period_id  numeric,--	会计期
			phase_date varchar(50),
			bg_code    varchar(50),--	bg编码
			bg_name    varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code       varchar(50),--	重量级团队lv1编码
			lv1_name       varchar(100),--	重量级团队lv1描述
			lv2_code       varchar(50),--	重量级团队lv2编码
			lv2_name       varchar(100),--	重量级团队lv2名称
			l1_name        varchar(100),--	l1名称
			l1_coefficient numeric(38,6),--	l1系数
			l2_name        varchar(100),--	l2名称
			equip_rev_cons_before_amt  numeric(38,10),-- 收入金额(对价前)
			equip_cost_cons_before_amt numeric(38,10),-- 成本金额(对价前)
			equip_rev_cons_after_amt   numeric(38,10),-- 设备收入金额(对价后)
			equip_cost_cons_after_amt  numeric(38,10),-- 设备成本金额(对价后)
			plan_qty  numeric(38,10),--	发货量（snop）
			ship_qty  numeric(38,10),--	发货量（历史）
			spart_qty numeric(38,10),--收入量（历史）
			currency     varchar(10),
			articulation_flag varchar(50)
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
	
	-- 创建 l1_temp 临时表
  drop table if exists l1_temp;
	create temporary table l1_temp(
	    version_code  varchar(50),  -- 版本编码
	    period_id  numeric,--	会计期
			phase_date varchar(50),
			bg_code    varchar(50),--	bg编码
			bg_name    varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			lv2_code      varchar(50),--	重量级团队lv2编码
			l1_name       varchar(500),--	l1名称
			equip_rev_cons_before_amt  numeric(38,10),-- l1对价前收入金额
			plan_qty  numeric(38,10),--	发货量（snop）
			ship_qty  numeric(38,10),--	发货量（历史）
			spart_qty  numeric(38,10),--收入量（历史）
			currency      varchar(10),--币种
			articulation_flag  varchar(50)
	)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
	
	-- 创建 ran_l1_temp 临时表
  drop table if exists ran_l1_temp;
	create temporary table ran_l1_temp(
	    version_code  varchar(50),  -- 版本编码
	    period_id  numeric,--	会计期
			phase_date varchar(50),
			bg_code    varchar(50),--	bg编码
			bg_name    varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			l1_name       varchar(500),--	l1名称
			equip_rev_cons_before_amt  numeric(38,10),-- l1对价前收入金额
			plan_qty  numeric(38,10),--	发货量（snop）
			ship_qty  numeric(38,10),--	发货量（历史）
			spart_qty  numeric(38,10),--收入量（历史）
			currency      varchar(10),--币种
			articulation_flag  varchar(50)
	)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;

	-- 创建 l2_temp 临时表
  drop table if exists l2_temp;
	create temporary table l2_temp(
	    version_code  varchar(50),  -- 版本编码
	    period_id     numeric,--	会计期
			phase_date    varchar(50),
			bg_code       varchar(50),--	bg编码
			bg_name       varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			lv1_name      varchar(200),--	重量级团队lv1描述
			lv2_code      varchar(50),--	重量级团队lv2编码
			lv2_name      varchar(200),--	重量级团队lv2名称
			l2_name       varchar(200),--	l2名称
			l1_name       varchar(200),--	l1名称
			equip_rev_cons_before_amt  numeric(38,10),-- 收入金额(对价前)
			equip_cost_cons_before_amt numeric(38,10),-- 成本金额(对价前)
			equip_rev_cons_after_amt   numeric(38,10),-- 设备收入金额(对价后)
			equip_cost_cons_after_amt  numeric(38,10),-- 设备成本金额(对价后)
			plan_qty  numeric(38,10),--	发货量（snop）
			ship_qty  numeric(38,10),--	发货量（历史）
			spart_qty numeric(38,10),--收入量（历史）
			currency  varchar(10),
			articulation_flag  varchar(50)
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
	
	-- 创建 ran_l2_temp 临时表
  drop table if exists ran_l2_temp;
	create temporary table ran_l2_temp(
	    version_code  varchar(50),  -- 版本编码
	    period_id     numeric,--	会计期
			phase_date    varchar(50),
			bg_code       varchar(50),--	bg编码
			bg_name       varchar(100),--	bg名称
			oversea_desc  varchar(50),--区域描述
			lv1_code      varchar(50),--	重量级团队lv1编码
			lv1_name      varchar(200),--	重量级团队lv1描述
			l2_name       varchar(200),--	l2名称
			l1_name       varchar(200),--	l1名称
			equip_rev_cons_before_amt  numeric(38,10),-- 收入金额(对价前)
			equip_cost_cons_before_amt numeric(38,10),-- 成本金额(对价前)
			equip_rev_cons_after_amt   numeric(38,10),-- 设备收入金额(对价后)
			equip_cost_cons_after_amt  numeric(38,10),-- 设备成本金额(对价后)
			plan_qty  numeric(38,10),--	发货量（snop）
			ship_qty  numeric(38,10),--	发货量（历史）
			spart_qty numeric(38,10),--收入量（历史）
			currency  varchar(10),
			articulation_flag  varchar(50)
  )on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
	
	-- 创建 all_temp_01 临时表
  drop table if exists all_temp_01;
	create temporary table all_temp_01(
	    version_code      varchar(50),  -- 版本编码
	    period_id					numeric,	--会计期
			phase_date        varchar(50),
			bg_code           varchar(50),	--bg编码
			bg_name           varchar(100),	--bg名称
			oversea_desc      varchar(50),	--区域
			lv1_code          varchar(50),	--重量级团队lv1编码
			lv1_name					varchar(200),	--重量级团队lv1描述
			lv2_code					varchar(50),	--重量级团队lv2编码
			lv2_name					varchar(200),	--重量级团队lv2名称
			l1_name						varchar(200),	--l1名称
			l2_name						varchar(200),	--l2名称
			currency					varchar(10),	--币种
			equip_rev_cons_before_amt		numeric(38,10),	--设备收入额(对价前)
			equip_cost_cons_before_amt	numeric(38,10),	--设备成本额(对价前)
		  plan_qty	  numeric(38,10),	--发货量（snop）
			ship_qty    numeric(38,10),	--发货量（历史）
			spart_qty   numeric(38,10),	--收入量（历史）
			unit_cost   numeric(38,10),	--单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)
			unit_price  numeric(38,10),	--单位价格 = l2对价前收入金额/l2收入数量
			rev_percent numeric(38,10),	        --收入占比 = l2对价前收入金额/l1对价前收入金额
			mgp_ratio   numeric(38,10),	          --制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
			articulation_flag  varchar(50),   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind            varchar(10)
  )on commit preserve rows distribute by /*replication --*/ hash(period_id,phase_date,l1_name)
  ;
  
  -- 创建 all_temp_02 临时表
  drop table if exists all_temp_02;
	create temporary table all_temp_02(
	    version_code      varchar(50),  -- 版本编码
	    period_id					numeric,	--会计期
			phase_date        varchar(50),
			bg_code           varchar(50),	--bg编码
			bg_name           varchar(100),	--bg名称
			oversea_desc      varchar(50),	--区域
			lv1_code          varchar(50),	--重量级团队lv1编码
			lv1_name					varchar(200),	--重量级团队lv1描述
			lv2_code					varchar(50),	--重量级团队lv2编码
			lv2_name					varchar(200),	--重量级团队lv2名称
			l1_name						varchar(200),	--l1名称
			l2_name						varchar(200),	--l2名称
			currency					varchar(10),	--币种
			equip_rev_cons_before_amt		numeric(38,10),	--设备收入额(对价前)
			equip_cost_cons_before_amt	numeric(38,10),	--设备成本额(对价前)
		  plan_qty	  numeric(38,10),	--发货量（snop）
			ship_qty    numeric(38,10),	--发货量（历史）
			spart_qty   numeric(38,10),	--收入量（历史）
			unit_cost   numeric(38,10),	--单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)
			unit_price  numeric(38,10),	--单位价格 = l2对价前收入金额/l2收入数量
			rev_percent numeric(38,10),	        --收入占比 = l2对价前收入金额/l1对价前收入金额
			mgp_ratio   numeric(38,10),	          --制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
			articulation_flag  varchar(50),   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind            varchar(10)
  )on commit preserve rows distribute by /*replication --*/ hash(period_id,phase_date,l1_name)
  ;
  
  -- 创建 all_temp 临时表
  drop table if exists all_temp;
	create temporary table all_temp(
	    version_code      varchar(50),  -- 版本编码
	    period_id					numeric,	--会计期
			phase_date        varchar(50),
			bg_code           varchar(50),	--bg编码
			bg_name           varchar(100),	--bg名称
			oversea_desc      varchar(50),	--区域
			lv1_code          varchar(50),	--重量级团队lv1编码
			lv1_name					varchar(200),	--重量级团队lv1描述
			lv2_code					varchar(50),	--重量级团队lv2编码
			lv2_name					varchar(200),	--重量级团队lv2名称
			l1_name						varchar(200),	--l1名称
			l2_name						varchar(200),	--l2名称
			currency					varchar(10),	--币种
			equip_rev_cons_before_amt		numeric(38,10),	--设备收入额(对价前)
			equip_cost_cons_before_amt	numeric(38,10),	--设备成本额(对价前)
		  plan_qty	  numeric(38,10),	--发货量（snop）
			ship_qty    numeric(38,10),	--发货量（历史）
			spart_qty   numeric(38,10),	--收入量（历史）
			unit_cost   numeric(38,10),	--单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)
			unit_price  numeric(38,10),	--单位价格 = l2对价前收入金额/l2收入数量
			rev_percent numeric(38,10),	        --收入占比 = l2对价前收入金额/l1对价前收入金额
			mgp_ratio   numeric(38,10),	          --制毛率   = 1 - l2对价前成本金额/l2对价前收入金额
			articulation_flag  varchar(50),   --勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
			del_ind            varchar(10)
  )on commit preserve rows distribute by /*replication --*/ hash(period_id,phase_date,l1_name)
  ;
  
  -- 创建 l2_all_phase_info_tmp 临时表
  drop table if exists l2_all_phase_info_tmp;
	create temporary table l2_all_phase_info_tmp(
	      version_code                   varchar(50)        -- 版本编码
	    , period_id                      numeric            -- 会计期
			, phase_date                     varchar(60)        -- 会计期次
			, bg_code                        varchar(50)        -- BG编码
			, bg_name                        varchar(200)       -- BG名称
			, oversea_desc                   varchar(20)        -- 海外标志
			, lv1_code                       varchar(50)        -- 重量级团队lv1编码
			, lv1_name                       varchar(600)       -- 重量级团队lv1名称
			, lv2_code                       varchar(50)        -- 重量级团队lv2编码
			, lv2_name                       varchar(600)       -- 重量级团队lv2名称
			, l1_name                        varchar(200)       -- l1名称
			, l2_name                        varchar(200)       -- l2名称
			, currency                       varchar(50)        -- 币种
			, articulation_flag              varchar(50)        -- 勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）
	)on commit preserve rows distribute by /*replication --*/hash(period_id,phase_date,l1_name)
	;
  
  
    insert into bg_name_temp(
	   version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , currency
     , articulation_flag
	
	)
	/*区域描述打上全球的标签*/
	with oversea_desc_temp  as (
	select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(nvl(l2_coefficient,0)*plan_qty ) as plan_qty
     , sum(nvl(l2_coefficient,0)*ship_qty ) as ship_qty
     , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty
     , currency
     , articulation_flag
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t_01
 where oversea_desc is not null    /*国内、海外需要排除空值*/
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , currency
     , articulation_flag
union all
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , '全球' as oversea_desc  /*全球=中国区+海外+其他*/
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(nvl(l2_coefficient,0)*plan_qty ) as plan_qty
     , sum(nvl(l2_coefficient,0)*ship_qty ) as ship_qty
     , sum(nvl(l2_coefficient,0)*spart_qty) as spart_qty
     , currency
     , articulation_flag
  from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t_01  /*术加解密中间表，只保留术加解密的这个版本的数据，并在函数最后清空该表*/
 where source_table not in('fin_dm_opt_fop.dm_fop_snop_forecasts_sum_t')  /*排除S&OP表的国内、海外、全球数据*/
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , currency
     , articulation_flag
	)
	/*bg名称和bg编码打上集团的标签*/
select version_code
     , period_id
     , phase_date
     , 'PROD0002' as bg_code
     , 'ICT' as bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
     , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
     , sum(plan_qty ) as plan_qty
     , sum(ship_qty ) as ship_qty
     , sum(spart_qty) as spart_qty
     , currency
     , articulation_flag
  from oversea_desc_temp
 group by version_code
     , period_id
     , phase_date
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , currency
     , articulation_flag
 union all
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l1_coefficient
     , l2_name
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , currency
     , articulation_flag
  from oversea_desc_temp
;

	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => 'bg_name_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

/*按照l2层级收敛*/
   insert into l2_temp (
        version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , lv2_code
      , lv2_name
      , l2_name
      , l1_name
      , equip_rev_cons_before_amt
      , equip_cost_cons_before_amt
      , equip_rev_cons_after_amt
      , equip_cost_cons_after_amt
      , plan_qty
      , ship_qty
      , spart_qty
      , currency
      , articulation_flag
	)
select version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , lv2_code
      , lv2_name
      , l2_name
      , l1_name
      , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
      , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
      , sum(equip_rev_cons_after_amt)   as equip_rev_cons_after_amt
      , sum(equip_cost_cons_after_amt)  as equip_cost_cons_after_amt
      , sum(plan_qty ) as plan_qty
      , sum(ship_qty ) as ship_qty
      , sum(spart_qty) as spart_qty
      , currency
      , articulation_flag
  from bg_name_temp
 group by version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , lv2_code
      , lv2_name
      , l2_name
      , l1_name
      , currency
      , articulation_flag
;

	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'l2_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

/*按照l2层级收敛*/
/*当l2_name=其他 时，对价前设备收入、设备成本为ran里面所有的 其他  ，RAN表示的是FDD\TDD\GUC\SRAN（l1）的合计  zwx1275798 202410版本新增逻辑*/
    insert into  ran_l2_temp (
	    version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , l2_name
      , l1_name
      , equip_rev_cons_before_amt
      , equip_cost_cons_before_amt    
      , currency
      , articulation_flag
	)
select version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , l2_name
      , 'RAN' as l1_name
      , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
      , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt    
      , currency
      , articulation_flag
  from bg_name_temp
 where l2_name='其他'
 and l1_name in ('GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD') 
 group by version_code
      , period_id
      , phase_date
      , bg_code
      , bg_name
      , oversea_desc
      , lv1_code
      , lv1_name
      , l2_name
      , currency
      , articulation_flag
;
	v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'ran_l2_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;


/*按照l1层级收敛*/
/*场景1对应的L1系数有2种：0或0.33333，其中0.33333的是射频模块的标识*/
    insert into   l1_temp (
	   version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv2_code
     , l1_name
     , equip_rev_cons_before_amt  /*l1对价前收入金额*/
     , plan_qty  /*发货量（snop）*/
     , ship_qty  /*发货量（历史）*/
     , spart_qty  /*收入量（历史）*/
     , currency
     , articulation_flag
	)
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv2_code
     , l1_name
     , sum(nvl(equip_rev_cons_before_amt,0)) as equip_rev_cons_before_amt  /*l1对价前收入金额*/
     , sum(case when articulation_flag = 'SCENO1' then plan_qty/3   /*S&OP的系数是从计委包取的，而计委包没有L1系数，计委包场景1的L1系数默认1/3*/
                else null
           end) as plan_qty  /*发货量（snop）*/
     , sum(case when articulation_flag = 'SCENO1' then nvl(l1_coefficient,0)*ship_qty else null end)  as ship_qty  /*发货量（历史）*/
     , sum(case when articulation_flag = 'SCENO1' then nvl(l1_coefficient,0)*spart_qty else null end) as spart_qty  /*收入量（历史）*/
     , currency
     , articulation_flag
  from bg_name_temp t1
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv2_code
     , l1_name
     , currency
     , articulation_flag
;

      v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'l1_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

/*RAN表示的是FDD\TDD\GUC\SRAN（l1）的合计  zwx1275798 202410版本新增逻辑*/
/*L1对价前收入金额为整体ran的对价前收入总和（RAN表示的是FDD\TDD\GUC\SRAN（l1）的合计）*/
insert into ran_l1_temp (
      version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , l1_name
     , equip_rev_cons_before_amt  /*整体ran的对价前收入总和*/     
     , currency
     , articulation_flag
    )
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , 'RAN' as l1_name
     , sum(nvl(equip_rev_cons_before_amt,0)) as equip_rev_cons_before_amt  /*整体ran的对价前收入总和*/     
     , currency
     , articulation_flag
  from bg_name_temp t1
  where l1_name in ('GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD') 
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , currency
     , articulation_flag
;

      v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => 'ran_l1_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



/*计算收入占比、制毛率*/
 insert into  all_temp_01(
       version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , ship_qty
     , spart_qty 
	 , rev_percent	/*收入占比 = l2对价前收入金额/l1对价前收入金额*/
	 , mgp_ratio	/*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/  
     , articulation_flag
     , del_ind   /*phase_date有值且plan_qty为0的是要剔除的*/
 )
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t2.l1_name
     , t1.l2_name
     , t1.currency
     , t1.equip_rev_cons_before_amt
     , t1.equip_cost_cons_before_amt
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name='其他' then (nvl(t2.plan_qty,0)*3) else t1.plan_qty end)   as plan_qty
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name='其他' then (nvl(t2.ship_qty,0)*3) else t1.ship_qty end)   as ship_qty
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name='其他' then (nvl(t2.spart_qty,0)*3) else t1.spart_qty end) as spart_qty 
	 , (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t1.equip_rev_cons_before_amt,0) = 0 then null
				    when nvl(t2.equip_rev_cons_before_amt,0) = 0 then -999999
				    else t1.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt 
					end) as rev_percent	/*收入占比 = l2对价前收入金额/l1对价前收入金额*/
	 , (case when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) = 0 then 0
				    when nvl ( t1.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t1.equip_cost_cons_before_amt, 0 ) <> 0 then -999999
				    else 1 - t1.equip_cost_cons_before_amt / t1.equip_rev_cons_before_amt 
					end) as mgp_ratio	/*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/  
     , t1.articulation_flag
     , (case when t1.phase_date is not null and t1.plan_qty = 0 then 'D' else '' end) as del_ind   /*phase_date有值且plan_qty为0的是要剔除的*/
  from l2_temp t1
  left join l1_temp t2
    on t1.version_code = t2.version_code
   and t1.period_id = t2.period_id
   and t1.bg_code = t2.bg_code
   and t1.oversea_desc = t2.oversea_desc
   and t1.lv1_code = t2.lv1_code
   and t1.lv2_code = t2.lv2_code
   and t1.currency = t2.currency
   and t1.l1_name = t2.l1_name
   and t1.articulation_flag = t2.articulation_flag 
   and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')   
 where t1.l2_name is not null or t1.l2_name <> ''
;


     v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => 'all_temp_01 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

/*计算收入占比、制毛率*/
     insert into  all_temp_02(
	         version_code                /* 版本编码*/  
	        ,period_id					 /*会计期*/            
			,phase_date            
			,bg_code					 /*bg编码*/
			,bg_name					 /*bg名称*/
			,oversea_desc				 /*区域*/
			,lv1_code					 /*重量级团队lv1编码*/
			,lv1_name					 /*重量级团队lv1描述*/
			,lv2_code					 /*重量级团队lv2编码*/
			,lv2_name					 /*重量级团队lv2名称*/
			,l1_name					 /*l1名称*/
			,l2_name					 /*l2名称*/
			,currency					 /*币种*/
		    ,equip_rev_cons_before_amt	 /*设备收入(对价前)*/
			,equip_cost_cons_before_amt	 /*设备成本(对价前)	*/				
			,plan_qty		             /*发货量（SNOP*/
			,ship_qty 	                 /*发货量    */
			,spart_qty 	                 /*收入量	*/		
		    ,rev_percent	             /*收入占比 = l2对价前收入金额/l1对价前收入金额*/
			,mgp_ratio	                 /*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/
			,articulation_flag           /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
			,del_ind                     /*phase_date有值且plan_qty为0的是要剔除的*/
	 )
 select     t1.version_code                 /* 版本编码*/  
	       ,t1.period_id					/*会计期*/            
			,t1.phase_date            
			,t1.bg_code						/*bg编码*/
			,t1.bg_name						/*bg名称*/
			,t1.oversea_desc					/*区域*/
			,t1.lv1_code						/*重量级团队lv1编码*/
			,t1.lv1_name						/*重量级团队lv1描述*/
			,t1.lv2_code						/*重量级团队lv2编码*/
			,t1.lv2_name						/*重量级团队lv2名称*/
			,t1.l1_name						/*l1名称*/
			,t1.l2_name						/*l2名称*/
			,t1.currency						/*币种*/
		   ,t1.equip_rev_cons_before_amt	/*设备收入(对价前)*/
			,t1.equip_cost_cons_before_amt	/*设备成本(对价前)	*/				
			,t1.plan_qty		/*发货量（SNOP*/
			,t1.ship_qty 	/*发货量    */
			,t1.spart_qty 	/*收入量	*/		
		   ,t1.rev_percent	 /*收入占比 = l2对价前收入金额/l1对价前收入金额*/
			,t1.mgp_ratio	   /*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/
			,t1.articulation_flag   /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
			,t1.del_ind             /*phase_date有值且plan_qty为0的是要剔除的*/
	   from all_temp_01 t1       		
	  where t1.l1_name not in ('GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD') 
		 or (t1.l1_name in ('GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD') 
		 and  t1.l2_name !='其他')		
union all 
 select t1.version_code                 /*版本编码*/
	    , t1.period_id                   /*--会计期*/
		, t1.phase_date                  
		, t1.bg_code                     /*bg编码*/
		, t1.bg_name                     /*bg名称*/
		, t1.oversea_desc                /*区域*/
		, t1.lv1_code                    /*重量级团队lv1编码*/
		, t1.lv1_name                    /*重量级团队lv1描述*/
		, t1.lv2_code                    /*重量级团队lv2编码*/
		, t1.lv2_name                    /*重量级团队lv2名称*/
		, t1.l1_name                     /*l1名称*/
		, t1.l2_name                     /*l2名称*/
		, t1.currency                    /*币种*/
		, t1.equip_rev_cons_before_amt   /*设备收入(对价前)*/
		, t1.equip_cost_cons_before_amt  /*设备成本(对价前)*/					
		, t1.plan_qty		             /*发货量（SNOP）*/
		, t1.ship_qty 	                 /*发货量*/
		, t1.spart_qty 	                 /*收入量*/
		, (case when nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t3.equip_rev_cons_before_amt,0) = 0 then null
		     when  nvl(t2.equip_rev_cons_before_amt,0) = 0 and nvl(t3.equip_rev_cons_before_amt,0) <> 0 then -999999
			 else t3.equip_rev_cons_before_amt / t2.equip_rev_cons_before_amt   /*t1.l2_name='其他' 收入占比 =  l2_name为其他的RAN对价前收入金额/ran的对价前收入总金额 202410版本新增逻辑 zwx1275798 */             
        end) as rev_percent	
		, (case when  nvl ( t3.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t3.equip_cost_cons_before_amt, 0 ) = 0 then 0
			      when  nvl ( t3.equip_rev_cons_before_amt, 0 ) = 0 and nvl ( t3.equip_cost_cons_before_amt, 0 ) <> 0 then -999999
			      else  1 - t3.equip_cost_cons_before_amt / t3.equip_rev_cons_before_amt /*t1.l2_name='其他' 制毛率 = ran里面所有的 其他 的(1 - l2对价前成本金额/l2对价前收入金额) 202410版本新增逻辑 zwx1275798 */
             end) as mgp_ratio	   /*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/
		, t1.articulation_flag     /*勾稽方法标签（SCENO1、场景一  SCENO2、场景二  SCENO3、场景三）*/
		, t1.del_ind               /*phase_date有值且plan_qty为0的是要剔除的*/
	   from all_temp_01 t1       
		left join ran_l1_temp t2
		on t1.version_code = t2.version_code
		and t1.period_id = t2.period_id       /*会计期*/
	    and t1.bg_code = t2.bg_code           /*bg编码*/
	    and t1.oversea_desc = t2.oversea_desc /*区域描述*/
	    and t1.lv1_code = t2.lv1_code         /*重量级团队lv1编码*/
	    and t1.currency = t2.currency         /*币种*/
	    and t1.articulation_flag = t2.articulation_flag
	    and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
		left join ran_l2_temp t3
		on t1.version_code = t3.version_code
		and t1.period_id = t3.period_id        /*会计期*/
	    and t1.bg_code = t3.bg_code            /*bg编码*/
	    and t1.oversea_desc = t3.oversea_desc  /*区域描述*/
	    and t1.lv1_code = t3.lv1_code          /*重量级团队lv1编码*/
	    and t1.currency = t3.currency          /*币种*/
	    and t1.articulation_flag = t3.articulation_flag
	    and nvl(t1.phase_date,'SNULL') = nvl(t3.phase_date,'SNULL')
		and t1.l2_name='其他'
	  where t1.l1_name in ('GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD') 
		and t1.l2_name ='其他'
	 ;

          v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => 'all_temp_02 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



/*计算单位成本、单位价格*/
    insert into all_temp(
	   version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , unit_cost	 /*单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)*/
     , unit_price	/*单位价格 = l2对价前收入金额/l2收入数量*/
	 , rev_percent	        /*收入占比 = l2对价前收入金额/l1对价前收入金额*/
     , mgp_ratio	     /*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/
     , articulation_flag
     , del_ind
	)
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , ship_qty
     , spart_qty
     , (case when nvl(equip_cost_cons_before_amt,0) = 0 then null
             when nvl(spart_qty,0) = 0 then -999999
             else equip_cost_cons_before_amt / spart_qty
        end) as unit_cost	 /*单位成本 = l2对价前成本金额/l2收入数量(其中场景1、L2为软件、其他，分母则用射频模块量*3计算)*/
     , (case when nvl(equip_rev_cons_before_amt,0) = 0 then null
             when nvl(spart_qty,0) = 0 then -999999
             else equip_rev_cons_before_amt / spart_qty
        end) as unit_price	/*单位价格 = l2对价前收入金额/l2收入数量*/
	, rev_percent	        /*收入占比 = l2对价前收入金额/l1对价前收入金额*/
     , mgp_ratio	     /*制毛率   = 1 - l2对价前成本金额/l2对价前收入金额*/
     , articulation_flag
     , del_ind
  from all_temp_02
;
    v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc => 'all_temp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

    insert into l2_all_phase_info_tmp(
	   version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , articulation_flag
       )
/*L2期次造数逻辑（知识表示需要补全所有 l2_name 的期次数据，金额、数量，赋值0；系数给空值）*/
/*取期次对应会计期的全量数据*/
  with phase_info_tmp as(
select distinct version_code, period_id, phase_date
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为 0 的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
        or nvl(equip_cost_cons_before_amt,0) <> 0
        or nvl(plan_qty,0) <> 0
        or nvl(spart_qty,0) <> 0
        or nvl(ship_qty,0) <> 0)
		   ),
/*已经存在的期次对应会计期数据*/
     l2_info_tmp as(
select distinct version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
	      or nvl(equip_cost_cons_before_amt,0) <> 0
	      or nvl(plan_qty,0) <> 0
	      or nvl(spart_qty,0) <> 0
	      or nvl(ship_qty,0) <> 0)
		   ),
      other_info_tmp as (
select distinct version_code
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
	      or nvl(equip_cost_cons_before_amt,0) <> 0
	      or nvl(plan_qty,0) <> 0
	      or nvl(spart_qty,0) <> 0
	      or nvl(ship_qty,0) <> 0)
		   ),
    l2_all_info_tmp as (
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t3.bg_code
     , t3.bg_name
     , t3.oversea_desc
     , t3.lv1_code
     , t3.lv1_name
     , t3.lv2_code
     , t3.lv2_name
     , t3.l1_name
     , t3.l2_name
     , t3.currency
     , t3.articulation_flag
  from phase_info_tmp t1
  left join other_info_tmp t3
    on 1=1
	)
/*需要造的数据入到临时表*/
select t5.version_code
     , t5.period_id
     , t5.phase_date
     , t5.bg_code
     , t5.bg_name
     , t5.oversea_desc
     , t5.lv1_code
     , t5.lv1_name
     , t5.lv2_code
     , t5.lv2_name
     , t5.l1_name
     , t5.l2_name
     , t5.currency
     , t5.articulation_flag
  from l2_all_info_tmp t5
  left join l2_info_tmp t2
    on t5.version_code       = t2.version_code
   and t5.period_id          = t2.period_id
   and nvl(t5.phase_date,'SNULL')    = nvl(t2.phase_date,'SNULL')
   and t5.bg_code            = t2.bg_code
   and t5.bg_name            = t2.bg_name
   and t5.oversea_desc       = t2.oversea_desc
   and t5.lv1_code           = t2.lv1_code
   and t5.lv1_name           = t2.lv1_name
   and t5.lv2_code           = t2.lv2_code
   and t5.lv2_name           = t2.lv2_name
   and t5.l1_name            = t2.l1_name
   and t5.l2_name            = t2.l2_name
   and t5.currency           = t2.currency
   and t5.articulation_flag  = t2.articulation_flag
 where t2.period_id is null
   and t5.phase_date is not null   /*只取期次不为空的*/
;


      v_dml_row_count := sql%rowcount;  -- 收集数据量

	  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 10,
        p_log_cal_log_desc => 'l2_all_phase_info_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

      delete fin_dm_opt_fop.dm_fop_spart_l2_info_his_t where version_code = v_max_version_code;
     insert into fin_dm_opt_fop.dm_fop_spart_l2_info_his_t(
	   version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
     , remark
     , created_by
     , creation_date
     , last_updated_by
     , last_update_date
     , del_flag	 
	 )
    with spart_l2_info_tmp as(
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
  from all_temp
 where nvl(del_ind,'SNULL') <> 'D'   /*phase_date有值且plan_qty为0的是要剔除的*/
   and (nvl(equip_rev_cons_before_amt,0) <> 0
        or nvl(equip_cost_cons_before_amt,0) <> 0
        or nvl(plan_qty,0) <> 0
        or nvl(spart_qty,0) <> 0
        or nvl(ship_qty,0) <> 0
			 )
 union all
/*l2_name缺失期次的数据入到目标表*/
select t1.version_code
     , t1.period_id
     , t1.phase_date
     , t1.bg_code
     , t1.bg_name
     , t1.oversea_desc
     , t1.lv1_code
     , t1.lv1_name
     , t1.lv2_code
     , t1.lv2_name
     , t1.l1_name
     , t1.l2_name
     , t1.currency
     , 0 as equip_rev_cons_before_amt
     , 0 as equip_cost_cons_before_amt
     , (case when t1.articulation_flag = 'SCENO1' and t1.l2_name ='其他' then (nvl(t2.plan_qty,0)*3) else 0 end) as plan_qty
     , 0 as spart_qty
     , 0 as ship_qty
     , null as unit_cost
     , null as unit_price
     , null as rev_percent
     , 0 as mgp_ratio
     , t1.articulation_flag
  from l2_all_phase_info_tmp t1
  left join l1_temp t2
    on t1.period_id = t2.period_id
   and t1.bg_code = t2.bg_code
   and t1.oversea_desc = t2.oversea_desc
   and t1.lv1_code = t2.lv1_code
   and t1.lv2_code = t2.lv2_code
   and t1.currency = t2.currency
   and t1.l1_name = t2.l1_name
   and t1.articulation_flag = t2.articulation_flag
   and nvl(t1.phase_date,'SNULL') = nvl(t2.phase_date,'SNULL')
   )
select version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , sum(equip_rev_cons_before_amt)  as equip_rev_cons_before_amt
     , sum(equip_cost_cons_before_amt) as equip_cost_cons_before_amt
     , sum(plan_qty)  as plan_qty
     , sum(spart_qty) as spart_qty
     , sum(ship_qty)  as ship_qty
     , sum(unit_cost) as unit_cost
     , sum(unit_price)  as unit_price
     , sum(rev_percent) as rev_percent
     , sum(mgp_ratio)   as mgp_ratio
     , articulation_flag
     , '' as remark
     ,  -1 as created_by
     ,  current_timestamp as creation_date
     ,  -1 as last_updated_by
     ,  current_timestamp as last_update_date
     ,  'N' as del_flag
  from spart_l2_info_tmp
 group by version_code
     , period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , articulation_flag
;


     v_dml_row_count := sql%rowcount;

  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 11,
        p_log_cal_log_desc => 'dm_fop_spart_l2_info_his_t 作业对象L2层级数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

         
          	-- 清空目标表数据
       truncate table fin_dm_opt_fop.dm_fop_spart_l2_info_t;
	   
       /*数据入到提供给知识表示的接口表 dm_fop_spart_l2_info_t*/
       insert into fin_dm_opt_fop.dm_fop_spart_l2_info_t(
       period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
     , remark
     , created_by
     , creation_date
     , last_updated_by
     , last_update_date
     , del_flag
     )
select period_id
     , phase_date
     , bg_code
     , bg_name
     , oversea_desc
     , lv1_code
     , lv1_name
     , lv2_code
     , lv2_name
     , l1_name
     , l2_name
     , currency
     , equip_rev_cons_before_amt
     , equip_cost_cons_before_amt
     , plan_qty
     , spart_qty
     , ship_qty
     , unit_cost
     , unit_price
     , rev_percent
     , mgp_ratio
     , articulation_flag
     , '' as remark
     ,  -1 as created_by
     ,  current_timestamp as creation_date
     ,  -1 as last_updated_by
     ,  current_timestamp as last_update_date
     ,  'N' as del_flag
     from fin_dm_opt_fop.dm_fop_spart_l2_info_his_t
    where version_code = v_max_version_code  -- 取最大版本数据
;
     	 	 			 
			 
	  v_dml_row_count := sql%rowcount;  -- 收集数据量

    perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 12,
        p_log_cal_log_desc => '最大版本：'||v_max_version_code||'，提供给知识表示的表 dm_fop_spart_l2_info_t 数据量：'||v_dml_row_count||'，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  
	  -- 清空 作业对象L2层级解密对接函数的中间表 数据
     truncate table fin_dm_opt_fop.dm_fop_spart_l2_info_his_t_01;
	  
	  
	  	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_spart_l2_info_his_t;
	analyse fin_dm_opt_fop.dm_fop_spart_l2_info_t;

		  --处理异常信息
	exception
		when others then
		perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_version_id => null,                 --版本
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_para_list => '',--参数
			p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
			p_log_formula_sql_txt => sqlerrm,--错误信息
			p_log_errbuf => sqlstate  --错误编码
			);
	x_success_flag := '2001';

 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100.0;

