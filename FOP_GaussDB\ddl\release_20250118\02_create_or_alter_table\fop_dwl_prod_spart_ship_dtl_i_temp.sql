-- ----------------------------
-- Table structure for fop_dwl_prod_spart_ship_dtl_i_temp
-- ----------------------------
DROP TABLE IF EXISTS "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp";
CREATE TABLE "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp" (
  "period_id" numeric,
  "scenario" varchar(13) COLLATE "pg_catalog"."default",
  "geo_pc_key" numeric,
  "prod_key" numeric,
  "material_key" numeric,
  "material_code" varchar(50) COLLATE "pg_catalog"."default",
  "spart_qty" numeric,
  "del_flag" varchar(1) COLLATE "pg_catalog"."default",
  "dw_last_update_date" timestamp(6),
  "proj_key" numeric,
  "recognise_type_id" numeric,
  "report_item_id" numeric
)
;
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."period_id" IS '会计期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."scenario" IS '业务量纲场景';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."geo_pc_key" IS '区域责任中心key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."prod_key" IS '产品key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."material_key" IS '物料key';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."material_code" IS '物料编码';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."spart_qty" IS '交易业务量:期末余额spart数量';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."del_flag" IS '逻辑删除标志';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."dw_last_update_date" IS 'dw最后更新日期';
COMMENT ON COLUMN "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp"."proj_key" IS '交付项目key';
COMMENT ON TABLE "fin_dm_opt_fop"."fop_dwl_prod_spart_ship_dtl_i_temp" IS '业财联接_ICT产业产品量纲_Spart粒度_供应中心发货时点';

